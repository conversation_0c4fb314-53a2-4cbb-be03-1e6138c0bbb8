# AFAC 环境变量配置示例
# 复制此文件为 .env 并填入实际值

# =============================================================================
# API 密钥配置
# =============================================================================

# OpenAI API 配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_ORGANIZATION=your_organization_id

# Hugging Face 配置
HUGGINGFACE_API_TOKEN=your_huggingface_token_here
HUGGINGFACE_CACHE_DIR=./data/models/huggingface

# Pinecone 配置
PINECONE_API_KEY=your_pinecone_api_key_here
PINECONE_ENVIRONMENT=your_pinecone_environment
PINECONE_INDEX_NAME=afac-vectors

# =============================================================================
# 数据库配置
# =============================================================================

# 主数据库 URL
DATABASE_URL=sqlite:///./data/afac.db
# 或使用 PostgreSQL: postgresql://user:password@localhost:5432/afac
# 或使用 MySQL: mysql://user:password@localhost:3306/afac

# Redis 配置
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=your_redis_password
REDIS_DB=0

# MongoDB 配置
MONGODB_URL=mongodb://localhost:27017/afac
MONGODB_USERNAME=your_mongodb_username
MONGODB_PASSWORD=your_mongodb_password

# =============================================================================
# 应用配置
# =============================================================================

# 应用环境
ENVIRONMENT=development  # development, staging, production
DEBUG=true
LOG_LEVEL=INFO

# API 服务配置
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4
API_RELOAD=true

# 安全配置
SECRET_KEY=your_super_secret_key_here_change_in_production
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
ALGORITHM=HS256

# CORS 配置
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
ALLOWED_HEADERS=*

# =============================================================================
# 文件存储配置
# =============================================================================

# 本地存储路径
UPLOAD_DIR=./data/uploads
PROCESSED_DIR=./data/processed
OUTPUT_DIR=./data/output
TEMP_DIR=./data/temp
LOG_DIR=./logs

# 文件大小限制 (MB)
MAX_FILE_SIZE=100
MAX_FILES_PER_REQUEST=10

# 支持的文件类型
ALLOWED_FILE_TYPES=pdf,docx,pptx,xlsx,csv,json,txt,png,jpg,jpeg,gif

# =============================================================================
# 云存储配置 (可选)
# =============================================================================

# AWS S3 配置
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-afac-bucket

# Azure Blob Storage 配置
AZURE_STORAGE_CONNECTION_STRING=your_azure_connection_string
AZURE_CONTAINER_NAME=afac-container

# Google Cloud Storage 配置
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json
GCS_BUCKET_NAME=your-gcs-bucket

# =============================================================================
# 消息队列配置
# =============================================================================

# Celery 配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=json
CELERY_TIMEZONE=UTC

# RabbitMQ 配置
RABBITMQ_URL=amqp://guest:guest@localhost:5672/
RABBITMQ_QUEUE=afac_tasks

# Kafka 配置
KAFKA_BOOTSTRAP_SERVERS=localhost:9092
KAFKA_TOPIC=afac-events
KAFKA_GROUP_ID=afac-consumer-group

# =============================================================================
# 监控和日志配置
# =============================================================================

# Prometheus 配置
PROMETHEUS_PORT=9090
PROMETHEUS_METRICS_PATH=/metrics

# Grafana 配置
GRAFANA_URL=http://localhost:3000
GRAFANA_API_KEY=your_grafana_api_key

# Sentry 错误监控
SENTRY_DSN=your_sentry_dsn_here
SENTRY_ENVIRONMENT=development
SENTRY_TRACES_SAMPLE_RATE=0.1

# Elasticsearch 日志
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_INDEX=afac-logs
ELASTICSEARCH_USERNAME=elastic
ELASTICSEARCH_PASSWORD=your_elasticsearch_password

# =============================================================================
# 外部服务配置
# =============================================================================

# 邮件服务配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_email_password
SMTP_USE_TLS=true
FROM_EMAIL=<EMAIL>

# SendGrid 配置
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>

# 短信服务配置
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# 推送通知配置
PUSHBULLET_API_KEY=your_pushbullet_api_key
FCM_SERVER_KEY=your_fcm_server_key

# =============================================================================
# 金融数据API配置
# =============================================================================

# Yahoo Finance (免费)
YFINANCE_ENABLED=true

# Alpha Vantage
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_api_key

# Quandl
QUANDL_API_KEY=your_quandl_api_key

# FRED (Federal Reserve Economic Data)
FRED_API_KEY=your_fred_api_key

# IEX Cloud
IEX_CLOUD_API_KEY=your_iex_cloud_api_key
IEX_CLOUD_SANDBOX=true

# Polygon.io
POLYGON_API_KEY=your_polygon_api_key

# =============================================================================
# 社交媒体API配置
# =============================================================================

# Twitter API v2
TWITTER_BEARER_TOKEN=your_twitter_bearer_token
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret
TWITTER_ACCESS_TOKEN=your_twitter_access_token
TWITTER_ACCESS_TOKEN_SECRET=your_twitter_access_token_secret

# Reddit API
REDDIT_CLIENT_ID=your_reddit_client_id
REDDIT_CLIENT_SECRET=your_reddit_client_secret
REDDIT_USER_AGENT=AFAC/1.0

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# Slack
SLACK_BOT_TOKEN=your_slack_bot_token
SLACK_CHANNEL=#general

# =============================================================================
# 地图和地理服务配置
# =============================================================================

# Google Maps
GOOGLE_MAPS_API_KEY=your_google_maps_api_key

# Mapbox
MAPBOX_ACCESS_TOKEN=your_mapbox_access_token

# OpenWeatherMap
OPENWEATHER_API_KEY=your_openweather_api_key

# =============================================================================
# 支付服务配置
# =============================================================================

# Stripe
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# PayPal
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=sandbox  # sandbox or live

# =============================================================================
# 区块链配置
# =============================================================================

# Ethereum
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your_infura_project_id
ETHEREUM_PRIVATE_KEY=your_ethereum_private_key
ETHEREUM_CONTRACT_ADDRESS=your_contract_address

# Bitcoin
BITCOIN_RPC_URL=http://localhost:8332
BITCOIN_RPC_USER=your_bitcoin_rpc_user
BITCOIN_RPC_PASSWORD=your_bitcoin_rpc_password

# =============================================================================
# 机器学习和AI配置
# =============================================================================

# 模型配置
DEFAULT_LLM_MODEL=gpt-3.5-turbo
DEFAULT_EMBEDDING_MODEL=all-MiniLM-L6-v2
DEFAULT_VISION_MODEL=clip-vit-base-patch32

# 模型缓存
MODEL_CACHE_DIR=./data/models
MODEL_CACHE_SIZE_GB=10

# GPU 配置
CUDA_VISIBLE_DEVICES=0
USE_GPU=true
GPU_MEMORY_FRACTION=0.8

# 推理配置
MAX_TOKENS=2048
TEMPERATURE=0.7
TOP_P=0.9
FREQUENCY_PENALTY=0.0
PRESENCE_PENALTY=0.0

# 批处理配置
BATCH_SIZE=32
MAX_CONCURRENT_REQUESTS=10
REQUEST_TIMEOUT=300

# =============================================================================
# 缓存配置
# =============================================================================

# 内存缓存
MEMORY_CACHE_SIZE_MB=512
MEMORY_CACHE_TTL=3600

# 磁盘缓存
DISK_CACHE_DIR=./data/cache
DISK_CACHE_SIZE_GB=5
DISK_CACHE_TTL=86400

# 分布式缓存
MEMCACHED_SERVERS=localhost:11211
MEMCACHED_USERNAME=your_memcached_username
MEMCACHED_PASSWORD=your_memcached_password

# =============================================================================
# 性能配置
# =============================================================================

# 并发配置
MAX_WORKERS=4
THREAD_POOL_SIZE=10
PROCESS_POOL_SIZE=4

# 连接池配置
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# HTTP 客户端配置
HTTP_TIMEOUT=30
HTTP_MAX_CONNECTIONS=100
HTTP_MAX_KEEPALIVE_CONNECTIONS=20
HTTP_KEEPALIVE_EXPIRY=5

# =============================================================================
# 开发和测试配置
# =============================================================================

# 测试数据库
TEST_DATABASE_URL=sqlite:///./data/test.db

# 测试配置
TEST_MODE=false
TEST_DATA_DIR=./tests/data
TEST_OUTPUT_DIR=./tests/output

# 开发工具
AUTO_RELOAD=true
HOT_RELOAD=true
DEBUG_TOOLBAR=true

# 代码质量
CODE_COVERAGE_THRESHOLD=80
LINT_ON_SAVE=true
FORMAT_ON_SAVE=true

# =============================================================================
# 部署配置
# =============================================================================

# Docker 配置
DOCKER_REGISTRY=your-registry.com
DOCKER_IMAGE_TAG=latest
DOCKER_NETWORK=afac-network

# Kubernetes 配置
KUBE_NAMESPACE=afac
KUBE_CONFIG_PATH=~/.kube/config
KUBE_CONTEXT=your-cluster-context

# 负载均衡
LOAD_BALANCER_URL=http://your-load-balancer.com
HEALTH_CHECK_PATH=/health
HEALTH_CHECK_INTERVAL=30

# SSL/TLS 配置
SSL_CERT_PATH=/path/to/cert.pem
SSL_KEY_PATH=/path/to/key.pem
SSL_CA_PATH=/path/to/ca.pem
SSL_VERIFY=true

# =============================================================================
# 备份和恢复配置
# =============================================================================

# 数据备份
BACKUP_DIR=./backups
BACKUP_SCHEDULE=0 2 * * *  # 每天凌晨2点
BACKUP_RETENTION_DAYS=30
BACKUP_COMPRESSION=gzip

# 远程备份
BACKUP_S3_BUCKET=your-backup-bucket
BACKUP_S3_PREFIX=afac-backups/
BACKUP_ENCRYPTION_KEY=your_backup_encryption_key

# =============================================================================
# 国际化配置
# =============================================================================

# 语言设置
DEFAULT_LANGUAGE=zh-CN
SUPPORTED_LANGUAGES=zh-CN,en-US,ja-JP
TIMEZONE=Asia/Shanghai
DATE_FORMAT=%Y-%m-%d
TIME_FORMAT=%H:%M:%S
DATETIME_FORMAT=%Y-%m-%d %H:%M:%S

# =============================================================================
# 特性开关
# =============================================================================

# 功能开关
ENABLE_VECTOR_SEARCH=true
ENABLE_OCR=true
ENABLE_SPEECH_RECOGNITION=true
ENABLE_IMAGE_ANALYSIS=true
ENABLE_SENTIMENT_ANALYSIS=true
ENABLE_TRANSLATION=true
ENABLE_SUMMARIZATION=true
ENABLE_QUESTION_ANSWERING=true
ENABLE_CODE_GENERATION=true
ENABLE_DATA_VISUALIZATION=true

# 实验性功能
ENABLE_EXPERIMENTAL_FEATURES=false
ENABLE_BETA_FEATURES=false
ENABLE_ALPHA_FEATURES=false

# =============================================================================
# 自定义配置
# =============================================================================

# 竞赛相关配置
COMPETITION_PLATFORM=tianchi  # tianchi, kaggle, datafountain
COMPETITION_ID=your_competition_id
COMPETITION_API_KEY=your_competition_api_key

# 团队配置
TEAM_NAME=your_team_name
TEAM_MEMBERS=member1,member2,member3
TEAM_LEADER=your_username

# 项目配置
PROJECT_NAME=AFAC
PROJECT_VERSION=1.0.0
PROJECT_DESCRIPTION=AI-powered Financial Analysis and Competition
PROJECT_URL=https://github.com/your-username/afac
PROJECT_AUTHOR=Your Name
PROJECT_EMAIL=<EMAIL>

# =============================================================================
# 注意事项
# =============================================================================
# 1. 请不要将包含真实密钥的 .env 文件提交到版本控制系统
# 2. 在生产环境中，请使用强密码和安全的密钥
# 3. 定期轮换API密钥和访问令牌
# 4. 使用环境变量管理工具（如 Docker Secrets、Kubernetes Secrets）
# 5. 对敏感信息进行加密存储
# 6. 限制API密钥的权限和访问范围
# 7. 监控API使用情况和异常访问
# 8. 备份重要的配置信息
# =============================================================================