# 金融研报自动生成系统环境变量配置
# Financial Research Report Generation System Environment Variables

# OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4

# 其他LLM服务配置（可选）
# ANTHROPIC_API_KEY=your_anthropic_api_key_here
# AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
# AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint_here

# 数据源配置
# 东方财富API配置（如果需要）
# EASTMONEY_API_KEY=your_eastmoney_api_key_here

# 同花顺API配置（如果需要）
# THS_API_KEY=your_ths_api_key_here

# 数据库配置
DATABASE_URL=sqlite:///financial_reports.db

# 缓存配置
CACHE_DIR=./cache
CACHE_EXPIRE_HOURS=24

# 输出配置
OUTPUT_DIR=./outputs
REPORT_FORMATS=markdown,word

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=./logs

# 系统配置
MAX_CONCURRENT_REQUESTS=5
REQUEST_TIMEOUT=30
RETRY_ATTEMPTS=3

# 图表配置
CHART_DPI=300
CHART_FORMAT=png
CHART_STYLE=seaborn

# 中文字体配置（用于图表显示）
CHINESE_FONT=SimHei

# 代理配置（如果需要）
# HTTP_PROXY=http://proxy.example.com:8080
# HTTPS_PROXY=https://proxy.example.com:8080

# 开发模式配置
DEBUG=False
DEVELOPMENT_MODE=False
