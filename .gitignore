# AFAC .gitignore 文件
# 排除不需要版本控制的文件和目录

# =============================================================================
# Python 相关
# =============================================================================

# Byte-compiled / optimized / DLL files
__pycache__/

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
#   For a library or package, you might want to ignore these files since the code is
#   intended to run in multiple environments; otherwise, check them in:
# .python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# poetry
#   Similar to Pipfile.lock, it is generally recommended to include poetry.lock in version control.
#   This is especially recommended for binary packages to ensure reproducibility, and is more
#   commonly ignored for libraries.
#   https://python-poetry.org/docs/basic-usage/#commit-your-poetrylock-file-to-version-control
#poetry.lock

# pdm
#   Similar to Pipfile.lock, it is generally recommended to include pdm.lock in version control.
#pdm.lock
#   pdm stores project-wide configurations in .pdm.toml, but it is recommended to not include it
#   in version control.
#   https://pdm.fming.dev/#use-with-ide
.pdm.toml

# PEP 582; used by e.g. github.com/David-OConnor/pyflow and github.com/pdm-project/pdm
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
#  JetBrains specific template is maintained in a separate JetBrains.gitignore that can
#  be added to the global gitignore or merged into this project gitignore.  For a PyCharm
#  project, it is recommended to include the .idea directory in version control.
.idea/

# =============================================================================
# 数据文件
# =============================================================================

# 数据目录
data/raw/
data/processed/
data/temp/
data/cache/
data/uploads/
data/output/
data/backups/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 向量数据库
data/vectordb/
data/chroma/
data/faiss/

# 模型文件
data/models/
*.bin
*.safetensors
*.ckpt
*.pth
*.pt
*.h5
*.pkl
*.pickle
*.joblib
models/*
# 大文件
*.zip
*.tar.gz
*.rar
*.7z

# =============================================================================
# 日志文件
# =============================================================================

logs/
*.log
*.log.*
*.out
*.err

# =============================================================================
# 配置文件（包含敏感信息）
# =============================================================================

# 环境变量文件
.env
.env.local
.env.development
.env.test
.env.production

# 配置文件（如果包含敏感信息）
config/local.yaml
config/production.yaml
config/secrets.yaml

# API 密钥文件
*.key
*.pem
*.crt
*.p12
*.pfx
secrets/

# =============================================================================
# IDE 和编辑器
# =============================================================================

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =============================================================================
# 操作系统
# =============================================================================

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Docker
# =============================================================================

# Docker 相关文件
.dockerignore
docker-compose.override.yml
docker-compose.local.yml

# =============================================================================
# 云服务
# =============================================================================

# AWS
.aws/

# Google Cloud
.gcloud/
gcloud-service-key.json

# Azure
.azure/

# =============================================================================
# 临时文件
# =============================================================================

# 临时目录
tmp/
temp/
.tmp/
.temp/

# 缓存文件
.cache/
cache/

# 锁文件
*.lock
.lock

# =============================================================================
# 测试相关
# =============================================================================

# 测试输出
test-results/
test-reports/
.coverage
coverage/

# 性能测试
*.prof
*.profile

# =============================================================================
# 构建和部署
# =============================================================================

# 构建输出
build/
dist/
out/

# 部署文件
deploy/
.deploy/

# Kubernetes
*.kubeconfig
kube-config

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Ansible
*.retry

# =============================================================================
# 监控和日志
# =============================================================================

# Prometheus
prometheus_data/

# Grafana
grafana_data/

# ELK Stack
elasticsearch_data/
logstash_data/
kibana_data/

# =============================================================================
# 机器学习相关
# =============================================================================

# Jupyter Notebook checkpoints
.ipynb_checkpoints/

# MLflow
mlruns/
mlartifacts/

# Weights & Biases
wandb/

# TensorBoard
runs/
tensorboard_logs/

# Model artifacts
artifacts/
experiments/

# =============================================================================
# 文档
# =============================================================================

# Sphinx
_build/
_static/
_templates/

# MkDocs
site/

# =============================================================================
# 包管理
# =============================================================================

# npm
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Yarn
.yarn/
.pnp.*

# =============================================================================
# 其他工具
# =============================================================================

# Pre-commit
.pre-commit-config.yaml.bak

# Bandit
.bandit

# Safety
.safety

# Black
.black

# isort
.isort.cfg

# =============================================================================
# 项目特定
# =============================================================================

# 竞赛数据
competition_data/
submissions/

# 用户上传文件
uploads/

# 生成的报告
reports/

# 临时脚本
scratch/
playground/

# 个人笔记
notes/
todo.md
README.local.md

# 性能分析
profile_output/

# 调试文件
debug/
*.debug

# =============================================================================
# 保留的文件（不要忽略）
# =============================================================================

# 保留示例配置文件
!config/settings.example.yaml
!.env.example

# 保留空目录的 .gitkeep 文件
!.gitkeep

# 保留文档中的图片
!docs/images/
!docs/assets/

# 保留测试数据
!tests/data/
!tests/fixtures/

# 保留 Docker 配置
!docker/
!Dockerfile
!docker-compose.yml
!docker-compose.*.yml

# 保留 CI/CD 配置
!.github/
!.gitlab-ci.yml
!.travis.yml
!.circleci/

# 保留许可证和说明文件
!LICENSE
!README.md
!CHANGELOG.md
!CONTRIBUTING.md

# =============================================================================
# 注意事项
# =============================================================================
# 1. 定期检查和更新 .gitignore 文件
# 2. 确保敏感信息不会被意外提交
# 3. 使用 git check-ignore 命令检查文件是否被忽略
# 4. 对于已经被跟踪的文件，需要先从索引中移除：git rm --cached <file>
# 5. 团队成员应该保持 .gitignore 文件的一致性
# =============================================================================

output/*