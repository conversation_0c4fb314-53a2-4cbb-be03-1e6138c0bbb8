python competition_ready.py
mkdir -p src/quality src/analysis src/visualization
powershell -Command "New-Item -ItemType Directory -Path 'src/quality', 'src/analysis', 'src/visualization' -Force"
python competition_ready.py
powershell -Command "Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::OpenRead('output/competition_final/results.zip').Entries | Select-Object Name, Length"
python run_competition.py --mode company --companies 000001
python test_enhanced_system.py
python -c "import sys; print('Python version:', sys.version); print('Path:', sys.path[:3])"
python -c "import sys; sys.path.append('.'); from src.knowledge.financial_knowledge_base import FinancialKnowledgeBase; print('导入成功')"
python test_fixed_system.py
python demo_enhanced_afac.py
python test_opensource_models.py
python demo_opensource_afac.py
python system_diagnosis.py
python test_optimized_system.py
python test_advanced_features.py
python competition_demo.py
pip install python-docx
python competition_demo.py
cd competition_output && python -c "import zipfile; z=zipfile.ZipFile('results.zip'); print('ZIP文件内容:'); [print(f'  {name}') for name in z.namelist()]"
python -c "import zipfile; z=zipfile.ZipFile('competition_output/results.zip'); print('ZIP文件内容:'); [print(f'  {name}') for name in z.namelist()]"
python enhanced_rag_analysis.py
python rag_based_competition_demo.py
python -c "import zipfile; z=zipfile.ZipFile('competition_output/results.zip'); print('ZIP文件内容:'); [print(f'   {name}') for name in z.namelist()]"
python rag_based_competition_demo.py
echo 'Terminal capability test'
