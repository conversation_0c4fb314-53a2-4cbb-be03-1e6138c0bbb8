# 金融研报自动生成系统

🤖 **基于AI大模型的智能金融研报生成平台**

[![Python Version](https://img.shields.io/badge/python-3.8%2B-blue.svg)](https://python.org) [![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE) [![OpenAI](https://img.shields.io/badge/LLM-OpenAI%20Compatible-orange.svg)](https://openai.com)

## 项目简介

本项目是一个基于AI大模型的金融研报自动生成系统，专为金融分析师、投资者和研究机构设计。通过整合多源数据采集、智能数据分析和专业报告生成功能，实现了从数据获取到研报输出的全流程自动化。

**🎯 支持AFAC竞赛**: 本系统完全兼容AFAC赛题四要求，可生成符合竞赛标准的三类研报。

## 核心功能

### 📊 多维度数据采集
- 自动获取公司财务报表、股权结构、行业信息等多源数据
- 支持A股、港股、美股市场数据
- 集成akshare、yfinance等专业数据源

### 🤖 智能财务分析
- 基于AI的财务分析、同业对比和趋势预测
- 专业的估值模型和风险评估
- 自动化的投资建议生成

### 📈 可视化图表生成
- 自动生成专业的财务图表和数据可视化
- 支持中文字体的高质量图表输出
- K线图、财务比率图、趋势分析图等

### 📝 三类专业研报
- **公司/个股研报**: 深度财务分析与估值
- **行业/子行业研报**: 全面行业分析
- **宏观经济/策略研报**: 宏观经济洞察

### 🔄 工作流引擎
- 轻量级流程编排
- 并行处理能力
- 条件分支支持
- 完整状态管理

## 🏗️ 项目结构

```
financial_research_report/
├── 📄 核心生成器
│   ├── financial_research_report_generator.py    # 主生成器
│   ├── integrated_research_report_generator.py   # 整合式生成器
│   ├── industry_workflow.py                      # 行业研究工作流
│   ├── macro_workflow.py                        # 宏观经济研究工作流
│   └── pocketflow.py                            # 轻量级工作流引擎
├── 📁 数据分析智能体
│   └── data_analysis_agent/                     # AI数据分析模块
│       ├── data_analysis_agent.py               # 主分析引擎
│       ├── prompts.py                           # 提示词模板
│       ├── config/llm_config.py                 # LLM配置
│       └── utils/                               # 工具函数
├── 📁 数据采集工具
│   └── utils/                                   # 数据获取工具集
│       ├── get_company_info.py                  # 公司信息获取
│       ├── get_financial_statements.py          # 财务报表获取
│       ├── get_shareholder_info.py              # 股东信息获取
│       └── search_info.py                       # 信息搜索
├── 📄 竞赛相关（保留）
│   ├── competition_ready.py                     # 竞赛就绪脚本
│   └── run_competition.py                       # 竞赛运行脚本
└── 📄 配置文件
    ├── requirements_financial.txt               # 金融系统依赖包
    └── .env.financial.example                  # 环境变量示例
```

## 🚀 快速开始

### 环境要求
- Python 3.8+
- OpenAI API密钥（或兼容的LLM服务）

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd AFAC
```

2. **创建虚拟环境**
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/Mac
source venv/bin/activate
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **配置环境变量**
```bash
# 复制环境变量模板并编辑
cp .env.example .env
# 编辑 .env 文件，配置API密钥等
```

5. **检查环境配置**
```bash
# 使用启动脚本检查环境
python start.py --check
```

6. **启动服务**
```bash
# 方式1: 使用启动脚本（推荐）
python start.py

# 方式2: 使用主程序
python -m src.main server

# 方式3: 指定端口启动
python start.py --port 8080
```

7. **验证安装**
```bash
# 运行系统测试
python test_system.py

# 访问API文档
# 浏览器打开: http://localhost:8000/docs
```

### 配置说明

主要配置文件位于 `config/` 目录：

- `settings.yaml` - 主配置文件
- `.env` - 环境变量配置

关键配置项：

```yaml
# AI模型配置
model:
  llm:
    provider: "openai"  # openai, huggingface, local
    model_name: "gpt-3.5-turbo"
    api_key: "${OPENAI_API_KEY}"
  
  embedding:
    provider: "sentence_transformers"
    model_name: "all-MiniLM-L6-v2"

# 向量数据库配置
vector_store:
  provider: "chromadb"  # chromadb, faiss, pinecone
  persist_directory: "./data/vectordb"

# 数据库配置
database:
  url: "sqlite:///./data/afac.db"
  
# API配置
api:
  host: "0.0.0.0"
  port: 8000
  debug: true
```

## 使用示例

### 1. 命令行使用

```bash
# 分析文档
python -m src.main analyze-document --file "path/to/document.pdf" --output "analysis.json"

# 搜索文档
python -m src.main search-documents --query "竞赛规则" --top-k 5

# 生成报告
python -m src.main generate-report --template "competition" --data "data.json" --output "report.pdf"

# 查看系统状态
python -m src.main status
```

### 2. API使用

启动API服务后，访问 `http://localhost:8000/docs` 查看完整的API文档。

**文档分析API**
```bash
curl -X POST "http://localhost:8000/api/v1/documents/analyze" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@document.pdf"
```

**语义搜索API**
```bash
curl -X POST "http://localhost:8000/api/v1/search/semantic" \
     -H "Content-Type: application/json" \
     -d '{"query": "竞赛数据分析方法", "top_k": 10}'
```

**报告生成API**
```bash
curl -X POST "http://localhost:8000/api/v1/reports/generate" \
     -H "Content-Type: application/json" \
     -d '{"template": "analysis", "data": {...}, "format": "pdf"}'
```

### 3. Python SDK使用

```python
from src.main import AFACApplication
from src.config.settings import Settings

# 初始化应用
app = AFACApplication()
app.initialize()

# 分析文档
result = await app.data_manager.process_file("document.pdf")
print(f"文档分析结果: {result}")

# 语义搜索
results = await app.vector_store.search(
    query="竞赛策略",
    top_k=5
)
print(f"搜索结果: {results}")

# 生成报告
report = await app.task_manager.submit_task(
    task_type="report_generation",
    params={
        "template": "competition_analysis",
        "data": result,
        "format": "pdf"
    }
)
print(f"报告生成完成: {report.result}")
```

## 项目结构

```
AFAC/
├── src/                    # 源代码目录
│   ├── config/            # 配置管理
│   │   └── settings.py    # 配置类定义
│   ├── utils/             # 工具函数
│   │   └── logger.py      # 日志工具
│   ├── data/              # 数据处理模块
│   │   └── data_manager.py # 数据管理器
│   ├── ai/                # AI模块
│   │   ├── model_manager.py # 模型管理器
│   │   └── vector_store.py  # 向量存储
│   ├── tasks/             # 任务管理模块
│   │   └── task_manager.py # 任务管理器
│   ├── api/               # API服务模块
│   │   └── api_server.py  # API服务器
│   └── main.py            # 主入口文件
├── config/                # 配置文件目录
│   ├── settings.yaml      # 主配置文件
│   └── logging.yaml       # 日志配置
├── data/                  # 数据目录
│   ├── raw/              # 原始数据
│   ├── processed/        # 处理后数据
│   ├── models/           # 模型文件
│   └── vectordb/         # 向量数据库
├── docs/                  # 文档目录
├── tests/                 # 测试目录
├── requirements.txt       # 依赖包列表
├── .env.example          # 环境变量示例
├── .gitignore            # Git忽略文件
└── README.md             # 项目说明
```

## 开发指南

### 代码规范

项目使用以下代码规范工具：

- **Black** - 代码格式化
- **Flake8** - 代码检查
- **MyPy** - 类型检查
- **isort** - 导入排序

运行代码检查：
```bash
# 格式化代码
black src/

# 检查代码风格
flake8 src/

# 类型检查
mypy src/

# 排序导入
isort src/
```

### 测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_data_manager.py

# 生成覆盖率报告
pytest --cov=src --cov-report=html
```

### 添加新功能

1. **创建功能分支**
```bash
git checkout -b feature/new-feature
```

2. **实现功能**
   - 在相应模块中添加代码
   - 编写单元测试
   - 更新文档

3. **提交代码**
```bash
git add .
git commit -m "feat: add new feature"
git push origin feature/new-feature
```

## 部署指南

### Docker部署

1. **构建镜像**
```bash
docker build -t afac:latest .
```

2. **运行容器**
```bash
docker run -d \
  --name afac \
  -p 8000:8000 \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/config:/app/config \
  --env-file .env \
  afac:latest
```

### 生产环境部署

1. **使用Gunicorn**
```bash
gunicorn src.api.api_server:app \
  --workers 4 \
  --worker-class uvicorn.workers.UvicornWorker \
  --bind 0.0.0.0:8000
```

2. **使用Nginx反向代理**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 常见问题

### Q: 如何配置OpenAI API密钥？
A: 在 `.env` 文件中设置 `OPENAI_API_KEY=your_api_key`

### Q: 向量数据库初始化失败怎么办？
A: 检查 `data/vectordb/` 目录权限，确保应用有读写权限

### Q: 模型加载速度慢怎么优化？
A: 可以使用模型缓存，或者切换到更小的模型

### Q: 如何添加自定义数据源？
A: 继承 `DataProcessor` 类，实现自定义的数据处理逻辑

### Q: 支持哪些文件格式？
A: 支持 PDF、DOCX、PPTX、XLSX、CSV、JSON、TXT、图片等格式

## 贡献指南

我们欢迎社区贡献！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 联系方式

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 邮箱: [<EMAIL>]

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 基础AI模型集成
- 文档处理功能
- API服务接口
- 向量存储和检索
- 任务管理系统

---

**感谢使用 AFAC！如果这个项目对您有帮助，请给我们一个 ⭐**