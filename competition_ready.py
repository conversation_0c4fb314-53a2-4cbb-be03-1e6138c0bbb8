#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AFAC赛题四竞赛就绪脚本
智能体赋能的金融多模态报告自动化生成系统
一键生成符合竞赛要求的三份Word文档
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime
import json
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from src.agents.competition_system import CompetitionSystem, CompetitionConfig
    from src.reports.competition_document_generator import CompetitionDocumentGenerator
    from src.quality.quality_control_system import QualityControlSystem
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保所有依赖已正确安装")
    sys.exit(1)


def setup_logging():
    """设置日志"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / f"competition_ready_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )


async def main():
    """主函数 - 竞赛就绪系统"""
    try:
        print("🏆 AFAC赛题四 - 智能体赋能的金融多模态报告自动化生成系统")
        print("🎯 竞赛就绪版本 - 一键生成提交文档")
        print("=" * 80)
        
        # 竞赛配置
        config = CompetitionConfig(
            target_companies=['000001'],  # 平安银行
            target_industries=['银行业'],   # 银行业
            macro_indicators=['GDP', 'CPI', '利率', '汇率', 'PMI'],
            output_dir='output/competition_final',
            use_opensource_only=True,
            generate_charts=True,
            quality_threshold=0.8
        )
        
        print(f"📊 竞赛配置:")
        print(f"   目标公司: {', '.join(config.target_companies)}")
        print(f"   目标行业: {', '.join(config.target_industries)}")
        print(f"   宏观指标: {', '.join(config.macro_indicators)}")
        print(f"   输出目录: {config.output_dir}")
        print("")
        
        # 初始化系统组件
        print("🚀 初始化竞赛系统组件...")
        competition_system = CompetitionSystem(config)
        document_generator = CompetitionDocumentGenerator(config.output_dir)
        quality_control = QualityControlSystem()
        print("✅ 系统组件初始化完成")
        print("")
        
        # 执行统计
        start_time = datetime.now()
        results = {
            'company_reports': [],
            'industry_reports': [],
            'macro_reports': [],
            'generated_documents': {},
            'quality_scores': {},
            'submission_package': ''
        }
        
        # 第一步：生成公司研报
        print("📈 第一步：生成公司研报")
        print("-" * 50)
        
        symbol = config.target_companies[0]
        print(f"🎯 分析目标公司: {symbol} (平安银行)")
        
        company_report = await competition_system.generate_company_report(symbol)
        
        if company_report:
            results['company_reports'].append(company_report)
            
            # 质量检查
            quality_result = quality_control.audit_report(company_report, 'company')
            results['quality_scores']['company'] = quality_result.get('overall_score', 0)
            
            print(f"   ✅ 公司研报生成成功")
            print(f"   📊 包含章节: {len(company_report.get('sections', {}))}")
            print(f"   📈 包含图表: {len(company_report.get('charts', []))}")
            print(f"   ⭐ 投资评级: {company_report.get('investment_rating', 'N/A')}")
            print(f"   🎯 质量评分: {quality_result.get('overall_score', 0):.1f}/100")
        else:
            print("   ❌ 公司研报生成失败")
        
        print("")
        
        # 第二步：生成行业研报
        print("🏭 第二步：生成行业研报")
        print("-" * 50)
        
        industry = config.target_industries[0]
        print(f"🎯 分析目标行业: {industry}")
        
        industry_report = await competition_system.generate_industry_report(industry)
        
        if industry_report:
            results['industry_reports'].append(industry_report)
            
            # 质量检查
            quality_result = quality_control.audit_report(industry_report, 'industry')
            results['quality_scores']['industry'] = quality_result.get('overall_score', 0)
            
            print(f"   ✅ 行业研报生成成功")
            print(f"   📊 包含章节: {len(industry_report.get('sections', {}))}")
            print(f"   📈 包含图表: {len(industry_report.get('charts', []))}")
            print(f"   ⭐ 投资评级: {industry_report.get('investment_rating', 'N/A')}")
            print(f"   🎯 质量评分: {quality_result.get('overall_score', 0):.1f}/100")
        else:
            print("   ❌ 行业研报生成失败")
        
        print("")
        
        # 第三步：生成宏观研报
        print("🌍 第三步：生成宏观经济研报")
        print("-" * 50)
        
        print(f"🎯 分析宏观指标: {', '.join(config.macro_indicators)}")
        
        macro_report = await competition_system.generate_macro_report()
        
        if macro_report:
            results['macro_reports'].append(macro_report)
            
            # 质量检查
            quality_result = quality_control.audit_report(macro_report, 'macro')
            results['quality_scores']['macro'] = quality_result.get('overall_score', 0)
            
            print(f"   ✅ 宏观研报生成成功")
            print(f"   📊 包含章节: {len(macro_report.get('sections', {}))}")
            print(f"   📈 包含图表: {len(macro_report.get('charts', []))}")
            print(f"   📋 政策建议: {macro_report.get('policy_recommendation', 'N/A')}")
            print(f"   🎯 质量评分: {quality_result.get('overall_score', 0):.1f}/100")
        else:
            print("   ❌ 宏观研报生成失败")
        
        print("")
        
        # 第四步：生成竞赛文档
        print("📄 第四步：生成竞赛提交文档")
        print("-" * 50)
        
        print("🔄 转换为Word文档格式...")
        
        generated_docs = {}

        # 生成公司文档
        if results['company_reports']:
            company_doc = document_generator.generate_company_document(
                results['company_reports'][0], config.target_companies[0]
            )
            if company_doc:
                generated_docs['company'] = company_doc

        # 生成行业文档
        if results['industry_reports']:
            industry_doc = document_generator.generate_industry_document(
                results['industry_reports'][0], config.target_industries[0]
            )
            if industry_doc:
                generated_docs['industry'] = industry_doc

        # 生成宏观文档
        if results['macro_reports']:
            macro_doc = document_generator.generate_macro_document(results['macro_reports'][0])
            if macro_doc:
                generated_docs['macro'] = macro_doc

        results['generated_documents'] = generated_docs

        if generated_docs:
            print("✅ Word文档生成成功:")
            for doc_type, file_path in generated_docs.items():
                filename = Path(file_path).name
                print(f"   📄 {filename}")

            # 创建提交包
            print("📦 创建提交包...")
            submission_package = document_generator.create_submission_package(list(generated_docs.values()))
            results['submission_package'] = submission_package
            
            if submission_package:
                print(f"   ✅ 提交包已创建: {Path(submission_package).name}")
        else:
            print("   ❌ Word文档生成失败")
        
        print("")
        
        # 第五步：最终验证
        print("🔍 第五步：最终验证")
        print("-" * 50)
        
        # 验证文件存在性
        required_files = [
            "Company_Research_Report.docx",
            "Industry_Research_Report.docx",
            "Macro_Research_Report.docx"
        ]
        
        output_dir = Path(config.output_dir)
        verification_results = {}
        
        for filename in required_files:
            file_path = output_dir / filename
            exists = file_path.exists()
            verification_results[filename] = exists
            
            if exists:
                file_size = file_path.stat().st_size / 1024  # KB
                print(f"   ✅ {filename} ({file_size:.1f} KB)")
            else:
                print(f"   ❌ {filename} (未找到)")
        
        # 检查提交包
        if results['submission_package'] and Path(results['submission_package']).exists():
            zip_size = Path(results['submission_package']).stat().st_size / 1024  # KB
            print(f"   ✅ results.zip ({zip_size:.1f} KB)")
        else:
            print(f"   ❌ results.zip (未找到)")
        
        print("")
        
        # 执行总结
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        
        print("🎉 竞赛系统执行完成!")
        print("=" * 80)
        
        print(f"📊 执行统计:")
        print(f"   总执行时间: {execution_time:.1f} 秒")
        print(f"   生成报告数: {len(results['company_reports']) + len(results['industry_reports']) + len(results['macro_reports'])}")
        print(f"   Word文档数: {len(results['generated_documents'])}")
        
        # 质量评分
        quality_scores = list(results['quality_scores'].values())
        if quality_scores:
            avg_quality = sum(quality_scores) / len(quality_scores)
            print(f"   平均质量: {avg_quality:.1f}/100")
        
        print("")
        
        print(f"🎯 竞赛提交清单:")
        all_files_ready = True
        for filename in required_files:
            if verification_results.get(filename, False):
                print(f"   ✅ {filename}")
            else:
                print(f"   ❌ {filename}")
                all_files_ready = False
        
        if results['submission_package'] and Path(results['submission_package']).exists():
            print(f"   ✅ results.zip")
        else:
            print(f"   ❌ results.zip")
            all_files_ready = False
        
        print("")
        
        if all_files_ready:
            print("🌟 恭喜！所有竞赛文档已准备就绪！")
            print("🎯 可以直接提交 results.zip 文件参加竞赛！")
            print("")
            print("📋 提交说明:")
            print("   1. 解压 results.zip 文件")
            print("   2. 检查三份Word文档内容")
            print("   3. 确认图表和数据完整性")
            print("   4. 提交到竞赛平台")
        else:
            print("⚠️  部分文档生成失败，请检查系统配置")
        
        # 保存执行结果
        results_file = output_dir / "execution_summary.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'execution_time': execution_time,
                'quality_scores': results['quality_scores'],
                'generated_documents': list(results['generated_documents'].keys()),
                'verification_results': verification_results,
                'submission_ready': all_files_ready,
                'timestamp': datetime.now().isoformat()
            }, f, ensure_ascii=False, indent=2)
        
        return all_files_ready
        
    except Exception as e:
        print(f"\n❌ 竞赛系统执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    try:
        # 设置日志
        setup_logging()
        
        print("🚀 启动AFAC赛题四竞赛就绪系统...")
        print("")
        
        # 运行系统
        success = asyncio.run(main())
        
        if success:
            print(f"\n🎊 竞赛系统执行成功！文档已准备就绪！")
            exit_code = 0
        else:
            print(f"\n⚠️  竞赛系统执行完成，但存在问题")
            exit_code = 1
            
    except KeyboardInterrupt:
        print("\n⏹️  执行被用户中断")
        exit_code = 1
    except Exception as e:
        print(f"\n❌ 系统运行失败: {e}")
        exit_code = 1
    
    sys.exit(exit_code)
