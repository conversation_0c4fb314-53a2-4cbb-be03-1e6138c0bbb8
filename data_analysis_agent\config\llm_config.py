#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM配置管理
"""

import os
from typing import Optional
from pydantic import BaseModel
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class LLMConfig(BaseModel):
    """LLM配置类"""
    
    # OpenAI配置
    api_key: str = os.getenv("OPENAI_API_KEY", "")
    base_url: str = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
    model: str = os.getenv("OPENAI_MODEL", "gpt-4")
    
    # 请求配置
    max_tokens: int = 4000
    temperature: float = 0.1
    timeout: int = 30
    max_retries: int = 3
    
    # 其他配置
    organization: Optional[str] = os.getenv("OPENAI_ORGANIZATION")
    
    class Config:
        env_prefix = "OPENAI_"
    
    def validate_config(self) -> bool:
        """验证配置是否有效"""
        if not self.api_key:
            raise ValueError("OPENAI_API_KEY is required")
        
        if not self.base_url:
            raise ValueError("OPENAI_BASE_URL is required")
        
        if not self.model:
            raise ValueError("OPENAI_MODEL is required")
        
        return True
    
    @classmethod
    def from_env(cls) -> "LLMConfig":
        """从环境变量创建配置"""
        config = cls()
        config.validate_config()
        return config
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "api_key": self.api_key,
            "base_url": self.base_url,
            "model": self.model,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "timeout": self.timeout,
            "max_retries": self.max_retries,
            "organization": self.organization
        }
