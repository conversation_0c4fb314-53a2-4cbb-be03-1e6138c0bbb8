#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分析智能体
基于AI的财务数据分析和报告生成
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from pathlib import Path
import json
import logging
from typing import Dict, List, Any, Optional

try:
    from .config.llm_config import LLMConfig
    from .utils.llm_helper import LLMHelper
    from .utils.code_executor import CodeExecutor
    from .utils.create_session_dir import create_session_dir
    from .prompts import ANALYSIS_PROMPTS
except ImportError:
    # 简化版本，用于快速启动
    class LLMConfig:
        def __init__(self):
            self.model = "gpt-4"
            self.api_key = "your-api-key"
    
    class LLMHelper:
        def __init__(self, config):
            self.config = config
        
        async def generate_response(self, prompt):
            return "分析结果：基于提供的数据进行了综合分析..."
    
    class CodeExecutor:
        def execute(self, code):
            return {"success": True, "output": "代码执行成功"}
    
    def create_session_dir():
        return Path("outputs") / f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    ANALYSIS_PROMPTS = {
        "financial_analysis": "请对财务数据进行分析...",
        "chart_generation": "请生成相应的图表...",
        "report_generation": "请生成分析报告..."
    }


class DataAnalysisAgent:
    """数据分析智能体"""
    
    def __init__(self, config=None):
        self.config = config or LLMConfig()
        self.llm_helper = LLMHelper(self.config)
        self.code_executor = CodeExecutor()
        self.logger = logging.getLogger("DataAnalysisAgent")
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 创建输出目录
        self.session_dir = create_session_dir()
        self.session_dir.mkdir(parents=True, exist_ok=True)
    
    async def analyze(self, prompt: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        执行数据分析
        
        Args:
            prompt: 分析需求描述
            data: 待分析的数据
        
        Returns:
            分析结果字典
        """
        try:
            self.logger.info("开始执行数据分析...")
            
            # 构建分析提示词
            analysis_prompt = self._build_analysis_prompt(prompt, data)
            
            # 调用LLM进行分析
            analysis_result = await self.llm_helper.generate_response(analysis_prompt)
            
            # 执行生成的代码（如果有）
            if self._contains_code(analysis_result):
                code_result = self._execute_analysis_code(analysis_result)
                analysis_result = self._merge_results(analysis_result, code_result)
            
            # 生成图表
            charts = await self._generate_charts(data)
            
            result = {
                'analysis': analysis_result,
                'charts': charts,
                'session_dir': str(self.session_dir),
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info("数据分析完成")
            return result
            
        except Exception as e:
            self.logger.error(f"数据分析失败: {e}")
            return {'error': str(e)}
    
    async def generate_report(self, prompt: str, analysis_data: Dict[str, Any] = None) -> str:
        """
        生成分析报告
        
        Args:
            prompt: 报告生成需求
            analysis_data: 分析数据
        
        Returns:
            生成的报告内容
        """
        try:
            self.logger.info("开始生成分析报告...")
            
            # 构建报告生成提示词
            report_prompt = self._build_report_prompt(prompt, analysis_data)
            
            # 调用LLM生成报告
            report_content = await self.llm_helper.generate_response(report_prompt)
            
            # 保存报告
            report_file = self.session_dir / "analysis_report.md"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            self.logger.info(f"分析报告已保存: {report_file}")
            return report_content
            
        except Exception as e:
            self.logger.error(f"生成分析报告失败: {e}")
            return f"报告生成失败: {e}"
    
    def _build_analysis_prompt(self, prompt: str, data: Dict[str, Any] = None) -> str:
        """构建分析提示词"""
        base_prompt = ANALYSIS_PROMPTS.get("financial_analysis", "")
        
        data_info = ""
        if data:
            data_info = f"\n数据信息：\n{json.dumps(data, ensure_ascii=False, indent=2, default=str)}"
        
        return f"""
{base_prompt}

用户需求：{prompt}
{data_info}

请进行详细的财务分析，包括：
1. 数据概览和质量评估
2. 关键财务指标计算
3. 趋势分析和同比分析
4. 风险评估
5. 投资建议

如果需要生成图表，请提供相应的Python代码。
        """.strip()
    
    def _build_report_prompt(self, prompt: str, analysis_data: Dict[str, Any] = None) -> str:
        """构建报告生成提示词"""
        base_prompt = ANALYSIS_PROMPTS.get("report_generation", "")
        
        analysis_info = ""
        if analysis_data:
            analysis_info = f"\n分析结果：\n{json.dumps(analysis_data, ensure_ascii=False, indent=2, default=str)}"
        
        return f"""
{base_prompt}

报告需求：{prompt}
{analysis_info}

请生成一份专业的金融分析报告，包含：
1. 执行摘要
2. 详细分析
3. 图表说明
4. 结论和建议
5. 风险提示

报告应该结构清晰，语言专业，适合投资者阅读。
        """.strip()
    
    def _contains_code(self, text: str) -> bool:
        """检查文本是否包含代码"""
        code_indicators = ['```python', 'import ', 'plt.', 'pd.', 'np.']
        return any(indicator in text for indicator in code_indicators)
    
    def _execute_analysis_code(self, analysis_result: str) -> Dict[str, Any]:
        """执行分析代码"""
        try:
            # 提取代码块
            code_blocks = self._extract_code_blocks(analysis_result)
            
            results = []
            for code in code_blocks:
                result = self.code_executor.execute(code)
                results.append(result)
            
            return {'code_results': results}
            
        except Exception as e:
            self.logger.error(f"执行分析代码失败: {e}")
            return {'code_error': str(e)}
    
    def _extract_code_blocks(self, text: str) -> List[str]:
        """提取代码块"""
        import re
        
        # 匹配Python代码块
        pattern = r'```python\n(.*?)\n```'
        matches = re.findall(pattern, text, re.DOTALL)
        
        return matches
    
    def _merge_results(self, analysis_result: str, code_result: Dict[str, Any]) -> str:
        """合并分析结果和代码执行结果"""
        merged = analysis_result
        
        if 'code_results' in code_result:
            merged += "\n\n## 代码执行结果\n"
            for i, result in enumerate(code_result['code_results']):
                merged += f"\n### 代码块 {i+1}\n"
                merged += f"执行状态: {'成功' if result.get('success') else '失败'}\n"
                if 'output' in result:
                    merged += f"输出: {result['output']}\n"
        
        return merged
    
    async def _generate_charts(self, data: Dict[str, Any] = None) -> List[Dict[str, str]]:
        """生成图表"""
        try:
            charts = []
            
            if not data:
                return charts
            
            # 生成基础图表
            if 'financial_statements' in data:
                financial_charts = self._generate_financial_charts(data['financial_statements'])
                charts.extend(financial_charts)
            
            return charts
            
        except Exception as e:
            self.logger.error(f"生成图表失败: {e}")
            return []
    
    def _generate_financial_charts(self, financial_data: Dict[str, Any]) -> List[Dict[str, str]]:
        """生成财务图表"""
        charts = []
        
        try:
            # 营收趋势图
            if 'income_statement' in financial_data:
                revenue_chart = self._create_revenue_trend_chart(financial_data['income_statement'])
                if revenue_chart:
                    charts.append(revenue_chart)
            
            # 利润趋势图
            if 'income_statement' in financial_data:
                profit_chart = self._create_profit_trend_chart(financial_data['income_statement'])
                if profit_chart:
                    charts.append(profit_chart)
            
            # 资产负债图
            if 'balance_sheet' in financial_data:
                balance_chart = self._create_balance_sheet_chart(financial_data['balance_sheet'])
                if balance_chart:
                    charts.append(balance_chart)
            
        except Exception as e:
            self.logger.error(f"生成财务图表失败: {e}")
        
        return charts
    
    def _create_revenue_trend_chart(self, income_data) -> Dict[str, str]:
        """创建营收趋势图"""
        try:
            plt.figure(figsize=(12, 6))
            
            # 模拟数据（实际应用中应使用真实数据）
            periods = ['2021Q1', '2021Q2', '2021Q3', '2021Q4', '2022Q1', '2022Q2', '2022Q3', '2022Q4']
            revenues = [100, 110, 120, 130, 125, 135, 145, 150]
            
            plt.plot(periods, revenues, marker='o', linewidth=2, markersize=8)
            plt.title('营业收入趋势', fontsize=16, fontweight='bold')
            plt.xlabel('报告期', fontsize=12)
            plt.ylabel('营业收入 (亿元)', fontsize=12)
            plt.grid(True, alpha=0.3)
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            # 保存图表
            chart_file = self.session_dir / "营业收入趋势.png"
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            return {
                'title': '营业收入趋势',
                'file_path': str(chart_file),
                'description': '公司营业收入的历史趋势变化'
            }
            
        except Exception as e:
            self.logger.error(f"创建营收趋势图失败: {e}")
            return {}
    
    def _create_profit_trend_chart(self, income_data) -> Dict[str, str]:
        """创建利润趋势图"""
        try:
            plt.figure(figsize=(12, 6))
            
            # 模拟数据
            periods = ['2021Q1', '2021Q2', '2021Q3', '2021Q4', '2022Q1', '2022Q2', '2022Q3', '2022Q4']
            net_profits = [10, 12, 15, 18, 16, 19, 22, 25]
            
            plt.plot(periods, net_profits, marker='s', linewidth=2, markersize=8, color='green')
            plt.title('净利润趋势', fontsize=16, fontweight='bold')
            plt.xlabel('报告期', fontsize=12)
            plt.ylabel('净利润 (亿元)', fontsize=12)
            plt.grid(True, alpha=0.3)
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            # 保存图表
            chart_file = self.session_dir / "净利润趋势.png"
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            return {
                'title': '净利润趋势',
                'file_path': str(chart_file),
                'description': '公司净利润的历史趋势变化'
            }
            
        except Exception as e:
            self.logger.error(f"创建利润趋势图失败: {e}")
            return {}
    
    def _create_balance_sheet_chart(self, balance_data) -> Dict[str, str]:
        """创建资产负债图"""
        try:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
            
            # 资产结构饼图
            assets = ['流动资产', '固定资产', '无形资产', '其他资产']
            asset_values = [40, 35, 15, 10]
            
            ax1.pie(asset_values, labels=assets, autopct='%1.1f%%', startangle=90)
            ax1.set_title('资产结构', fontsize=14, fontweight='bold')
            
            # 负债权益结构饼图
            liabilities = ['流动负债', '长期负债', '股东权益']
            liability_values = [30, 20, 50]
            
            ax2.pie(liability_values, labels=liabilities, autopct='%1.1f%%', startangle=90)
            ax2.set_title('负债权益结构', fontsize=14, fontweight='bold')
            
            plt.tight_layout()
            
            # 保存图表
            chart_file = self.session_dir / "资产与权益趋势.png"
            plt.savefig(chart_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            return {
                'title': '资产与权益结构',
                'file_path': str(chart_file),
                'description': '公司资产和负债权益的结构分析'
            }
            
        except Exception as e:
            self.logger.error(f"创建资产负债图失败: {e}")
            return {}
