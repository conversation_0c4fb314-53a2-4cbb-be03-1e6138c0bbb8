#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
代码执行器
安全执行Python代码
"""

import sys
import io
import contextlib
import logging
from typing import Dict, Any
import traceback

logger = logging.getLogger(__name__)


class CodeExecutor:
    """代码执行器"""
    
    def __init__(self):
        self.allowed_modules = {
            'pandas', 'numpy', 'matplotlib', 'seaborn', 'plotly',
            'scipy', 'sklearn', 'statsmodels', 'math', 'datetime',
            'json', 're', 'os', 'pathlib'
        }
        
        self.forbidden_functions = {
            'exec', 'eval', 'compile', '__import__', 'open',
            'input', 'raw_input', 'file', 'execfile'
        }
    
    def execute(self, code: str, timeout: int = 30) -> Dict[str, Any]:
        """
        执行Python代码
        
        Args:
            code: 要执行的Python代码
            timeout: 超时时间（秒）
        
        Returns:
            执行结果字典
        """
        try:
            # 安全检查
            if not self._is_safe_code(code):
                return {
                    'success': False,
                    'error': '代码包含不安全的操作',
                    'output': ''
                }
            
            # 准备执行环境
            namespace = self._prepare_namespace()
            
            # 捕获输出
            output_buffer = io.StringIO()
            error_buffer = io.StringIO()
            
            try:
                with contextlib.redirect_stdout(output_buffer), \
                     contextlib.redirect_stderr(error_buffer):
                    
                    # 执行代码
                    exec(code, namespace)
                
                # 获取输出
                stdout = output_buffer.getvalue()
                stderr = error_buffer.getvalue()
                
                result = {
                    'success': True,
                    'output': stdout,
                    'error': stderr if stderr else None,
                    'namespace': self._extract_results(namespace)
                }
                
                logger.info("代码执行成功")
                return result
                
            except Exception as e:
                error_msg = f"{type(e).__name__}: {str(e)}"
                traceback_str = traceback.format_exc()
                
                result = {
                    'success': False,
                    'error': error_msg,
                    'traceback': traceback_str,
                    'output': output_buffer.getvalue()
                }
                
                logger.error(f"代码执行失败: {error_msg}")
                return result
                
        except Exception as e:
            logger.error(f"代码执行器错误: {e}")
            return {
                'success': False,
                'error': f"执行器错误: {str(e)}",
                'output': ''
            }
    
    def _is_safe_code(self, code: str) -> bool:
        """检查代码安全性"""
        try:
            # 检查禁用函数
            for func in self.forbidden_functions:
                if func in code:
                    logger.warning(f"代码包含禁用函数: {func}")
                    return False
            
            # 检查危险操作
            dangerous_patterns = [
                'import os', 'import sys', 'import subprocess',
                'import socket', 'import urllib', 'import requests',
                '__builtins__', '__globals__', '__locals__',
                'file(', 'open(', 'input(', 'raw_input('
            ]
            
            for pattern in dangerous_patterns:
                if pattern in code:
                    logger.warning(f"代码包含危险模式: {pattern}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"安全检查失败: {e}")
            return False
    
    def _prepare_namespace(self) -> Dict[str, Any]:
        """准备执行命名空间"""
        try:
            import pandas as pd
            import numpy as np
            import matplotlib.pyplot as plt
            import seaborn as sns
            import json
            import math
            from datetime import datetime, timedelta
            from pathlib import Path
            
            # 设置matplotlib中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            
            namespace = {
                'pd': pd,
                'np': np,
                'plt': plt,
                'sns': sns,
                'json': json,
                'math': math,
                'datetime': datetime,
                'timedelta': timedelta,
                'Path': Path,
                # 添加一些常用函数
                'print': print,
                'len': len,
                'range': range,
                'enumerate': enumerate,
                'zip': zip,
                'sum': sum,
                'max': max,
                'min': min,
                'abs': abs,
                'round': round,
                'sorted': sorted,
                'list': list,
                'dict': dict,
                'tuple': tuple,
                'set': set,
                'str': str,
                'int': int,
                'float': float,
                'bool': bool
            }
            
            return namespace
            
        except Exception as e:
            logger.error(f"准备命名空间失败: {e}")
            return {}
    
    def _extract_results(self, namespace: Dict[str, Any]) -> Dict[str, Any]:
        """提取执行结果"""
        try:
            results = {}
            
            # 提取变量（排除内置和模块）
            for key, value in namespace.items():
                if not key.startswith('_') and not callable(value):
                    try:
                        # 尝试序列化值
                        if isinstance(value, (int, float, str, bool, list, dict)):
                            results[key] = value
                        elif hasattr(value, 'to_dict'):
                            results[key] = value.to_dict()
                        elif hasattr(value, 'tolist'):
                            results[key] = value.tolist()
                        else:
                            results[key] = str(value)
                    except Exception:
                        results[key] = f"<{type(value).__name__} object>"
            
            return results
            
        except Exception as e:
            logger.error(f"提取结果失败: {e}")
            return {}
    
    def validate_code_syntax(self, code: str) -> Dict[str, Any]:
        """验证代码语法"""
        try:
            compile(code, '<string>', 'exec')
            return {'valid': True, 'error': None}
        except SyntaxError as e:
            return {
                'valid': False,
                'error': f"语法错误: {e.msg} (行 {e.lineno})"
            }
        except Exception as e:
            return {
                'valid': False,
                'error': f"编译错误: {str(e)}"
            }
