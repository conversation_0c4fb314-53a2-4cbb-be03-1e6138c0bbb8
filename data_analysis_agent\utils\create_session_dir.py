#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建会话目录
管理输出文件的组织结构
"""

import os
from pathlib import Path
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


def create_session_dir(base_dir: str = "outputs") -> Path:
    """
    创建会话目录
    
    Args:
        base_dir: 基础输出目录
    
    Returns:
        会话目录路径
    """
    try:
        # 生成会话ID
        session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建会话目录
        session_dir = Path(base_dir) / f"session_{session_id}"
        session_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建子目录
        subdirs = ['charts', 'reports', 'data', 'logs']
        for subdir in subdirs:
            (session_dir / subdir).mkdir(exist_ok=True)
        
        logger.info(f"创建会话目录: {session_dir}")
        return session_dir
        
    except Exception as e:
        logger.error(f"创建会话目录失败: {e}")
        # 返回默认目录
        default_dir = Path(base_dir)
        default_dir.mkdir(parents=True, exist_ok=True)
        return default_dir


def get_latest_session_dir(base_dir: str = "outputs") -> Path:
    """
    获取最新的会话目录
    
    Args:
        base_dir: 基础输出目录
    
    Returns:
        最新会话目录路径
    """
    try:
        base_path = Path(base_dir)
        
        if not base_path.exists():
            return create_session_dir(base_dir)
        
        # 查找所有会话目录
        session_dirs = [d for d in base_path.iterdir() 
                       if d.is_dir() and d.name.startswith("session_")]
        
        if not session_dirs:
            return create_session_dir(base_dir)
        
        # 返回最新的会话目录
        latest_dir = max(session_dirs, key=lambda x: x.stat().st_mtime)
        logger.info(f"使用最新会话目录: {latest_dir}")
        return latest_dir
        
    except Exception as e:
        logger.error(f"获取最新会话目录失败: {e}")
        return create_session_dir(base_dir)


def cleanup_old_sessions(base_dir: str = "outputs", keep_count: int = 10):
    """
    清理旧的会话目录
    
    Args:
        base_dir: 基础输出目录
        keep_count: 保留的会话数量
    """
    try:
        base_path = Path(base_dir)
        
        if not base_path.exists():
            return
        
        # 查找所有会话目录
        session_dirs = [d for d in base_path.iterdir() 
                       if d.is_dir() and d.name.startswith("session_")]
        
        if len(session_dirs) <= keep_count:
            return
        
        # 按修改时间排序，删除最旧的
        session_dirs.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        dirs_to_remove = session_dirs[keep_count:]
        
        for dir_path in dirs_to_remove:
            try:
                import shutil
                shutil.rmtree(dir_path)
                logger.info(f"删除旧会话目录: {dir_path}")
            except Exception as e:
                logger.error(f"删除会话目录失败 {dir_path}: {e}")
        
    except Exception as e:
        logger.error(f"清理旧会话目录失败: {e}")


def get_session_info(session_dir: Path) -> dict:
    """
    获取会话信息
    
    Args:
        session_dir: 会话目录路径
    
    Returns:
        会话信息字典
    """
    try:
        info = {
            'session_id': session_dir.name,
            'created_time': datetime.fromtimestamp(session_dir.stat().st_ctime),
            'modified_time': datetime.fromtimestamp(session_dir.stat().st_mtime),
            'size_mb': sum(f.stat().st_size for f in session_dir.rglob('*') if f.is_file()) / (1024 * 1024),
            'file_count': len(list(session_dir.rglob('*'))),
            'subdirs': [d.name for d in session_dir.iterdir() if d.is_dir()]
        }
        
        return info
        
    except Exception as e:
        logger.error(f"获取会话信息失败: {e}")
        return {}


def list_all_sessions(base_dir: str = "outputs") -> list:
    """
    列出所有会话
    
    Args:
        base_dir: 基础输出目录
    
    Returns:
        会话信息列表
    """
    try:
        base_path = Path(base_dir)
        
        if not base_path.exists():
            return []
        
        session_dirs = [d for d in base_path.iterdir() 
                       if d.is_dir() and d.name.startswith("session_")]
        
        sessions = []
        for session_dir in session_dirs:
            session_info = get_session_info(session_dir)
            if session_info:
                sessions.append(session_info)
        
        # 按创建时间排序
        sessions.sort(key=lambda x: x['created_time'], reverse=True)
        
        return sessions
        
    except Exception as e:
        logger.error(f"列出会话失败: {e}")
        return []
