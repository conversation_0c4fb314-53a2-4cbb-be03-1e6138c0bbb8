#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LLM助手类
处理与大语言模型的交互
"""

import asyncio
import logging
from typing import Dict, Any, Optional
import openai
from tenacity import retry, stop_after_attempt, wait_exponential

logger = logging.getLogger(__name__)


class LLMHelper:
    """LLM助手类"""
    
    def __init__(self, config):
        self.config = config
        self.client = openai.AsyncOpenAI(
            api_key=config.api_key,
            base_url=config.base_url,
            timeout=config.timeout
        )
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def generate_response(self, prompt: str, **kwargs) -> str:
        """
        生成LLM响应
        
        Args:
            prompt: 输入提示词
            **kwargs: 额外参数
        
        Returns:
            LLM生成的响应
        """
        try:
            # 合并配置参数
            params = {
                "model": self.config.model,
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
                "temperature": kwargs.get("temperature", self.config.temperature),
            }
            
            # 添加其他参数
            if self.config.organization:
                params["organization"] = self.config.organization
            
            logger.info(f"发送LLM请求，模型: {params['model']}")
            
            # 调用API
            response = await self.client.chat.completions.create(**params)
            
            # 提取响应内容
            content = response.choices[0].message.content
            
            logger.info("LLM响应生成成功")
            return content
            
        except Exception as e:
            logger.error(f"LLM请求失败: {e}")
            raise e
    
    async def generate_streaming_response(self, prompt: str, **kwargs):
        """
        生成流式LLM响应
        
        Args:
            prompt: 输入提示词
            **kwargs: 额外参数
        
        Yields:
            LLM生成的响应片段
        """
        try:
            params = {
                "model": self.config.model,
                "messages": [{"role": "user", "content": prompt}],
                "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
                "temperature": kwargs.get("temperature", self.config.temperature),
                "stream": True
            }
            
            logger.info(f"发送流式LLM请求，模型: {params['model']}")
            
            async for chunk in await self.client.chat.completions.create(**params):
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error(f"流式LLM请求失败: {e}")
            raise e
    
    async def analyze_with_context(self, prompt: str, context: Dict[str, Any]) -> str:
        """
        带上下文的分析
        
        Args:
            prompt: 分析提示词
            context: 上下文信息
        
        Returns:
            分析结果
        """
        try:
            # 构建带上下文的提示词
            context_str = self._format_context(context)
            full_prompt = f"""
上下文信息：
{context_str}

分析任务：
{prompt}

请基于上下文信息进行详细分析。
            """.strip()
            
            return await self.generate_response(full_prompt)
            
        except Exception as e:
            logger.error(f"带上下文分析失败: {e}")
            raise e
    
    def _format_context(self, context: Dict[str, Any]) -> str:
        """格式化上下文信息"""
        try:
            formatted_parts = []
            
            for key, value in context.items():
                if isinstance(value, dict):
                    formatted_parts.append(f"{key}:")
                    for sub_key, sub_value in value.items():
                        formatted_parts.append(f"  {sub_key}: {sub_value}")
                elif isinstance(value, list):
                    formatted_parts.append(f"{key}: {', '.join(map(str, value))}")
                else:
                    formatted_parts.append(f"{key}: {value}")
            
            return "\n".join(formatted_parts)
            
        except Exception as e:
            logger.error(f"格式化上下文失败: {e}")
            return str(context)
    
    async def validate_response(self, response: str) -> bool:
        """验证响应质量"""
        try:
            # 基本验证
            if not response or len(response.strip()) < 10:
                return False
            
            # 检查是否包含错误信息
            error_indicators = ["error", "failed", "无法", "错误", "失败"]
            response_lower = response.lower()
            
            if any(indicator in response_lower for indicator in error_indicators):
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"响应验证失败: {e}")
            return False
    
    async def extract_key_points(self, text: str) -> list:
        """提取关键要点"""
        try:
            prompt = f"""
请从以下文本中提取关键要点，以列表形式返回：

文本内容：
{text}

请提取3-5个最重要的要点。
            """
            
            response = await self.generate_response(prompt)
            
            # 简单解析要点
            lines = response.strip().split('\n')
            key_points = []
            
            for line in lines:
                line = line.strip()
                if line and (line.startswith('-') or line.startswith('•') or line.startswith('*')):
                    key_points.append(line[1:].strip())
                elif line and line[0].isdigit():
                    key_points.append(line.split('.', 1)[-1].strip())
            
            return key_points
            
        except Exception as e:
            logger.error(f"提取关键要点失败: {e}")
            return []
