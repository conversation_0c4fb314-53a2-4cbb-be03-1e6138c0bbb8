#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AFAC增强系统演示
展示RAG增强、A2A协作、专业工具调用等核心功能
"""

import asyncio
import logging
import sys
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from src.knowledge.financial_knowledge_base import FinancialKnowledgeBase
from src.rag.financial_rag_system import FinancialRAGSystem
from src.tools.financial_tools_system import FinancialToolsManager
from src.agents.data_collector_agent import DataCollectorAgent
from src.agents.financial_analyst_agent import FinancialAnalystAgent
from src.agents.report_generator_agent import ReportGeneratorAgent
from src.data_integration.real_time_data_system import RealTimeDataSystem


class AFACEnhancedDemo:
    """AFAC增强系统演示类"""
    
    def __init__(self):
        """初始化演示系统"""
        self.logger = logging.getLogger(__name__)
        
        # 初始化核心组件
        self.knowledge_base = FinancialKnowledgeBase()
        self.rag_system = FinancialRAGSystem()
        self.tools_manager = FinancialToolsManager()
        
        # 初始化Agent
        agent_config = {
            'analysis_depth': 'comprehensive',
            'include_charts': True,
            'data_sources': ['mock', 'akshare']
        }
        
        self.data_collector = DataCollectorAgent('DataCollector', agent_config)
        self.financial_analyst = FinancialAnalystAgent('FinancialAnalyst', agent_config)
        self.report_generator = ReportGeneratorAgent('ReportGenerator', agent_config)
        
        # 初始化实时数据系统
        data_config = {'akshare_enabled': True, 'cache_ttl': 300}
        self.real_time_data = RealTimeDataSystem(data_config)
    
    async def demo_knowledge_base(self):
        """演示金融知识库功能"""
        print("\n" + "="*60)
        print("🧠 金融知识库演示")
        print("="*60)
        
        # 展示杜邦分析知识
        dupont_knowledge = self.knowledge_base.get_knowledge('dupont_analysis')
        print(f"📚 杜邦分析公式: {dupont_knowledge.get('formula')}")
        
        components = dupont_knowledge.get('components', {})
        for component, info in components.items():
            print(f"   • {component}: {info.get('meaning', 'N/A')}")
        
        # 展示财务比率知识
        print(f"\n📊 财务比率类别:")
        ratios_knowledge = self.knowledge_base.get_knowledge('financial_ratios')
        for category in ratios_knowledge.keys():
            if isinstance(ratios_knowledge[category], dict):
                print(f"   • {category}: {len(ratios_knowledge[category])}个指标")
        
        # 知识搜索演示
        search_results = self.knowledge_base.search_knowledge('风险评估')
        print(f"\n🔍 '风险评估'搜索结果: {len(search_results)}个相关条目")
    
    async def demo_rag_enhancement(self):
        """演示RAG增强功能"""
        print("\n" + "="*60)
        print("🔍 RAG增强系统演示")
        print("="*60)
        
        # 模拟财务分析数据
        mock_analysis = {
            'financial_ratios': {
                'roe': 18.5,
                'roa': 9.2,
                'net_profit_margin': 15.3,
                'debt_to_equity': 0.8,
                'current_ratio': 2.1
            },
            'profitability_analysis': {
                'profitability_score': 88
            }
        }
        
        # RAG增强分析
        enhanced_result = self.rag_system.enhance_analysis(
            'financial', mock_analysis, '分析优质公司财务状况'
        )
        
        print("📈 原始分析数据:")
        for key, value in mock_analysis['financial_ratios'].items():
            print(f"   • {key}: {value}")
        
        print(f"\n🚀 RAG增强后的专业见解:")
        insights = enhanced_result.get('professional_insights', [])
        for i, insight in enumerate(insights[:3], 1):
            print(f"   {i}. {insight}")
        
        print(f"\n📋 方法论说明:")
        methodology = enhanced_result.get('methodology_explanation', {})
        for method, description in methodology.items():
            print(f"   • {method}: {description}")
    
    async def demo_financial_tools(self):
        """演示专业金融工具"""
        print("\n" + "="*60)
        print("🛠️ 专业金融工具演示")
        print("="*60)
        
        # 模拟股价数据
        price_data = [25.0, 25.5, 26.2, 25.8, 27.1, 26.9, 28.3, 27.8, 29.2, 28.5]
        returns = [0.02, 0.027, -0.015, 0.05, -0.007, 0.052, -0.018, 0.05, -0.024]
        
        print("📊 技术指标计算:")
        
        # SMA计算
        sma_result = self.tools_manager.execute_tool(
            'technical_indicators', indicator_type='SMA', data=price_data, period=5
        )
        print(f"   • SMA(5): {sma_result.get('current_value', 0):.2f}")
        
        # MACD计算
        macd_result = self.tools_manager.execute_tool(
            'technical_indicators', indicator_type='MACD', data=price_data
        )
        print(f"   • MACD: DIF={macd_result.get('current_dif', 0):.3f}, DEA={macd_result.get('current_dea', 0):.3f}")
        
        # RSI计算
        rsi_result = self.tools_manager.execute_tool(
            'technical_indicators', indicator_type='RSI', data=price_data, period=14
        )
        print(f"   • RSI(14): {rsi_result.get('current_value', 0):.1f} ({rsi_result.get('condition', 'N/A')})")
        
        print(f"\n⚠️ 风险评估:")
        
        # VaR计算
        var_result = self.tools_manager.execute_tool(
            'risk_assessment', assessment_type='VaR', returns=returns, confidence=0.95
        )
        print(f"   • VaR(95%): {var_result.get('var_historical', 0):.3f}")
        
        # 夏普比率
        sharpe_result = self.tools_manager.execute_tool(
            'risk_assessment', assessment_type='sharpe_ratio', returns=returns
        )
        print(f"   • 夏普比率: {sharpe_result.get('sharpe_ratio', 0):.2f} ({sharpe_result.get('performance_level', 'N/A')})")
        
        print(f"\n💰 估值分析:")
        
        # DCF估值
        dcf_result = self.tools_manager.execute_tool(
            'valuation', valuation_type='DCF',
            cash_flows=[100, 110, 121, 133, 146],
            discount_rate=0.1, terminal_growth_rate=0.03
        )
        print(f"   • DCF估值: {dcf_result.get('total_enterprise_value', 0):,.0f}万元")
        
        # PE估值
        pe_result = self.tools_manager.execute_tool(
            'valuation', valuation_type='PE_valuation',
            earnings=50000000, industry_pe=15.2, growth_adjustment=1.1
        )
        print(f"   • PE估值: {pe_result.get('adjusted_valuation', 0):,.0f}元")
    
    async def demo_agent_collaboration(self):
        """演示Agent协作"""
        print("\n" + "="*60)
        print("🤝 Agent协作演示")
        print("="*60)
        
        company_symbol = "000001"
        
        print(f"🏢 开始分析公司: {company_symbol}")
        
        # 步骤1: 数据收集
        print(f"\n📊 步骤1: 数据收集Agent工作中...")
        data_result = await self.data_collector.execute_task(
            'collect_company_data', company_symbol=company_symbol
        )
        
        if data_result.get('success'):
            company_data = data_result['result']
            print(f"   ✅ 数据收集完成")
            print(f"   • 公司名称: {company_data.get('company_info', {}).get('name', 'N/A')}")
            print(f"   • 行业: {company_data.get('company_info', {}).get('industry', 'N/A')}")
            print(f"   • 市值: {company_data.get('company_info', {}).get('market_cap', 0):,}元")
            
            # 步骤2: 财务分析
            print(f"\n📈 步骤2: 财务分析Agent工作中...")
            analysis_result = await self.financial_analyst.execute_task(
                'analyze_company', symbol=company_symbol, company_data=company_data
            )
            
            if analysis_result.get('success'):
                analysis_data = analysis_result['result']
                print(f"   ✅ 财务分析完成")
                
                ratios = analysis_data.get('financial_ratios', {})
                print(f"   • ROE: {ratios.get('roe', 0):.2f}%")
                print(f"   • ROA: {ratios.get('roa', 0):.2f}%")
                print(f"   • 净利润率: {ratios.get('net_profit_margin', 0):.2f}%")
                
                recommendation = analysis_data.get('investment_recommendation', {})
                print(f"   • 投资评级: {recommendation.get('recommendation', 'N/A')}")
                
                # 步骤3: 报告生成
                print(f"\n📄 步骤3: 报告生成Agent工作中...")
                report_result = await self.report_generator.execute_task(
                    'generate_company_report',
                    symbol=company_symbol,
                    company_data=company_data,
                    analysis_result=analysis_data
                )
                
                if report_result.get('success'):
                    report_data = report_result['result']
                    print(f"   ✅ 报告生成完成")
                    print(f"   • 报告标题: {report_data.get('title', 'N/A')}")
                    print(f"   • 章节数量: {len(report_data.get('sections', {}))}")
                    print(f"   • 图表数量: {len(report_data.get('charts', []))}")
                    
                    quality = report_data.get('quality_metrics', {})
                    print(f"   • 专业评分: {quality.get('overall_professional_score', 0):.1f}/100")
    
    async def demo_real_time_data(self):
        """演示实时数据集成"""
        print("\n" + "="*60)
        print("🌐 实时数据集成演示")
        print("="*60)
        
        # 股票实时数据
        print("📈 获取实时股票数据...")
        stock_data = await self.real_time_data.get_real_time_stock_data('000001')
        
        print(f"   • 股票代码: {stock_data.get('symbol', 'N/A')}")
        print(f"   • 当前价格: {stock_data.get('current_price', 0):.2f}元")
        print(f"   • 涨跌幅: {stock_data.get('change_percent', 0):.2f}%")
        print(f"   • 成交量: {stock_data.get('volume', 0):,}股")
        print(f"   • PE比率: {stock_data.get('pe_ratio', 0):.1f}")
        
        # 市场数据
        print(f"\n📊 获取市场概况...")
        market_data = await self.real_time_data.get_real_time_market_data()
        
        indices = market_data.get('indices', {})
        print(f"   • 主要指数数量: {len(indices)}")
        
        for index_code, index_data in list(indices.items())[:3]:
            print(f"     - {index_code}: {index_data.get('current_value', 0):.2f} "
                  f"({index_data.get('change_percent', 0):+.2f}%)")
        
        # 系统性能
        metrics = self.real_time_data.get_system_metrics()
        print(f"\n⚡ 系统性能指标:")
        print(f"   • 请求成功率: {metrics.get('success_rate', 0):.1f}%")
        print(f"   • 缓存命中率: {metrics.get('cache_hit_rate', 0):.1f}%")
        print(f"   • 平均响应时间: {metrics.get('average_response_time', 0):.3f}秒")
    
    async def run_complete_demo(self):
        """运行完整演示"""
        print("🚀 AFAC增强系统完整演示")
        print("基于专业金融知识的智能分析系统")
        print("支持RAG增强、A2A协作、专业工具调用")
        
        try:
            # 各模块演示
            await self.demo_knowledge_base()
            await self.demo_rag_enhancement()
            await self.demo_financial_tools()
            await self.demo_agent_collaboration()
            await self.demo_real_time_data()
            
            # 总结
            print("\n" + "="*60)
            print("🎉 演示完成总结")
            print("="*60)
            print("✅ 金融知识库: 提供专业金融知识支撑")
            print("✅ RAG增强系统: 智能增强分析结果")
            print("✅ 专业工具: 技术指标、风险评估、估值模型")
            print("✅ Agent协作: 数据收集→财务分析→报告生成")
            print("✅ 实时数据: 多源数据集成与质量控制")
            
            print(f"\n🏆 AFAC系统已成功增强为专业级金融分析平台！")
            print(f"📊 支持公司分析、行业研究、宏观分析等多种场景")
            print(f"🎯 满足金融专业人士的高标准分析需求")
            
        except Exception as e:
            print(f"❌ 演示过程中发生错误: {e}")


async def main():
    """主函数"""
    # 设置日志级别
    logging.basicConfig(level=logging.WARNING)
    
    # 创建并运行演示
    demo = AFACEnhancedDemo()
    await demo.run_complete_demo()


if __name__ == "__main__":
    asyncio.run(main())
