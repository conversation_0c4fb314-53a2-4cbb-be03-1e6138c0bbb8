#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AFAC开源模型系统完整演示
展示基于开源LLM的专业金融分析能力
"""

import asyncio
import logging
import sys
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from src.models.afac_model_integration import AFACModelIntegration
from src.agents.data_collector_agent import DataCollectorAgent
from src.agents.financial_analyst_agent import FinancialAnalystAgent
from src.agents.report_generator_agent import ReportGeneratorAgent


class OpenSourceAFACDemo:
    """开源AFAC系统演示类"""
    
    def __init__(self):
        """初始化演示系统"""
        self.logger = logging.getLogger(__name__)
        
        # 初始化开源模型集成
        self.llm_integration = AFACModelIntegration()
        
        # 初始化Agent
        agent_config = {
            'analysis_depth': 'comprehensive',
            'include_charts': True,
            'data_sources': ['mock', 'akshare']
        }
        
        self.data_collector = DataCollectorAgent('DataCollector', agent_config)
        self.financial_analyst = FinancialAnalystAgent('FinancialAnalyst', agent_config)
        self.report_generator = ReportGeneratorAgent('ReportGenerator', agent_config)
    
    async def initialize_system(self):
        """初始化系统"""
        print("🚀 初始化AFAC开源模型系统...")
        
        # 初始化默认模型
        success = await self.llm_integration.initialize_default_model('qwen-7b-chat')
        if success:
            print("✅ 默认模型(Qwen-7B-Chat)初始化成功")
            
            # 获取系统状态
            status = self.llm_integration.get_integration_status()
            print(f"✅ 当前模型: {status['current_model']}")
            print(f"✅ 可用模型: {', '.join(status['available_models'])}")
            return True
        else:
            print("❌ 模型初始化失败")
            return False
    
    async def demo_model_capabilities(self):
        """演示模型能力"""
        print("\n" + "="*60)
        print("🤖 开源模型能力演示")
        print("="*60)
        
        # 测试不同类型的金融分析任务
        test_cases = [
            {
                'name': '财务比率分析',
                'company_data': {
                    'symbol': '000001',
                    'company_info': {'name': '平安银行', 'industry': '银行业'},
                    'financial_statements': {
                        'revenue': 150000000000,
                        'net_income': 35000000000,
                        'total_assets': 4500000000000,
                        'total_equity': 350000000000
                    }
                }
            },
            {
                'name': '科技公司分析',
                'company_data': {
                    'symbol': '000002',
                    'company_info': {'name': '万科A', 'industry': '房地产'},
                    'financial_statements': {
                        'revenue': 450000000000,
                        'net_income': 41500000000,
                        'total_assets': 1800000000000,
                        'total_equity': 280000000000
                    }
                }
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📊 测试案例 {i}: {test_case['name']}")
            
            # LLM财务分析
            analysis_result = await self.llm_integration.financial_analysis_with_llm(
                test_case['company_data'], "comprehensive"
            )
            
            if analysis_result.get('success'):
                print(f"✅ 财务分析完成 (响应时间: {analysis_result['response_time']:.2f}s)")
                
                # 显示分析要点
                key_insights = analysis_result['analysis_result'].get('key_insights', [])
                if key_insights:
                    print("   关键见解:")
                    for insight in key_insights[:2]:
                        print(f"   • {insight}")
                
                # LLM投资建议
                advice_result = await self.llm_integration.investment_advice_with_llm(
                    test_case['company_data'], analysis_result['analysis_result']
                )
                
                if advice_result.get('success'):
                    recommendation = advice_result['investment_advice'].get('recommendation', 'HOLD')
                    print(f"✅ 投资建议: {recommendation}")
            else:
                print(f"❌ 分析失败: {analysis_result.get('error', 'Unknown error')}")
    
    async def demo_model_comparison(self):
        """演示不同模型对比"""
        print("\n" + "="*60)
        print("⚖️ 多模型性能对比")
        print("="*60)
        
        models_to_test = ['qwen-7b-chat', 'chatglm3-6b', 'baichuan2-7b-chat']
        test_prompt = """请分析以下公司财务状况：
        公司：测试科技公司
        ROE：18.5%
        净利润率：12.3%
        资产负债率：45%
        流动比率：1.8
        
        请提供专业的财务分析和投资建议。"""
        
        results = {}
        
        for model_name in models_to_test:
            print(f"\n🔄 切换到模型: {model_name}")
            
            # 切换模型
            switch_success = await self.llm_integration.switch_model(model_name)
            
            if switch_success:
                start_time = datetime.now()
                
                # 生成响应
                response = await self.llm_integration.model_manager.generate_response(
                    test_prompt, model_name, max_length=512, temperature=0.7
                )
                
                response_time = (datetime.now() - start_time).total_seconds()
                
                results[model_name] = {
                    'response_time': response_time,
                    'response_length': len(response),
                    'response_preview': response[:150] + "..." if len(response) > 150 else response
                }
                
                print(f"✅ 响应时间: {response_time:.2f}s")
                print(f"✅ 响应长度: {len(response)}字符")
                print(f"✅ 响应预览: {results[model_name]['response_preview']}")
            else:
                print(f"❌ 模型切换失败")
        
        # 性能总结
        if results:
            print(f"\n📊 性能对比总结:")
            fastest_model = min(results.keys(), key=lambda x: results[x]['response_time'])
            longest_response = max(results.keys(), key=lambda x: results[x]['response_length'])
            
            print(f"🏃 最快模型: {fastest_model} ({results[fastest_model]['response_time']:.2f}s)")
            print(f"📝 最详细回答: {longest_response} ({results[longest_response]['response_length']}字符)")
    
    async def demo_end_to_end_workflow(self):
        """演示端到端工作流"""
        print("\n" + "="*60)
        print("🔄 端到端LLM增强工作流演示")
        print("="*60)
        
        company_symbol = "000001"
        
        print(f"🏢 开始分析公司: {company_symbol}")
        
        # 步骤1: 数据收集
        print(f"\n📊 步骤1: 数据收集...")
        data_result = await self.data_collector.execute_task(
            'collect_company_data', company_symbol=company_symbol
        )
        
        if data_result.get('success'):
            company_data = data_result['result']
            print(f"✅ 数据收集完成")
            print(f"   • 公司: {company_data.get('company_info', {}).get('name', 'N/A')}")
            print(f"   • 行业: {company_data.get('company_info', {}).get('industry', 'N/A')}")
            
            # 步骤2: LLM增强财务分析
            print(f"\n🧠 步骤2: LLM增强财务分析...")
            analysis_result = await self.financial_analyst.execute_task(
                'analyze_company', symbol=company_symbol, company_data=company_data
            )
            
            if analysis_result.get('success'):
                analysis_data = analysis_result['result']
                print(f"✅ 财务分析完成")
                
                # 显示传统分析结果
                ratios = analysis_data.get('financial_ratios', {})
                print(f"   传统分析 - ROE: {ratios.get('roe', 0):.2f}%")
                
                # 显示LLM增强结果
                if 'llm_enhancement' in analysis_data:
                    print(f"   ✨ LLM增强分析: 已集成")
                    llm_insights = analysis_data.get('llm_insights', [])
                    if llm_insights:
                        print(f"   ✨ LLM见解数量: {len(llm_insights)}")
                
                if 'llm_investment_advice' in analysis_data:
                    llm_advice = analysis_data['llm_investment_advice']
                    print(f"   ✨ LLM投资建议: {llm_advice.get('recommendation', 'N/A')}")
                
                # 步骤3: LLM增强报告生成
                print(f"\n📄 步骤3: LLM增强报告生成...")
                report_result = await self.report_generator.execute_task(
                    'generate_company_report',
                    symbol=company_symbol,
                    company_data=company_data,
                    analysis_result=analysis_data
                )
                
                if report_result.get('success'):
                    report_data = report_result['result']
                    print(f"✅ 报告生成完成")
                    print(f"   • 报告标题: {report_data.get('title', 'N/A')}")
                    print(f"   • 章节数量: {len(report_data.get('sections', {}))}")
                    
                    # 显示LLM增强标记
                    if report_data.get('metadata', {}).get('llm_enhanced'):
                        print(f"   ✨ LLM增强报告: 已启用")
                    
                    if 'llm_enhanced_content' in report_data:
                        print(f"   ✨ LLM增强内容: 已集成")
                        llm_content = report_data['llm_enhanced_content']
                        quality_score = llm_content.get('content_quality_score', 0)
                        print(f"   ✨ 内容质量评分: {quality_score}/100")
    
    async def demo_system_status(self):
        """演示系统状态"""
        print("\n" + "="*60)
        print("📊 系统状态监控")
        print("="*60)
        
        # 获取集成状态
        status = self.llm_integration.get_integration_status()
        
        print(f"🤖 当前模型: {status['current_model']}")
        print(f"📦 已加载模型: {', '.join(status['loaded_models'])}")
        print(f"🔧 可用模型: {', '.join(status['available_models'])}")
        
        # 集成统计
        integration_stats = status['integration_stats']
        print(f"\n📈 集成统计:")
        print(f"   • 总请求数: {integration_stats['total_requests']}")
        print(f"   • 成功请求: {integration_stats['successful_requests']}")
        print(f"   • 失败请求: {integration_stats['failed_requests']}")
        print(f"   • 平均响应时间: {integration_stats['average_response_time']:.3f}s")
        print(f"   • 模型切换次数: {integration_stats['model_switches']}")
        
        # 模型统计
        model_stats = status['model_stats']
        print(f"\n🧠 模型统计:")
        print(f"   • 推理请求: {model_stats['total_requests']}")
        print(f"   • 生成Token: {model_stats['total_tokens']}")
        print(f"   • 平均延迟: {model_stats['average_latency']:.3f}s")
        
        # 推理引擎统计
        inference_stats = status['inference_stats']
        print(f"\n⚡ 推理引擎:")
        print(f"   • 成功率: {inference_stats['success_rate']:.1f}%")
        print(f"   • 可用框架: {', '.join(inference_stats['available_frameworks'])}")
        print(f"   • 工作线程: {inference_stats['max_workers']}")
    
    async def run_complete_demo(self):
        """运行完整演示"""
        print("🚀 AFAC开源模型系统完整演示")
        print("基于开源LLM的专业金融分析平台")
        print("符合竞赛要求，无需闭源API")
        
        try:
            # 初始化系统
            if not await self.initialize_system():
                print("❌ 系统初始化失败")
                return False
            
            # 各模块演示
            await self.demo_model_capabilities()
            await self.demo_model_comparison()
            await self.demo_end_to_end_workflow()
            await self.demo_system_status()
            
            # 总结
            print("\n" + "="*60)
            print("🎉 演示完成总结")
            print("="*60)
            print("✅ 开源模型集成: 支持Qwen、ChatGLM、Baichuan、InternLM")
            print("✅ 本地推理引擎: 无需外部API调用")
            print("✅ 专业提示词工程: 针对金融领域优化")
            print("✅ Agent智能增强: LLM增强的分析和报告")
            print("✅ 多模型对比: 性能和质量评估")
            print("✅ 完整工作流: 数据→分析→报告全流程")
            
            print(f"\n🏆 AFAC系统已成功集成开源模型！")
            print(f"🎯 完全符合竞赛开源要求")
            print(f"🚀 提供专业级金融分析能力")
            print(f"⚡ 支持本地部署和推理")
            
            return True
            
        except Exception as e:
            print(f"❌ 演示过程中发生错误: {e}")
            return False
        finally:
            # 清理资源
            await self.llm_integration.cleanup()


async def main():
    """主函数"""
    # 设置日志级别
    logging.basicConfig(level=logging.WARNING)
    
    # 创建并运行演示
    demo = OpenSourceAFACDemo()
    success = await demo.run_complete_demo()
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
