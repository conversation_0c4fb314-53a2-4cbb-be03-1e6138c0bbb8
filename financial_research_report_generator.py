#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AFAC赛题四 - 金融研报自动生成系统
基于AI大模型的智能金融研报生成平台

主要功能：
1. 公司/个股研报生成
2. 行业/子行业研报生成  
3. 宏观经济/策略研报生成
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime
import json
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from data_analysis_agent.data_analysis_agent import DataAnalysisAgent
    from utils.get_company_info import get_company_info
    from utils.get_financial_statements import get_financial_statements
    from utils.get_shareholder_info import get_shareholder_info
    from utils.search_info import search_industry_info
    from pocketflow import Flow
    from industry_workflow import IndustryResearchFlow
    from macro_workflow import MacroResearchFlow
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("正在创建必要的模块...")


class FinancialResearchReportGenerator:
    """金融研报生成器主类"""
    
    def __init__(self, config=None):
        self.config = config or self._get_default_config()
        self.setup_logging()
        self.data_agent = DataAnalysisAgent()
        self.output_dir = Path("outputs")
        self.output_dir.mkdir(exist_ok=True)
        
    def _get_default_config(self):
        """获取默认配置"""
        return {
            'target_companies': ['000001', '000002', '600036'],  # 平安银行、万科A、招商银行
            'target_industries': ['银行业', '房地产', '新能源汽车'],
            'macro_indicators': ['GDP', 'CPI', '利率', '汇率', 'PMI'],
            'llm_model': 'gpt-4',
            'output_format': ['markdown', 'word'],
            'include_charts': True
        }
    
    def setup_logging(self):
        """设置日志"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / f"research_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger("FinancialResearchReportGenerator")
    
    async def generate_company_report(self, company_code, company_name=None, market="A"):
        """
        生成公司研报
        
        Args:
            company_code: 股票代码
            company_name: 公司名称
            market: 市场类型 (A/HK/US)
        """
        try:
            self.logger.info(f"开始生成公司 {company_code} 的研报...")
            
            # 1. 数据收集
            print("📊 收集公司数据...")
            company_data = await self._collect_company_data(company_code, company_name, market)
            
            # 2. 数据分析
            print("🔍 进行财务分析...")
            analysis_result = await self._analyze_company_data(company_data)
            
            # 3. 生成报告
            print("📝 生成研报...")
            report = await self._generate_company_report_content(company_data, analysis_result)
            
            # 4. 保存报告
            output_file = await self._save_report(report, f"company_report_{company_code}")
            
            self.logger.info(f"公司研报生成完成: {output_file}")
            return output_file
            
        except Exception as e:
            self.logger.error(f"生成公司研报失败: {e}")
            return None
    
    async def generate_industry_report(self, industry_name):
        """
        生成行业研报
        
        Args:
            industry_name: 行业名称
        """
        try:
            self.logger.info(f"开始生成行业 {industry_name} 的研报...")
            
            # 使用工作流引擎
            flow = Flow()
            industry_flow = IndustryResearchFlow()
            flow.add_node("industry_research", industry_flow)
            
            # 设置研究参数
            shared_data = {
                "industry": industry_name,
                "context": [],
                "output_dir": str(self.output_dir)
            }
            
            # 执行研究流程
            print("🏭 执行行业研究流程...")
            result = flow.run(shared_data)
            
            # 保存报告
            output_file = await self._save_report(result, f"industry_report_{industry_name}")
            
            self.logger.info(f"行业研报生成完成: {output_file}")
            return output_file
            
        except Exception as e:
            self.logger.error(f"生成行业研报失败: {e}")
            return None
    
    async def generate_macro_report(self, indicators=None):
        """
        生成宏观经济研报
        
        Args:
            indicators: 宏观指标列表
        """
        try:
            indicators = indicators or self.config['macro_indicators']
            self.logger.info(f"开始生成宏观经济研报...")
            
            # 使用工作流引擎
            flow = Flow()
            macro_flow = MacroResearchFlow()
            flow.add_node("macro_research", macro_flow)
            
            # 设置研究参数
            shared_data = {
                "indicators": indicators,
                "context": [],
                "output_dir": str(self.output_dir)
            }
            
            # 执行研究流程
            print("🌍 执行宏观经济研究流程...")
            result = flow.run(shared_data)
            
            # 保存报告
            output_file = await self._save_report(result, "macro_economic_report")
            
            self.logger.info(f"宏观经济研报生成完成: {output_file}")
            return output_file
            
        except Exception as e:
            self.logger.error(f"生成宏观经济研报失败: {e}")
            return None
    
    async def generate_all_reports(self):
        """生成所有类型的研报"""
        try:
            self.logger.info("开始生成所有研报...")
            
            results = {
                'company_reports': [],
                'industry_reports': [],
                'macro_reports': [],
                'summary': {}
            }
            
            # 生成公司研报
            for company_code in self.config['target_companies']:
                report_file = await self.generate_company_report(company_code)
                if report_file:
                    results['company_reports'].append(report_file)
            
            # 生成行业研报
            for industry in self.config['target_industries']:
                report_file = await self.generate_industry_report(industry)
                if report_file:
                    results['industry_reports'].append(report_file)
            
            # 生成宏观研报
            macro_report = await self.generate_macro_report()
            if macro_report:
                results['macro_reports'].append(macro_report)
            
            # 生成汇总
            results['summary'] = {
                'total_reports': len(results['company_reports']) + len(results['industry_reports']) + len(results['macro_reports']),
                'generated_at': datetime.now().isoformat(),
                'config': self.config
            }
            
            # 保存汇总结果
            summary_file = self.output_dir / "generation_summary.json"
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info("所有研报生成完成")
            return results
            
        except Exception as e:
            self.logger.error(f"生成所有研报失败: {e}")
            return {}
    
    async def _collect_company_data(self, company_code, company_name, market):
        """收集公司数据"""
        try:
            data = {}
            
            # 获取公司基本信息
            try:
                company_info = get_company_info(company_code, market)
                data['company_info'] = company_info
            except Exception as e:
                self.logger.warning(f"获取公司信息失败: {e}")
                data['company_info'] = {}
            
            # 获取财务报表
            try:
                financial_data = get_financial_statements(company_code, market)
                data['financial_statements'] = financial_data
            except Exception as e:
                self.logger.warning(f"获取财务报表失败: {e}")
                data['financial_statements'] = {}
            
            # 获取股东信息
            try:
                shareholder_info = get_shareholder_info(company_code, market)
                data['shareholder_info'] = shareholder_info
            except Exception as e:
                self.logger.warning(f"获取股东信息失败: {e}")
                data['shareholder_info'] = {}
            
            return data
            
        except Exception as e:
            self.logger.error(f"收集公司数据失败: {e}")
            return {}
    
    async def _analyze_company_data(self, company_data):
        """分析公司数据"""
        try:
            # 使用数据分析智能体进行分析
            analysis_prompt = f"""
            请对以下公司数据进行全面的财务分析：
            
            公司信息：{company_data.get('company_info', {})}
            财务报表：{company_data.get('financial_statements', {})}
            股东信息：{company_data.get('shareholder_info', {})}
            
            请从以下维度进行分析：
            1. 财务健康状况
            2. 盈利能力分析
            3. 偿债能力分析
            4. 运营效率分析
            5. 成长性分析
            6. 投资价值评估
            
            请生成相应的图表和详细分析报告。
            """
            
            analysis_result = await self.data_agent.analyze(analysis_prompt)
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"分析公司数据失败: {e}")
            return {}
    
    async def _generate_company_report_content(self, company_data, analysis_result):
        """生成公司研报内容"""
        try:
            report_prompt = f"""
            基于以下数据和分析结果，生成一份专业的公司研究报告：
            
            公司数据：{company_data}
            分析结果：{analysis_result}
            
            报告应包含以下章节：
            1. 执行摘要
            2. 公司概况
            3. 业务分析
            4. 财务分析
            5. 估值分析
            6. 竞争分析
            7. 风险分析
            8. 投资建议
            
            请确保报告专业、详细，并包含明确的投资评级。
            """
            
            report_content = await self.data_agent.generate_report(report_prompt)
            return report_content
            
        except Exception as e:
            self.logger.error(f"生成公司研报内容失败: {e}")
            return ""
    
    async def _save_report(self, content, filename):
        """保存报告"""
        try:
            # 创建会话目录
            session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
            session_dir = self.output_dir / f"session_{session_id}"
            session_dir.mkdir(exist_ok=True)
            
            # 保存Markdown格式
            if 'markdown' in self.config['output_format']:
                md_file = session_dir / f"{filename}.md"
                with open(md_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.logger.info(f"Markdown报告已保存: {md_file}")
            
            # 保存Word格式
            if 'word' in self.config['output_format']:
                try:
                    from docx import Document
                    doc = Document()
                    doc.add_paragraph(content)
                    word_file = session_dir / f"{filename}.docx"
                    doc.save(str(word_file))
                    self.logger.info(f"Word报告已保存: {word_file}")
                except ImportError:
                    self.logger.warning("python-docx未安装，跳过Word格式保存")
            
            return str(session_dir / f"{filename}.md")
            
        except Exception as e:
            self.logger.error(f"保存报告失败: {e}")
            return None


def main():
    """主函数"""
    try:
        print("🏆 AFAC赛题四 - 金融研报自动生成系统")
        print("=" * 60)
        
        # 创建生成器
        generator = FinancialResearchReportGenerator()
        
        # 运行生成流程
        results = asyncio.run(generator.generate_all_reports())
        
        if results:
            print("\n✅ 研报生成完成!")
            print(f"📊 总计生成 {results['summary']['total_reports']} 份研报")
            print(f"📁 输出目录: {generator.output_dir}")
        else:
            print("\n❌ 研报生成失败")
        
    except Exception as e:
        print(f"\n❌ 系统运行失败: {e}")


if __name__ == "__main__":
    main()
