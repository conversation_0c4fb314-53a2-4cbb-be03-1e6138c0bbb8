#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
行业研究工作流
基于PocketFlow的行业分析流程
"""

import asyncio
from typing import Dict, Any, List
import logging
from datetime import datetime
from pathlib import Path

from pocketflow import Flow, flow_node
from utils.search_info import search_industry_info
from data_analysis_agent.data_analysis_agent import DataAnalysisAgent

logger = logging.getLogger(__name__)


class IndustryResearchFlow:
    """行业研究工作流"""

    def __init__(self):
        self.flow = Flow("IndustryResearchFlow")
        self.data_agent = DataAnalysisAgent()
        self._setup_flow()

    def _setup_flow(self):
        """设置工作流"""
        # 添加工作流节点
        self.flow.add_node("collect_industry_data", self.collect_industry_data)
        self.flow.add_node("analyze_market_structure", self.analyze_market_structure, ["collect_industry_data"])
        self.flow.add_node("analyze_supply_chain", self.analyze_supply_chain, ["collect_industry_data"])
        self.flow.add_node("analyze_competitive_landscape", self.analyze_competitive_landscape, ["collect_industry_data"])
        self.flow.add_node("analyze_policy_environment", self.analyze_policy_environment, ["collect_industry_data"])
        self.flow.add_node("analyze_development_trends", self.analyze_development_trends,
                          ["analyze_market_structure", "analyze_supply_chain", "analyze_competitive_landscape"])
        self.flow.add_node("generate_investment_opportunities", self.generate_investment_opportunities,
                          ["analyze_development_trends", "analyze_policy_environment"])
        self.flow.add_node("assess_risk_factors", self.assess_risk_factors,
                          ["analyze_competitive_landscape", "analyze_policy_environment"])
        self.flow.add_node("generate_industry_report", self.generate_industry_report,
                          ["generate_investment_opportunities", "assess_risk_factors"])

    async def run(self, shared_data: Dict[str, Any]) -> Dict[str, Any]:
        """运行行业研究工作流"""
        return await self.flow.run(shared_data)

    async def collect_industry_data(self, shared_data: Dict[str, Any]) -> Dict[str, Any]:
        """收集行业数据"""
        try:
            industry = shared_data.get("industry", "未知行业")
            logger.info(f"开始收集行业数据: {industry}")

            # 搜索行业信息
            industry_info = search_industry_info(industry)

            # 保存到共享数据
            shared_data["industry_info"] = industry_info

            logger.info(f"行业数据收集完成: {industry}")
            return industry_info

        except Exception as e:
            logger.error(f"收集行业数据失败: {e}")
            return {}

    async def analyze_market_structure(self, shared_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析市场结构"""
        try:
            industry_info = shared_data.get("industry_info", {})
            industry = shared_data.get("industry", "未知行业")

            logger.info(f"开始分析市场结构: {industry}")

            # 构建分析提示词
            analysis_prompt = f"""
            请对{industry}的市场结构进行深度分析：

            行业数据：{industry_info}

            请从以下维度进行分析：
            1. 市场规模和增长趋势
            2. 市场集中度分析
            3. 主要参与者及市场份额
            4. 进入壁垒分析
            5. 替代品威胁
            6. 客户议价能力
            7. 供应商议价能力

            请提供详细的分析结果和图表建议。
            """

            # 使用数据分析智能体进行分析
            analysis_result = await self.data_agent.analyze(analysis_prompt, industry_info)

            shared_data["market_structure_analysis"] = analysis_result

            logger.info(f"市场结构分析完成: {industry}")
            return analysis_result

        except Exception as e:
            logger.error(f"市场结构分析失败: {e}")
            return {}

    async def analyze_supply_chain(self, shared_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析产业链"""
        try:
            industry_info = shared_data.get("industry_info", {})
            industry = shared_data.get("industry", "未知行业")

            logger.info(f"开始分析产业链: {industry}")

            analysis_prompt = f"""
            请对{industry}的产业链进行全面分析：

            行业数据：{industry_info}

            分析要点：
            1. 上游供应链分析（原材料、设备、技术）
            2. 中游制造环节分析（生产工艺、技术水平、产能分布）
            3. 下游应用市场分析（主要应用领域、需求特点）
            4. 产业链价值分布
            5. 关键环节识别
            6. 产业链风险点
            7. 产业链发展趋势

            请生成产业链图表和详细分析报告。
            """

            analysis_result = await self.data_agent.analyze(analysis_prompt, industry_info)

            shared_data["supply_chain_analysis"] = analysis_result

            logger.info(f"产业链分析完成: {industry}")
            return analysis_result

        except Exception as e:
            logger.error(f"产业链分析失败: {e}")
            return {}

    async def analyze_competitive_landscape(self, shared_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析竞争格局"""
        try:
            industry_info = shared_data.get("industry_info", {})
            industry = shared_data.get("industry", "未知行业")

            logger.info(f"开始分析竞争格局: {industry}")

            analysis_prompt = f"""
            请对{industry}的竞争格局进行深入分析：

            行业数据：{industry_info}

            分析维度：
            1. 竞争强度评估
            2. 主要竞争者分析
            3. 竞争优势对比
            4. 市场地位分析
            5. 竞争策略分析
            6. 新进入者威胁
            7. 竞争格局变化趋势
            8. 并购重组机会

            请生成竞争格局图表和战略建议。
            """

            analysis_result = await self.data_agent.analyze(analysis_prompt, industry_info)

            shared_data["competitive_landscape"] = analysis_result

            logger.info(f"竞争格局分析完成: {industry}")
            return analysis_result

        except Exception as e:
            logger.error(f"竞争格局分析失败: {e}")
            return {}

    async def analyze_policy_environment(self, shared_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析政策环境"""
        try:
            industry_info = shared_data.get("industry_info", {})
            industry = shared_data.get("industry", "未知行业")

            logger.info(f"开始分析政策环境: {industry}")

            analysis_prompt = f"""
            请对{industry}的政策环境进行全面分析：

            行业数据：{industry_info}

            分析要点：
            1. 相关政策法规梳理
            2. 政策支持力度评估
            3. 监管环境分析
            4. 政策变化趋势
            5. 政策对行业的影响
            6. 政策风险评估
            7. 政策机会识别
            8. 政策建议

            请重点关注最新的政策动向和未来政策预期。
            """

            analysis_result = await self.data_agent.analyze(analysis_prompt, industry_info)

            shared_data["policy_environment"] = analysis_result

            logger.info(f"政策环境分析完成: {industry}")
            return analysis_result

        except Exception as e:
            logger.error(f"政策环境分析失败: {e}")
            return {}

    async def analyze_development_trends(self, shared_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析发展趋势"""
        try:
            industry = shared_data.get("industry", "未知行业")
            market_structure = shared_data.get("market_structure_analysis", {})
            supply_chain = shared_data.get("supply_chain_analysis", {})
            competitive_landscape = shared_data.get("competitive_landscape", {})

            logger.info(f"开始分析发展趋势: {industry}")

            analysis_prompt = f"""
            基于前期分析结果，请对{industry}的发展趋势进行预测：

            市场结构分析：{market_structure}
            产业链分析：{supply_chain}
            竞争格局分析：{competitive_landscape}

            趋势分析要点：
            1. 技术发展趋势
            2. 市场需求趋势
            3. 商业模式创新趋势
            4. 产业整合趋势
            5. 国际化发展趋势
            6. 可持续发展趋势
            7. 数字化转型趋势
            8. 未来3-5年发展预测

            请提供具体的趋势判断和投资方向建议。
            """

            analysis_result = await self.data_agent.analyze(analysis_prompt)

            shared_data["development_trends"] = analysis_result

            logger.info(f"发展趋势分析完成: {industry}")
            return analysis_result

        except Exception as e:
            logger.error(f"发展趋势分析失败: {e}")
            return {}

    async def generate_investment_opportunities(self, shared_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成投资机会"""
        try:
            industry = shared_data.get("industry", "未知行业")
            trends = shared_data.get("development_trends", {})
            policy = shared_data.get("policy_environment", {})

            logger.info(f"开始生成投资机会: {industry}")

            analysis_prompt = f"""
            基于发展趋势和政策环境分析，识别{industry}的投资机会：

            发展趋势：{trends}
            政策环境：{policy}

            投资机会分析：
            1. 细分领域投资机会
            2. 技术创新投资机会
            3. 商业模式创新机会
            4. 并购整合机会
            5. 国际化投资机会
            6. 投资时机分析
            7. 投资风险评估
            8. 投资建议和策略

            请提供具体的投资标的和投资逻辑。
            """

            analysis_result = await self.data_agent.analyze(analysis_prompt)

            shared_data["investment_opportunities"] = analysis_result

            logger.info(f"投资机会分析完成: {industry}")
            return analysis_result

        except Exception as e:
            logger.error(f"投资机会分析失败: {e}")
            return {}

    async def assess_risk_factors(self, shared_data: Dict[str, Any]) -> Dict[str, Any]:
        """评估风险因素"""
        try:
            industry = shared_data.get("industry", "未知行业")
            competitive = shared_data.get("competitive_landscape", {})
            policy = shared_data.get("policy_environment", {})

            logger.info(f"开始评估风险因素: {industry}")

            analysis_prompt = f"""
            请对{industry}的风险因素进行全面评估：

            竞争格局：{competitive}
            政策环境：{policy}

            风险评估维度：
            1. 市场风险（需求波动、价格风险）
            2. 竞争风险（竞争加剧、替代威胁）
            3. 技术风险（技术变革、技术落后）
            4. 政策风险（政策变化、监管风险）
            5. 供应链风险（原材料、供应中断）
            6. 财务风险（资金链、盈利能力）
            7. 运营风险（管理、人才）
            8. 系统性风险（宏观经济、不可抗力）

            请对每类风险进行评级并提出应对措施。
            """

            analysis_result = await self.data_agent.analyze(analysis_prompt)

            shared_data["risk_factors"] = analysis_result

            logger.info(f"风险因素评估完成: {industry}")
            return analysis_result

        except Exception as e:
            logger.error(f"风险因素评估失败: {e}")
            return {}

    async def generate_industry_report(self, shared_data: Dict[str, Any]) -> str:
        """生成行业研究报告"""
        try:
            industry = shared_data.get("industry", "未知行业")

            logger.info(f"开始生成行业研究报告: {industry}")

            # 收集所有分析结果
            all_analysis = {
                "industry_info": shared_data.get("industry_info", {}),
                "market_structure": shared_data.get("market_structure_analysis", {}),
                "supply_chain": shared_data.get("supply_chain_analysis", {}),
                "competitive_landscape": shared_data.get("competitive_landscape", {}),
                "policy_environment": shared_data.get("policy_environment", {}),
                "development_trends": shared_data.get("development_trends", {}),
                "investment_opportunities": shared_data.get("investment_opportunities", {}),
                "risk_factors": shared_data.get("risk_factors", {})
            }

            report_prompt = f"""
            基于全面的行业分析，生成{industry}行业研究报告：

            分析结果：{all_analysis}

            报告结构：
            1. 执行摘要
            2. 行业概况
            3. 市场结构分析
            4. 产业链分析
            5. 竞争格局分析
            6. 政策环境分析
            7. 发展趋势分析
            8. 投资机会分析
            9. 风险因素分析
            10. 投资建议

            请生成专业、详细的行业研究报告，确保逻辑清晰、数据支撑充分。
            """

            report_content = await self.data_agent.generate_report(report_prompt, all_analysis)

            # 保存报告
            output_dir = shared_data.get("output_dir", "outputs")
            report_file = Path(output_dir) / f"{industry}_行业研究报告.md"
            report_file.parent.mkdir(parents=True, exist_ok=True)

            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)

            shared_data["industry_report"] = report_content
            shared_data["report_file"] = str(report_file)

            logger.info(f"行业研究报告生成完成: {report_file}")
            return report_content

        except Exception as e:
            logger.error(f"生成行业研究报告失败: {e}")
            return ""


# 使用装饰器的方式定义工作流节点
@flow_node("collect_data")
async def collect_industry_data_node(shared_data: Dict[str, Any]) -> Dict[str, Any]:
    """收集行业数据节点"""
    industry = shared_data.get("industry", "未知行业")
    industry_info = search_industry_info(industry)
    return industry_info


@flow_node("analyze_structure", dependencies=["collect_data"])
async def analyze_market_structure_node(shared_data: Dict[str, Any]) -> Dict[str, Any]:
    """分析市场结构节点"""
    # 实现市场结构分析逻辑
    return {"market_structure": "分析结果"}


# 示例使用
if __name__ == "__main__":
    async def test_industry_workflow():
        # 创建行业研究工作流
        industry_flow = IndustryResearchFlow()

        # 设置研究参数
        shared_data = {
            "industry": "银行业",
            "context": [],
            "output_dir": "outputs/test"
        }

        # 运行工作流
        result = await industry_flow.run(shared_data)

        print("行业研究完成")
        print(f"报告文件: {result.get('report_file')}")

    # 运行测试
    asyncio.run(test_industry_workflow())