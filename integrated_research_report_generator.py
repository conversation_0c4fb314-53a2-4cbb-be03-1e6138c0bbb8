#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合式研报生成器
集成所有功能的完整金融研报生成系统
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime
import json
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from financial_research_report_generator import FinancialResearchReportGenerator
    from industry_workflow import IndustryResearchFlow
    from macro_workflow import MacroResearchFlow
    from pocketflow import Flow, ParallelFlow
    from data_analysis_agent.data_analysis_agent import DataAnalysisAgent
    from utils.get_company_info import get_company_info
    from utils.get_financial_statements import get_financial_statements
    from utils.search_info import search_industry_info, search_macro_data
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保所有依赖已正确安装")


class IntegratedResearchReportGenerator:
    """整合式研报生成器"""
    
    def __init__(self, config=None):
        self.config = config or self._get_default_config()
        self.setup_logging()
        
        # 初始化组件
        self.main_generator = FinancialResearchReportGenerator(self.config)
        self.industry_flow = IndustryResearchFlow()
        self.macro_flow = MacroResearchFlow()
        self.data_agent = DataAnalysisAgent()
        
        # 输出目录
        self.output_dir = Path(self.config.get('output_dir', 'outputs/integrated'))
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def _get_default_config(self):
        """获取默认配置"""
        return {
            'target_companies': ['000001', '600036', '000002'],  # 平安银行、招商银行、万科A
            'target_industries': ['银行业', '房地产', '新能源汽车'],
            'macro_indicators': ['GDP', 'CPI', '利率', '汇率', 'PMI'],
            'output_dir': 'outputs/integrated',
            'output_formats': ['markdown', 'word'],
            'include_charts': True,
            'parallel_processing': True,
            'quality_threshold': 0.8
        }
    
    def setup_logging(self):
        """设置日志"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / f"integrated_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger("IntegratedResearchReportGenerator")
    
    async def run_full_pipeline(self) -> dict:
        """运行完整的研报生成流水线"""
        try:
            self.logger.info("🚀 启动整合式研报生成流水线")
            
            start_time = datetime.now()
            results = {
                'company_reports': [],
                'industry_reports': [],
                'macro_reports': [],
                'integrated_analysis': {},
                'summary': {},
                'execution_time': 0,
                'quality_scores': {}
            }
            
            # 第一阶段：并行数据收集
            print("📊 第一阶段：数据收集")
            data_collection_results = await self._parallel_data_collection()
            
            # 第二阶段：并行分析处理
            print("🔍 第二阶段：分析处理")
            analysis_results = await self._parallel_analysis_processing(data_collection_results)
            
            # 第三阶段：报告生成
            print("📝 第三阶段：报告生成")
            report_results = await self._generate_all_reports(analysis_results)
            
            # 第四阶段：整合分析
            print("🔗 第四阶段：整合分析")
            integrated_results = await self._integrated_analysis(report_results)
            
            # 第五阶段：最终输出
            print("📄 第五阶段：最终输出")
            final_results = await self._generate_final_outputs(integrated_results)
            
            # 计算执行时间
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            # 汇总结果
            results.update({
                'company_reports': report_results.get('company_reports', []),
                'industry_reports': report_results.get('industry_reports', []),
                'macro_reports': report_results.get('macro_reports', []),
                'integrated_analysis': integrated_results,
                'final_outputs': final_results,
                'execution_time': execution_time,
                'summary': {
                    'total_reports': len(report_results.get('company_reports', [])) + 
                                   len(report_results.get('industry_reports', [])) + 
                                   len(report_results.get('macro_reports', [])),
                    'execution_time': execution_time,
                    'output_dir': str(self.output_dir),
                    'generated_at': datetime.now().isoformat()
                }
            })
            
            # 保存执行结果
            await self._save_execution_summary(results)
            
            self.logger.info(f"✅ 整合式研报生成完成，耗时 {execution_time:.1f} 秒")
            return results
            
        except Exception as e:
            self.logger.error(f"整合式研报生成失败: {e}")
            return {}
    
    async def _parallel_data_collection(self) -> dict:
        """并行数据收集"""
        try:
            self.logger.info("开始并行数据收集...")
            
            # 创建并行任务
            tasks = []
            
            # 公司数据收集任务
            for company_code in self.config['target_companies']:
                task = asyncio.create_task(
                    self._collect_company_data(company_code),
                    name=f"company_data_{company_code}"
                )
                tasks.append(task)
            
            # 行业数据收集任务
            for industry in self.config['target_industries']:
                task = asyncio.create_task(
                    self._collect_industry_data(industry),
                    name=f"industry_data_{industry}"
                )
                tasks.append(task)
            
            # 宏观数据收集任务
            task = asyncio.create_task(
                self._collect_macro_data(),
                name="macro_data"
            )
            tasks.append(task)
            
            # 等待所有任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 整理结果
            data_results = {
                'company_data': {},
                'industry_data': {},
                'macro_data': {}
            }
            
            for i, result in enumerate(results):
                task_name = tasks[i].get_name()
                if not isinstance(result, Exception):
                    if task_name.startswith('company_data_'):
                        company_code = task_name.split('_')[-1]
                        data_results['company_data'][company_code] = result
                    elif task_name.startswith('industry_data_'):
                        industry = task_name.split('_', 2)[-1]
                        data_results['industry_data'][industry] = result
                    elif task_name == 'macro_data':
                        data_results['macro_data'] = result
                else:
                    self.logger.error(f"数据收集任务失败 {task_name}: {result}")
            
            self.logger.info("并行数据收集完成")
            return data_results
            
        except Exception as e:
            self.logger.error(f"并行数据收集失败: {e}")
            return {}
    
    async def _collect_company_data(self, company_code: str) -> dict:
        """收集公司数据"""
        try:
            self.logger.info(f"收集公司数据: {company_code}")
            
            # 获取公司基本信息
            company_info = get_company_info(company_code, "A")
            
            # 获取财务报表
            financial_data = get_financial_statements(company_code, "A")
            
            return {
                'company_code': company_code,
                'company_info': company_info,
                'financial_data': financial_data,
                'collected_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"收集公司数据失败 {company_code}: {e}")
            return {}
    
    async def _collect_industry_data(self, industry: str) -> dict:
        """收集行业数据"""
        try:
            self.logger.info(f"收集行业数据: {industry}")
            
            industry_info = search_industry_info(industry)
            
            return {
                'industry': industry,
                'industry_info': industry_info,
                'collected_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"收集行业数据失败 {industry}: {e}")
            return {}
    
    async def _collect_macro_data(self) -> dict:
        """收集宏观数据"""
        try:
            self.logger.info("收集宏观经济数据")
            
            macro_data = search_macro_data(self.config['macro_indicators'])
            
            return {
                'indicators': self.config['macro_indicators'],
                'macro_data': macro_data,
                'collected_at': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"收集宏观数据失败: {e}")
            return {}
    
    async def _parallel_analysis_processing(self, data_results: dict) -> dict:
        """并行分析处理"""
        try:
            self.logger.info("开始并行分析处理...")
            
            analysis_results = {
                'company_analysis': {},
                'industry_analysis': {},
                'macro_analysis': {}
            }
            
            # 公司分析
            for company_code, company_data in data_results.get('company_data', {}).items():
                if company_data:
                    analysis = await self.data_agent.analyze(
                        f"请对{company_code}公司进行全面财务分析",
                        company_data
                    )
                    analysis_results['company_analysis'][company_code] = analysis
            
            # 行业分析
            for industry, industry_data in data_results.get('industry_data', {}).items():
                if industry_data:
                    analysis = await self.data_agent.analyze(
                        f"请对{industry}进行全面行业分析",
                        industry_data
                    )
                    analysis_results['industry_analysis'][industry] = analysis
            
            # 宏观分析
            macro_data = data_results.get('macro_data', {})
            if macro_data:
                analysis = await self.data_agent.analyze(
                    "请对宏观经济数据进行全面分析",
                    macro_data
                )
                analysis_results['macro_analysis'] = analysis
            
            self.logger.info("并行分析处理完成")
            return analysis_results
            
        except Exception as e:
            self.logger.error(f"并行分析处理失败: {e}")
            return {}
    
    async def _generate_all_reports(self, analysis_results: dict) -> dict:
        """生成所有报告"""
        try:
            self.logger.info("开始生成所有报告...")
            
            report_results = {
                'company_reports': [],
                'industry_reports': [],
                'macro_reports': []
            }
            
            # 生成公司报告
            for company_code in self.config['target_companies']:
                report = await self.main_generator.generate_company_report(company_code)
                if report:
                    report_results['company_reports'].append(report)
            
            # 生成行业报告
            for industry in self.config['target_industries']:
                report = await self.main_generator.generate_industry_report(industry)
                if report:
                    report_results['industry_reports'].append(report)
            
            # 生成宏观报告
            macro_report = await self.main_generator.generate_macro_report()
            if macro_report:
                report_results['macro_reports'].append(macro_report)
            
            self.logger.info("所有报告生成完成")
            return report_results
            
        except Exception as e:
            self.logger.error(f"生成报告失败: {e}")
            return {}
    
    async def _integrated_analysis(self, report_results: dict) -> dict:
        """整合分析"""
        try:
            self.logger.info("开始整合分析...")
            
            # 跨报告关联分析
            integrated_prompt = f"""
            基于以下研报结果，进行整合分析：
            
            公司报告: {len(report_results.get('company_reports', []))} 份
            行业报告: {len(report_results.get('industry_reports', []))} 份
            宏观报告: {len(report_results.get('macro_reports', []))} 份
            
            请进行以下整合分析：
            1. 宏观-行业-公司的传导机制分析
            2. 跨行业比较分析
            3. 投资组合建议
            4. 风险因素关联分析
            5. 市场机会识别
            """
            
            integrated_analysis = await self.data_agent.analyze(integrated_prompt, report_results)
            
            self.logger.info("整合分析完成")
            return integrated_analysis
            
        except Exception as e:
            self.logger.error(f"整合分析失败: {e}")
            return {}
    
    async def _generate_final_outputs(self, integrated_results: dict) -> dict:
        """生成最终输出"""
        try:
            self.logger.info("生成最终输出...")
            
            # 生成整合报告
            final_report = await self.data_agent.generate_report(
                "生成整合式金融研究报告",
                integrated_results
            )
            
            # 保存最终报告
            final_report_file = self.output_dir / "integrated_financial_research_report.md"
            with open(final_report_file, 'w', encoding='utf-8') as f:
                f.write(final_report)
            
            outputs = {
                'final_report': str(final_report_file),
                'output_directory': str(self.output_dir),
                'generated_files': list(self.output_dir.glob('*'))
            }
            
            self.logger.info(f"最终输出完成: {final_report_file}")
            return outputs
            
        except Exception as e:
            self.logger.error(f"生成最终输出失败: {e}")
            return {}
    
    async def _save_execution_summary(self, results: dict):
        """保存执行摘要"""
        try:
            summary_file = self.output_dir / "execution_summary.json"
            with open(summary_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)
            
            self.logger.info(f"执行摘要已保存: {summary_file}")
            
        except Exception as e:
            self.logger.error(f"保存执行摘要失败: {e}")


async def main():
    """主函数"""
    try:
        print("🏆 整合式金融研报生成系统")
        print("=" * 60)
        
        # 创建生成器
        generator = IntegratedResearchReportGenerator()
        
        # 运行完整流水线
        results = await generator.run_full_pipeline()
        
        if results:
            print("\n✅ 整合式研报生成完成!")
            print(f"📊 总计生成 {results['summary']['total_reports']} 份研报")
            print(f"⏱️  执行时间: {results['summary']['execution_time']:.1f} 秒")
            print(f"📁 输出目录: {results['summary']['output_dir']}")
            
            # 显示生成的文件
            if 'final_outputs' in results:
                print(f"📄 最终报告: {results['final_outputs'].get('final_report')}")
        else:
            print("\n❌ 整合式研报生成失败")
        
    except Exception as e:
        print(f"\n❌ 系统运行失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
