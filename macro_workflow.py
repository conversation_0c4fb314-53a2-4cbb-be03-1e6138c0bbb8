#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
宏观经济研究工作流
基于PocketFlow的宏观经济分析流程
"""

import asyncio
from typing import Dict, Any, List
import logging
from datetime import datetime
from pathlib import Path

from pocketflow import Flow, flow_node
from utils.search_info import search_macro_data
from data_analysis_agent.data_analysis_agent import DataAnalysisAgent

logger = logging.getLogger(__name__)


class MacroResearchFlow:
    """宏观经济研究工作流"""
    
    def __init__(self):
        self.flow = Flow("MacroResearchFlow")
        self.data_agent = DataAnalysisAgent()
        self._setup_flow()
    
    def _setup_flow(self):
        """设置工作流"""
        # 添加工作流节点
        self.flow.add_node("collect_macro_data", self.collect_macro_data)
        self.flow.add_node("analyze_gdp", self.analyze_gdp, ["collect_macro_data"])
        self.flow.add_node("analyze_inflation", self.analyze_inflation, ["collect_macro_data"])
        self.flow.add_node("analyze_monetary_policy", self.analyze_monetary_policy, ["collect_macro_data"])
        self.flow.add_node("analyze_fiscal_policy", self.analyze_fiscal_policy, ["collect_macro_data"])
        self.flow.add_node("analyze_exchange_rate", self.analyze_exchange_rate, ["collect_macro_data"])
        self.flow.add_node("generate_market_outlook", self.generate_market_outlook,
                          ["analyze_gdp", "analyze_inflation", "analyze_monetary_policy"])
        self.flow.add_node("generate_policy_recommendations", self.generate_policy_recommendations,
                          ["analyze_fiscal_policy", "analyze_exchange_rate", "generate_market_outlook"])
        self.flow.add_node("generate_macro_report", self.generate_macro_report,
                          ["generate_market_outlook", "generate_policy_recommendations"])
    
    async def run(self, shared_data: Dict[str, Any]) -> Dict[str, Any]:
        """运行宏观经济研究工作流"""
        return await self.flow.run(shared_data)
    
    async def collect_macro_data(self, shared_data: Dict[str, Any]) -> Dict[str, Any]:
        """收集宏观经济数据"""
        try:
            indicators = shared_data.get("indicators", ["GDP", "CPI", "利率", "汇率", "PMI"])
            logger.info(f"开始收集宏观经济数据: {indicators}")
            
            # 搜索宏观数据
            macro_data = search_macro_data(indicators)
            
            # 保存到共享数据
            shared_data["macro_data"] = macro_data
            
            logger.info("宏观经济数据收集完成")
            return macro_data
            
        except Exception as e:
            logger.error(f"收集宏观经济数据失败: {e}")
            return {}
    
    async def analyze_gdp(self, shared_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析GDP"""
        try:
            macro_data = shared_data.get("macro_data", {})
            gdp_data = macro_data.get("gdp", {})
            
            logger.info("开始分析GDP")
            
            analysis_prompt = f"""
            请对GDP数据进行深度分析：
            
            GDP数据：{gdp_data}
            
            分析要点：
            1. GDP增长趋势分析
            2. GDP增长质量评估
            3. GDP增长动力分析（消费、投资、出口）
            4. 与历史同期对比
            5. 与国际水平对比
            6. 季度环比分析
            7. 未来增长预测
            8. 对经济政策的影响
            
            请生成详细的GDP分析报告和相关图表。
            """
            
            analysis_result = await self.data_agent.analyze(analysis_prompt, gdp_data)
            
            shared_data["gdp_analysis"] = analysis_result
            
            logger.info("GDP分析完成")
            return analysis_result
            
        except Exception as e:
            logger.error(f"GDP分析失败: {e}")
            return {}
    
    async def analyze_inflation(self, shared_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析通胀"""
        try:
            macro_data = shared_data.get("macro_data", {})
            cpi_data = macro_data.get("cpi", {})
            
            logger.info("开始分析通胀")
            
            analysis_prompt = f"""
            请对通胀数据进行全面分析：
            
            CPI数据：{cpi_data}
            
            分析维度：
            1. 通胀水平评估
            2. 通胀结构分析（食品、非食品、服务）
            3. 核心通胀分析
            4. 通胀预期分析
            5. 通胀驱动因素
            6. 国际通胀对比
            7. 通胀趋势预测
            8. 对货币政策的影响
            
            请重点分析通胀的持续性和政策含义。
            """
            
            analysis_result = await self.data_agent.analyze(analysis_prompt, cpi_data)
            
            shared_data["inflation_analysis"] = analysis_result
            
            logger.info("通胀分析完成")
            return analysis_result
            
        except Exception as e:
            logger.error(f"通胀分析失败: {e}")
            return {}
    
    async def analyze_monetary_policy(self, shared_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析货币政策"""
        try:
            macro_data = shared_data.get("macro_data", {})
            interest_rate_data = macro_data.get("interest_rate", {})
            money_supply_data = macro_data.get("money_supply", {})
            
            logger.info("开始分析货币政策")
            
            analysis_prompt = f"""
            请对货币政策进行深入分析：
            
            利率数据：{interest_rate_data}
            货币供应量数据：{money_supply_data}
            
            分析要点：
            1. 货币政策取向评估
            2. 利率水平分析
            3. 流动性状况分析
            4. 货币供应量增长分析
            5. 政策工具使用效果
            6. 政策传导机制分析
            7. 未来政策预期
            8. 对实体经济的影响
            
            请结合当前经济形势分析货币政策的适宜性。
            """
            
            analysis_result = await self.data_agent.analyze(analysis_prompt)
            
            shared_data["monetary_policy_analysis"] = analysis_result
            
            logger.info("货币政策分析完成")
            return analysis_result
            
        except Exception as e:
            logger.error(f"货币政策分析失败: {e}")
            return {}
    
    async def analyze_fiscal_policy(self, shared_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析财政政策"""
        try:
            macro_data = shared_data.get("macro_data", {})
            
            logger.info("开始分析财政政策")
            
            analysis_prompt = f"""
            请对财政政策进行全面分析：
            
            宏观数据：{macro_data}
            
            分析维度：
            1. 财政收支状况
            2. 财政政策取向
            3. 减税降费政策效果
            4. 政府投资分析
            5. 债务水平评估
            6. 财政可持续性
            7. 财政政策空间
            8. 财政与货币政策协调
            
            请重点分析财政政策的有效性和可持续性。
            """
            
            analysis_result = await self.data_agent.analyze(analysis_prompt)
            
            shared_data["fiscal_policy_analysis"] = analysis_result
            
            logger.info("财政政策分析完成")
            return analysis_result
            
        except Exception as e:
            logger.error(f"财政政策分析失败: {e}")
            return {}
    
    async def analyze_exchange_rate(self, shared_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析汇率"""
        try:
            macro_data = shared_data.get("macro_data", {})
            exchange_rate_data = macro_data.get("exchange_rate", {})
            
            logger.info("开始分析汇率")
            
            analysis_prompt = f"""
            请对汇率进行深入分析：
            
            汇率数据：{exchange_rate_data}
            
            分析要点：
            1. 汇率水平评估
            2. 汇率波动分析
            3. 汇率影响因素
            4. 国际收支状况
            5. 外汇储备分析
            6. 汇率政策分析
            7. 汇率预期分析
            8. 对经济的影响
            
            请分析汇率的合理性和未来走势。
            """
            
            analysis_result = await self.data_agent.analyze(analysis_prompt, exchange_rate_data)
            
            shared_data["exchange_rate_analysis"] = analysis_result
            
            logger.info("汇率分析完成")
            return analysis_result
            
        except Exception as e:
            logger.error(f"汇率分析失败: {e}")
            return {}
    
    async def generate_market_outlook(self, shared_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成市场展望"""
        try:
            gdp_analysis = shared_data.get("gdp_analysis", {})
            inflation_analysis = shared_data.get("inflation_analysis", {})
            monetary_analysis = shared_data.get("monetary_policy_analysis", {})
            
            logger.info("开始生成市场展望")
            
            analysis_prompt = f"""
            基于宏观经济分析，生成市场展望：
            
            GDP分析：{gdp_analysis}
            通胀分析：{inflation_analysis}
            货币政策分析：{monetary_analysis}
            
            市场展望要点：
            1. 经济增长前景
            2. 通胀走势预测
            3. 政策环境展望
            4. 股票市场影响
            5. 债券市场影响
            6. 商品市场影响
            7. 汇率市场影响
            8. 投资策略建议
            
            请提供具体的市场判断和投资建议。
            """
            
            analysis_result = await self.data_agent.analyze(analysis_prompt)
            
            shared_data["market_outlook"] = analysis_result
            
            logger.info("市场展望生成完成")
            return analysis_result
            
        except Exception as e:
            logger.error(f"市场展望生成失败: {e}")
            return {}
    
    async def generate_policy_recommendations(self, shared_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成政策建议"""
        try:
            fiscal_analysis = shared_data.get("fiscal_policy_analysis", {})
            exchange_analysis = shared_data.get("exchange_rate_analysis", {})
            market_outlook = shared_data.get("market_outlook", {})
            
            logger.info("开始生成政策建议")
            
            analysis_prompt = f"""
            基于宏观经济分析，提出政策建议：
            
            财政政策分析：{fiscal_analysis}
            汇率分析：{exchange_analysis}
            市场展望：{market_outlook}
            
            政策建议要点：
            1. 货币政策建议
            2. 财政政策建议
            3. 汇率政策建议
            4. 结构性政策建议
            5. 风险防控建议
            6. 改革开放建议
            7. 国际协调建议
            8. 政策实施建议
            
            请提供具体可操作的政策建议。
            """
            
            analysis_result = await self.data_agent.analyze(analysis_prompt)
            
            shared_data["policy_recommendations"] = analysis_result
            
            logger.info("政策建议生成完成")
            return analysis_result
            
        except Exception as e:
            logger.error(f"政策建议生成失败: {e}")
            return {}
    
    async def generate_macro_report(self, shared_data: Dict[str, Any]) -> str:
        """生成宏观经济研究报告"""
        try:
            logger.info("开始生成宏观经济研究报告")
            
            # 收集所有分析结果
            all_analysis = {
                "macro_data": shared_data.get("macro_data", {}),
                "gdp_analysis": shared_data.get("gdp_analysis", {}),
                "inflation_analysis": shared_data.get("inflation_analysis", {}),
                "monetary_policy_analysis": shared_data.get("monetary_policy_analysis", {}),
                "fiscal_policy_analysis": shared_data.get("fiscal_policy_analysis", {}),
                "exchange_rate_analysis": shared_data.get("exchange_rate_analysis", {}),
                "market_outlook": shared_data.get("market_outlook", {}),
                "policy_recommendations": shared_data.get("policy_recommendations", {})
            }
            
            report_prompt = f"""
            基于全面的宏观经济分析，生成宏观经济研究报告：
            
            分析结果：{all_analysis}
            
            报告结构：
            1. 执行摘要
            2. 宏观经济概况
            3. GDP分析
            4. 通胀分析
            5. 货币政策分析
            6. 财政政策分析
            7. 汇率分析
            8. 市场展望
            9. 政策建议
            10. 风险提示
            
            请生成专业、详细的宏观经济研究报告，确保分析深入、判断准确。
            """
            
            report_content = await self.data_agent.generate_report(report_prompt, all_analysis)
            
            # 保存报告
            output_dir = shared_data.get("output_dir", "outputs")
            report_file = Path(output_dir) / "宏观经济研究报告.md"
            report_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            shared_data["macro_report"] = report_content
            shared_data["report_file"] = str(report_file)
            
            logger.info(f"宏观经济研究报告生成完成: {report_file}")
            return report_content
            
        except Exception as e:
            logger.error(f"生成宏观经济研究报告失败: {e}")
            return ""


# 示例使用
if __name__ == "__main__":
    async def test_macro_workflow():
        # 创建宏观经济研究工作流
        macro_flow = MacroResearchFlow()
        
        # 设置研究参数
        shared_data = {
            "indicators": ["GDP", "CPI", "利率", "汇率", "PMI"],
            "context": [],
            "output_dir": "outputs/test"
        }
        
        # 运行工作流
        result = await macro_flow.run(shared_data)
        
        print("宏观经济研究完成")
        print(f"报告文件: {result.get('report_file')}")
    
    # 运行测试
    asyncio.run(test_macro_workflow())
