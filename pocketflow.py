#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PocketFlow - 轻量级工作流引擎
用于金融研报生成的流程编排
"""

import asyncio
from typing import Dict, Any, List, Callable, Optional
import logging
from datetime import datetime
import json

logger = logging.getLogger(__name__)


class FlowNode:
    """工作流节点"""
    
    def __init__(self, name: str, func: Callable, dependencies: List[str] = None):
        self.name = name
        self.func = func
        self.dependencies = dependencies or []
        self.status = "pending"  # pending, running, completed, failed
        self.result = None
        self.error = None
        self.start_time = None
        self.end_time = None
    
    async def execute(self, shared_data: Dict[str, Any]) -> Any:
        """执行节点"""
        try:
            self.status = "running"
            self.start_time = datetime.now()
            
            logger.info(f"开始执行节点: {self.name}")
            
            # 执行函数
            if asyncio.iscoroutinefunction(self.func):
                self.result = await self.func(shared_data)
            else:
                self.result = self.func(shared_data)
            
            self.status = "completed"
            self.end_time = datetime.now()
            
            logger.info(f"节点执行完成: {self.name}")
            return self.result
            
        except Exception as e:
            self.status = "failed"
            self.error = str(e)
            self.end_time = datetime.now()
            
            logger.error(f"节点执行失败 {self.name}: {e}")
            raise e


class Flow:
    """工作流引擎"""
    
    def __init__(self, name: str = "DefaultFlow"):
        self.name = name
        self.nodes: Dict[str, FlowNode] = {}
        self.execution_order: List[str] = []
        self.shared_data: Dict[str, Any] = {}
        self.status = "initialized"
        self.start_time = None
        self.end_time = None
    
    def add_node(self, name: str, func: Callable, dependencies: List[str] = None):
        """添加节点"""
        node = FlowNode(name, func, dependencies)
        self.nodes[name] = node
        logger.info(f"添加节点: {name}")
    
    def add_nodes(self, nodes: List[Dict[str, Any]]):
        """批量添加节点"""
        for node_config in nodes:
            self.add_node(
                name=node_config["name"],
                func=node_config["func"],
                dependencies=node_config.get("dependencies", [])
            )
    
    def _build_execution_order(self):
        """构建执行顺序（拓扑排序）"""
        visited = set()
        temp_visited = set()
        order = []
        
        def dfs(node_name: str):
            if node_name in temp_visited:
                raise ValueError(f"检测到循环依赖: {node_name}")
            
            if node_name in visited:
                return
            
            temp_visited.add(node_name)
            
            node = self.nodes[node_name]
            for dep in node.dependencies:
                if dep not in self.nodes:
                    raise ValueError(f"依赖节点不存在: {dep}")
                dfs(dep)
            
            temp_visited.remove(node_name)
            visited.add(node_name)
            order.append(node_name)
        
        for node_name in self.nodes:
            if node_name not in visited:
                dfs(node_name)
        
        self.execution_order = order
        logger.info(f"执行顺序: {' -> '.join(order)}")
    
    async def run(self, initial_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """运行工作流"""
        try:
            self.status = "running"
            self.start_time = datetime.now()
            
            # 初始化共享数据
            self.shared_data = initial_data or {}
            
            # 构建执行顺序
            self._build_execution_order()
            
            logger.info(f"开始执行工作流: {self.name}")
            
            # 按顺序执行节点
            for node_name in self.execution_order:
                node = self.nodes[node_name]
                
                # 检查依赖是否完成
                for dep in node.dependencies:
                    dep_node = self.nodes[dep]
                    if dep_node.status != "completed":
                        raise RuntimeError(f"依赖节点未完成: {dep}")
                
                # 执行节点
                result = await node.execute(self.shared_data)
                
                # 将结果添加到共享数据
                self.shared_data[f"{node_name}_result"] = result
            
            self.status = "completed"
            self.end_time = datetime.now()
            
            logger.info(f"工作流执行完成: {self.name}")
            return self.shared_data
            
        except Exception as e:
            self.status = "failed"
            self.end_time = datetime.now()
            
            logger.error(f"工作流执行失败 {self.name}: {e}")
            raise e
    
    def get_status(self) -> Dict[str, Any]:
        """获取工作流状态"""
        node_status = {}
        for name, node in self.nodes.items():
            node_status[name] = {
                "status": node.status,
                "start_time": node.start_time.isoformat() if node.start_time else None,
                "end_time": node.end_time.isoformat() if node.end_time else None,
                "error": node.error
            }
        
        return {
            "flow_name": self.name,
            "flow_status": self.status,
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "execution_order": self.execution_order,
            "nodes": node_status
        }
    
    def save_status(self, file_path: str):
        """保存工作流状态到文件"""
        status = self.get_status()
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(status, f, ensure_ascii=False, indent=2)


class ConditionalFlow(Flow):
    """条件工作流"""
    
    def __init__(self, name: str = "ConditionalFlow"):
        super().__init__(name)
        self.conditions: Dict[str, Callable] = {}
    
    def add_condition(self, name: str, condition_func: Callable):
        """添加条件"""
        self.conditions[name] = condition_func
    
    def add_conditional_node(self, name: str, func: Callable, condition: str, dependencies: List[str] = None):
        """添加条件节点"""
        if condition not in self.conditions:
            raise ValueError(f"条件不存在: {condition}")
        
        # 包装函数，添加条件检查
        async def conditional_func(shared_data: Dict[str, Any]):
            condition_func = self.conditions[condition]
            
            # 检查条件
            if asyncio.iscoroutinefunction(condition_func):
                condition_result = await condition_func(shared_data)
            else:
                condition_result = condition_func(shared_data)
            
            if condition_result:
                # 条件满足，执行原函数
                if asyncio.iscoroutinefunction(func):
                    return await func(shared_data)
                else:
                    return func(shared_data)
            else:
                # 条件不满足，跳过
                logger.info(f"条件不满足，跳过节点: {name}")
                return None
        
        self.add_node(name, conditional_func, dependencies)


class ParallelFlow(Flow):
    """并行工作流"""
    
    def __init__(self, name: str = "ParallelFlow"):
        super().__init__(name)
        self.parallel_groups: List[List[str]] = []
    
    def add_parallel_group(self, node_names: List[str]):
        """添加并行组"""
        self.parallel_groups.append(node_names)
    
    async def run(self, initial_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """运行并行工作流"""
        try:
            self.status = "running"
            self.start_time = datetime.now()
            
            # 初始化共享数据
            self.shared_data = initial_data or {}
            
            logger.info(f"开始执行并行工作流: {self.name}")
            
            # 执行并行组
            for group in self.parallel_groups:
                # 并行执行组内节点
                tasks = []
                for node_name in group:
                    if node_name in self.nodes:
                        node = self.nodes[node_name]
                        task = asyncio.create_task(node.execute(self.shared_data))
                        tasks.append((node_name, task))
                
                # 等待所有任务完成
                for node_name, task in tasks:
                    try:
                        result = await task
                        self.shared_data[f"{node_name}_result"] = result
                    except Exception as e:
                        logger.error(f"并行节点执行失败 {node_name}: {e}")
                        raise e
            
            self.status = "completed"
            self.end_time = datetime.now()
            
            logger.info(f"并行工作流执行完成: {self.name}")
            return self.shared_data
            
        except Exception as e:
            self.status = "failed"
            self.end_time = datetime.now()
            
            logger.error(f"并行工作流执行失败 {self.name}: {e}")
            raise e


# 工作流装饰器
def flow_node(name: str, dependencies: List[str] = None):
    """工作流节点装饰器"""
    def decorator(func):
        func._flow_node_name = name
        func._flow_node_dependencies = dependencies or []
        return func
    return decorator


def create_flow_from_functions(functions: List[Callable], flow_name: str = "AutoFlow") -> Flow:
    """从函数列表创建工作流"""
    flow = Flow(flow_name)
    
    for func in functions:
        if hasattr(func, '_flow_node_name'):
            name = func._flow_node_name
            dependencies = func._flow_node_dependencies
        else:
            name = func.__name__
            dependencies = []
        
        flow.add_node(name, func, dependencies)
    
    return flow


# 示例使用
if __name__ == "__main__":
    async def test_flow():
        # 创建工作流
        flow = Flow("TestFlow")
        
        # 定义节点函数
        def step1(shared_data):
            print("执行步骤1")
            shared_data["step1_data"] = "step1 completed"
            return "step1 result"
        
        def step2(shared_data):
            print("执行步骤2")
            print(f"使用step1数据: {shared_data.get('step1_data')}")
            return "step2 result"
        
        def step3(shared_data):
            print("执行步骤3")
            return "step3 result"
        
        # 添加节点
        flow.add_node("step1", step1)
        flow.add_node("step2", step2, dependencies=["step1"])
        flow.add_node("step3", step3, dependencies=["step1", "step2"])
        
        # 运行工作流
        result = await flow.run({"initial": "data"})
        
        print("工作流结果:", result)
        print("工作流状态:", flow.get_status())
    
    # 运行测试
    asyncio.run(test_flow())
