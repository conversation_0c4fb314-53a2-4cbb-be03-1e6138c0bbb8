﻿# AFAC - 智能文档分析和竞赛辅助系统
# 核心依赖包

# Web框架和API
fastapi>=0.104.1
uvicorn[standard]>=0.24.0
websockets>=12.0
starlette>=0.27.0

# 数据处理和分析
pandas>=2.1.0
numpy>=1.24.0
scipy>=1.11.0
scikit-learn>=1.3.0
matplotlib>=3.7.0
seaborn>=0.12.0
plotly>=5.17.0

# AI和机器学习
openai>=1.3.0
transformers>=4.35.0
torch>=2.1.0
sentence-transformers>=2.2.2
langchain>=0.0.350
langchain-openai>=0.0.2

# 向量数据库
chromadb>=0.4.15
faiss-cpu>=1.7.4
pinecone-client>=2.2.4

# 文档处理
pypdf2>=3.0.1
python-docx>=0.8.11
python-pptx>=0.6.21
openpyxl>=3.1.2
pillow>=10.0.0
pytesseract>=0.3.10

# 数据库和缓存
sqlalchemy>=2.0.0
redis>=5.0.0

# 金融数据
yfinance>=0.2.18
akshare>=1.12.0
fredapi>=0.5.0

# 配置和环境
pydantic>=2.5.0
pydantic-settings>=2.1.0
python-dotenv>=1.0.0
pyyaml>=6.0.1

# 日志和监控
rich>=13.7.0
loguru>=0.7.2
psutil>=5.9.0

# 额外的必需依赖
apscheduler>=3.10.0
dill>=0.3.7
passlib[bcrypt]>=1.7.4
python-jose[cryptography]>=3.3.0
python-multipart>=0.0.6
jinja2>=3.1.0

# 异步和并发
aiofiles>=23.2.0
aiohttp>=3.9.0

# 工具和实用程序
click>=8.1.0
tqdm>=4.66.0
requests>=2.31.0
httpx>=0.25.0
watchdog>=3.0.0

# 开发和测试
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.7.0
isort>=5.12.0

# 类型注解
typing-extensions>=4.8.0
annotated-types>=0.6.0

# 其他工具
markdown-it-py>=3.0.0
pygments>=2.16.0
colorama>=0.4.6
packaging>=23.2
wheel>=0.42.0

# AFAC增强版特定依赖
# 金融数据源
akshare>=1.9.0
yfinance>=0.2.0

# 图表增强
kaleido>=0.2.1
reportlab>=3.6.0

# 文本处理增强
jieba>=0.42.1
textblob>=0.17.1
snownlp>=0.12.3

# 数据库支持
sqlalchemy>=1.4.0
pymongo>=4.0.0
redis>=4.3.0

# 文档处理增强
PyPDF2>=2.10.0

# 数学计算
sympy>=1.10.0

# 监控和性能
prometheus-client>=0.14.0
psutil>=5.9.0

# 安全
cryptography>=37.0.0
bcrypt>=3.2.0

# 可选依赖（用于完整功能，需要手动安装）
# opencv-python>=4.6.0  # 图像处理
# talib>=0.4.25         # 技术分析
# feedparser>=6.0.10    # RSS解析
# tushare>=1.2.89       # 金融数据
# tweepy>=4.14.0        # Twitter API
# praw>=7.6.0           # Reddit API
# newspaper3k>=0.2.8    # 新闻解析
