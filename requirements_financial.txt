# 金融研报自动生成系统依赖包
# Financial Research Report Generation System Dependencies

# Core dependencies for financial research report generation
openai>=1.0.0
requests>=2.28.0
pandas>=1.5.0
numpy>=1.21.0

# Financial data sources
akshare>=1.9.0
yfinance>=0.2.0

# Data visualization
matplotlib>=3.6.0
seaborn>=0.12.0
plotly>=5.11.0

# Document processing
python-docx>=0.8.11
openpyxl>=3.0.10

# Web scraping for data collection
beautifulsoup4>=4.11.0
lxml>=4.9.0
requests-html>=0.10.0

# Async support
aiohttp>=3.8.0

# Utilities
python-dotenv>=1.0.0
pydantic>=2.0.0
loguru>=0.7.0
tqdm>=4.64.0

# Chinese text processing
jieba>=0.42.0

# Financial analysis
scipy>=1.9.0
scikit-learn>=1.1.0

# Time series analysis
statsmodels>=0.13.0

# Configuration
pyyaml>=6.0

# Image processing for charts
Pillow>=9.3.0

# Development and testing
pytest>=7.2.0
pytest-asyncio>=0.21.0
black>=23.0.0

# Database support
sqlalchemy>=2.0.0

# HTTP client
httpx>=0.24.0

# Retry mechanisms
tenacity>=8.2.0

# Caching
diskcache>=5.4.0

# Optional: Enhanced AI capabilities (uncomment if needed)
# transformers>=4.21.0
# torch>=1.13.0
# sentence-transformers>=2.2.0

# Optional: Advanced financial analysis (uncomment if needed)
# quantlib>=1.29
# ta-lib>=0.4.0
# xgboost>=1.7.0
# lightgbm>=3.3.0
