#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AFAC赛题四主运行脚本
智能体赋能的金融多模态报告自动化生成系统
一键运行生成三类研报：公司研报、行业研报、宏观研报
"""

import asyncio
import sys
import os
import argparse
from pathlib import Path
from datetime import datetime
import json
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from src.agents.competition_system import CompetitionSystem, CompetitionConfig
    from src.reports.word_report_generator import WordReportGenerator
    from src.utils.logger import setup_logging
except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保所有依赖已正确安装")
    sys.exit(1)


def setup_competition_logging():
    """设置竞赛日志"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / f"competition_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='AFAC赛题四金融研报生成系统')
    
    parser.add_argument('--companies', nargs='+', default=['000001', '000002', '600036'],
                       help='目标公司代码列表 (默认: 000001 000002 600036)')
    
    parser.add_argument('--industries', nargs='+', default=['银行业', '房地产', '新能源汽车'],
                       help='目标行业列表 (默认: 银行业 房地产 新能源汽车)')
    
    parser.add_argument('--macro-indicators', nargs='+', 
                       default=['GDP', 'CPI', '利率', '汇率', 'PMI', '货币供应量'],
                       help='宏观指标列表')
    
    parser.add_argument('--output-dir', default='output/competition_results',
                       help='输出目录 (默认: output/competition_results)')
    
    parser.add_argument('--mode', choices=['all', 'company', 'industry', 'macro'], default='all',
                       help='运行模式 (默认: all)')
    
    parser.add_argument('--quality-threshold', type=float, default=0.8,
                       help='质量阈值 (默认: 0.8)')
    
    parser.add_argument('--generate-charts', action='store_true', default=True,
                       help='是否生成图表 (默认: True)')
    
    parser.add_argument('--use-opensource-only', action='store_true', default=True,
                       help='仅使用开源模型 (默认: True)')
    
    return parser.parse_args()


async def run_competition_system(args):
    """运行竞赛系统"""
    try:
        print("🏆 AFAC赛题四 - 智能体赋能的金融多模态报告自动化生成系统")
        print("=" * 80)
        
        # 创建配置
        config = CompetitionConfig(
            target_companies=args.companies,
            target_industries=args.industries,
            macro_indicators=args.macro_indicators,
            output_dir=args.output_dir,
            use_opensource_only=args.use_opensource_only,
            generate_charts=args.generate_charts,
            quality_threshold=args.quality_threshold
        )
        
        print(f"📊 系统配置:")
        print(f"   目标公司: {', '.join(config.target_companies)}")
        print(f"   目标行业: {', '.join(config.target_industries)}")
        print(f"   宏观指标: {', '.join(config.macro_indicators)}")
        print(f"   输出目录: {config.output_dir}")
        print(f"   运行模式: {args.mode}")
        print(f"   质量阈值: {config.quality_threshold}")
        print("")
        
        # 初始化系统
        print("🚀 初始化竞赛系统...")
        competition_system = CompetitionSystem(config)
        word_generator = WordReportGenerator(config.output_dir)
        print("✅ 系统初始化完成")
        print("")
        
        results = {
            'company_reports': [],
            'industry_reports': [],
            'macro_reports': [],
            'generated_files': [],
            'quality_scores': {},
            'execution_summary': {}
        }
        
        start_time = datetime.now()
        
        # 根据模式运行
        if args.mode in ['all', 'company']:
            print("📈 生成公司研报...")
            print("-" * 50)
            
            for i, symbol in enumerate(config.target_companies, 1):
                print(f"🎯 [{i}/{len(config.target_companies)}] 处理公司: {symbol}")
                
                try:
                    company_report = await competition_system.generate_company_report(symbol)
                    
                    if company_report:
                        results['company_reports'].append(company_report)
                        
                        # 生成Word文档
                        word_file = word_generator.generate_company_report(company_report, symbol)
                        if word_file:
                            results['generated_files'].append(word_file)
                            print(f"   ✅ 公司 {symbol} 研报生成成功: {Path(word_file).name}")
                        
                        # 记录质量评分
                        quality_audit = company_report.get('quality_audit', {})
                        if quality_audit:
                            results['quality_scores'][f'company_{symbol}'] = quality_audit.get('overall_score', 0)
                    else:
                        print(f"   ❌ 公司 {symbol} 研报生成失败")
                        
                except Exception as e:
                    print(f"   ❌ 公司 {symbol} 处理失败: {e}")
            
            print("")
        
        if args.mode in ['all', 'industry']:
            print("🏭 生成行业研报...")
            print("-" * 50)
            
            for i, industry in enumerate(config.target_industries, 1):
                print(f"🎯 [{i}/{len(config.target_industries)}] 处理行业: {industry}")
                
                try:
                    industry_report = await competition_system.generate_industry_report(industry)
                    
                    if industry_report:
                        results['industry_reports'].append(industry_report)
                        
                        # 生成Word文档
                        word_file = word_generator.generate_industry_report(industry_report, industry)
                        if word_file:
                            results['generated_files'].append(word_file)
                            print(f"   ✅ 行业 {industry} 研报生成成功: {Path(word_file).name}")
                        
                        # 记录质量评分
                        quality_audit = industry_report.get('quality_audit', {})
                        if quality_audit:
                            results['quality_scores'][f'industry_{industry}'] = quality_audit.get('overall_score', 0)
                    else:
                        print(f"   ❌ 行业 {industry} 研报生成失败")
                        
                except Exception as e:
                    print(f"   ❌ 行业 {industry} 处理失败: {e}")
            
            print("")
        
        if args.mode in ['all', 'macro']:
            print("🌍 生成宏观经济研报...")
            print("-" * 50)
            
            try:
                print("🎯 处理宏观经济指标...")
                macro_report = await competition_system.generate_macro_report()
                
                if macro_report:
                    results['macro_reports'].append(macro_report)
                    
                    # 生成Word文档
                    word_file = word_generator.generate_macro_report(macro_report)
                    if word_file:
                        results['generated_files'].append(word_file)
                        print(f"   ✅ 宏观经济研报生成成功: {Path(word_file).name}")
                    
                    # 记录质量评分
                    quality_audit = macro_report.get('quality_audit', {})
                    if quality_audit:
                        results['quality_scores']['macro_economy'] = quality_audit.get('overall_score', 0)
                else:
                    print("   ❌ 宏观经济研报生成失败")
                    
            except Exception as e:
                print(f"   ❌ 宏观经济处理失败: {e}")
            
            print("")
        
        # 执行总结
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        
        results['execution_summary'] = {
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'execution_time_seconds': execution_time,
            'total_reports': len(results['company_reports']) + len(results['industry_reports']) + len(results['macro_reports']),
            'company_count': len(results['company_reports']),
            'industry_count': len(results['industry_reports']),
            'macro_count': len(results['macro_reports']),
            'generated_files_count': len(results['generated_files']),
            'average_quality_score': sum(results['quality_scores'].values()) / len(results['quality_scores']) if results['quality_scores'] else 0
        }
        
        # 保存执行结果
        results_file = Path(config.output_dir) / "execution_results.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)
        
        # 显示最终结果
        print("🎉 竞赛系统执行完成!")
        print("=" * 80)
        
        summary = results['execution_summary']
        print(f"📊 执行统计:")
        print(f"   总执行时间: {summary['execution_time_seconds']:.1f} 秒")
        print(f"   生成报告数: {summary['total_reports']}")
        print(f"   公司研报: {summary['company_count']}")
        print(f"   行业研报: {summary['industry_count']}")
        print(f"   宏观研报: {summary['macro_count']}")
        print(f"   Word文档: {summary['generated_files_count']}")
        print(f"   平均质量: {summary['average_quality_score']:.1f}/100")
        print("")
        
        print(f"📁 生成文件:")
        for file_path in results['generated_files']:
            print(f"   📄 {Path(file_path).name}")
        print("")
        
        print(f"🎯 竞赛提交文件:")
        output_dir = Path(config.output_dir)
        submission_files = [
            "Company_Research_Report.docx",
            "Industry_Research_Report.docx", 
            "Macro_Research_Report.docx"
        ]
        
        for filename in submission_files:
            file_path = output_dir / filename
            if file_path.exists():
                print(f"   ✅ {filename}")
            else:
                # 查找类似文件
                similar_files = list(output_dir.glob(f"*{filename.split('_')[0]}*{filename.split('_')[1]}*.docx"))
                if similar_files:
                    print(f"   📄 {similar_files[0].name} (类似文件)")
                else:
                    print(f"   ❌ {filename} (未找到)")
        
        print("")
        print("🌟 AFAC赛题四解决方案执行完成!")
        print("🎯 系统已准备就绪，可用于正式竞赛提交!")
        
        return results
        
    except Exception as e:
        print(f"\n❌ 竞赛系统执行失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def main():
    """主函数"""
    try:
        # 设置日志
        setup_competition_logging()
        
        # 解析参数
        args = parse_arguments()
        
        # 运行系统
        results = asyncio.run(run_competition_system(args))
        
        if results:
            print(f"\n🎊 竞赛系统执行成功!")
            return 0
        else:
            print(f"\n⚠️  竞赛系统执行失败")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️  执行被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 系统运行失败: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
