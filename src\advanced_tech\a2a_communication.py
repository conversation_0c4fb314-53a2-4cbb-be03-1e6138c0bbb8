#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A2A (Agent-to-Agent) 通信系统
实现智能Agent间的直接通信、协作和知识共享
"""

import asyncio
import logging
import json
import uuid
from typing import Dict, List, Any, Optional, Union, Callable, Set
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import threading
from abc import ABC, abstractmethod


class A2AMessageType(Enum):
    """A2A消息类型"""
    TASK_REQUEST = "task_request"
    TASK_RESPONSE = "task_response"
    KNOWLEDGE_SHARE = "knowledge_share"
    CAPABILITY_QUERY = "capability_query"
    CAPABILITY_RESPONSE = "capability_response"
    COLLABORATION_INVITE = "collaboration_invite"
    COLLABORATION_ACCEPT = "collaboration_accept"
    COLLABORATION_REJECT = "collaboration_reject"
    STATUS_UPDATE = "status_update"
    HEARTBEAT = "heartbeat"
    ERROR = "error"


class AgentCapability(Enum):
    """Agent能力类型"""
    DATA_COLLECTION = "data_collection"
    FINANCIAL_ANALYSIS = "financial_analysis"
    REPORT_GENERATION = "report_generation"
    QUALITY_AUDIT = "quality_audit"
    RISK_ASSESSMENT = "risk_assessment"
    MARKET_RESEARCH = "market_research"
    TECHNICAL_ANALYSIS = "technical_analysis"
    FUNDAMENTAL_ANALYSIS = "fundamental_analysis"
    SENTIMENT_ANALYSIS = "sentiment_analysis"
    VISUALIZATION = "visualization"


@dataclass
class A2AMessage:
    """A2A消息"""
    message_id: str
    message_type: A2AMessageType
    sender_id: str
    receiver_id: Optional[str]
    payload: Dict[str, Any]
    priority: int = 5  # 1-10, 10最高
    timestamp: datetime = field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    correlation_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'message_id': self.message_id,
            'message_type': self.message_type.value,
            'sender_id': self.sender_id,
            'receiver_id': self.receiver_id,
            'payload': self.payload,
            'priority': self.priority,
            'timestamp': self.timestamp.isoformat(),
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'correlation_id': self.correlation_id
        }
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.expires_at is None:
            return False
        return datetime.now() > self.expires_at


@dataclass
class AgentProfile:
    """Agent配置文件"""
    agent_id: str
    agent_type: str
    capabilities: Set[AgentCapability]
    specializations: List[str]
    performance_metrics: Dict[str, float]
    availability: bool = True
    max_concurrent_tasks: int = 5
    current_load: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'agent_id': self.agent_id,
            'agent_type': self.agent_type,
            'capabilities': [cap.value for cap in self.capabilities],
            'specializations': self.specializations,
            'performance_metrics': self.performance_metrics,
            'availability': self.availability,
            'max_concurrent_tasks': self.max_concurrent_tasks,
            'current_load': self.current_load
        }


class A2AAgent(ABC):
    """A2A Agent抽象基类"""
    
    def __init__(self, agent_id: str, agent_type: str, capabilities: Set[AgentCapability]):
        """
        初始化A2A Agent
        
        Args:
            agent_id: Agent ID
            agent_type: Agent类型
            capabilities: Agent能力集合
        """
        self.profile = AgentProfile(
            agent_id=agent_id,
            agent_type=agent_type,
            capabilities=capabilities,
            specializations=[],
            performance_metrics={}
        )
        
        self.logger = logging.getLogger(f"{__name__}.{agent_id}")
        self.message_queue = asyncio.Queue()
        self.connections = {}
        self.knowledge_base = {}
        self.collaboration_sessions = {}
        self.running = False
        
        # 消息处理器
        self.message_handlers = {
            A2AMessageType.TASK_REQUEST: self._handle_task_request,
            A2AMessageType.KNOWLEDGE_SHARE: self._handle_knowledge_share,
            A2AMessageType.CAPABILITY_QUERY: self._handle_capability_query,
            A2AMessageType.COLLABORATION_INVITE: self._handle_collaboration_invite,
            A2AMessageType.STATUS_UPDATE: self._handle_status_update,
            A2AMessageType.HEARTBEAT: self._handle_heartbeat
        }
    
    @abstractmethod
    async def execute_task(self, task_type: str, **kwargs) -> Dict[str, Any]:
        """执行任务"""
        pass
    
    async def start(self) -> None:
        """启动Agent"""
        self.running = True
        asyncio.create_task(self._message_processor())
        self.logger.info(f"Agent {self.profile.agent_id} 已启动")
    
    async def stop(self) -> None:
        """停止Agent"""
        self.running = False
        self.logger.info(f"Agent {self.profile.agent_id} 已停止")
    
    async def _message_processor(self) -> None:
        """消息处理器"""
        while self.running:
            try:
                # 等待消息，超时1秒
                message = await asyncio.wait_for(self.message_queue.get(), timeout=1.0)
                
                # 检查消息是否过期
                if message.is_expired():
                    self.logger.warning(f"消息已过期: {message.message_id}")
                    continue
                
                # 处理消息
                await self._process_message(message)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"消息处理错误: {e}")
    
    async def _process_message(self, message: A2AMessage) -> None:
        """处理消息"""
        try:
            handler = self.message_handlers.get(message.message_type)
            if handler:
                response = await handler(message)
                if response:
                    await self.send_message(response)
            else:
                self.logger.warning(f"未知消息类型: {message.message_type}")
                
        except Exception as e:
            self.logger.error(f"处理消息失败: {e}")
            # 发送错误响应
            error_response = A2AMessage(
                message_id=str(uuid.uuid4()),
                message_type=A2AMessageType.ERROR,
                sender_id=self.profile.agent_id,
                receiver_id=message.sender_id,
                payload={'error': str(e), 'original_message_id': message.message_id},
                correlation_id=message.correlation_id
            )
            await self.send_message(error_response)
    
    async def send_message(self, message: A2AMessage) -> None:
        """发送消息"""
        if message.receiver_id:
            # 点对点消息
            if message.receiver_id in self.connections:
                target_agent = self.connections[message.receiver_id]
                await target_agent.receive_message(message)
            else:
                self.logger.warning(f"未找到目标Agent: {message.receiver_id}")
        else:
            # 广播消息
            for agent_id, agent in self.connections.items():
                if agent_id != self.profile.agent_id:
                    await agent.receive_message(message)
    
    async def receive_message(self, message: A2AMessage) -> None:
        """接收消息"""
        await self.message_queue.put(message)
    
    def connect_to(self, other_agent: 'A2AAgent') -> None:
        """连接到其他Agent"""
        self.connections[other_agent.profile.agent_id] = other_agent
        other_agent.connections[self.profile.agent_id] = self
        self.logger.info(f"已连接到Agent: {other_agent.profile.agent_id}")
    
    async def request_task(self, target_agent_id: str, task_type: str, 
                          task_data: Dict[str, Any], priority: int = 5) -> Dict[str, Any]:
        """请求其他Agent执行任务"""
        try:
            message = A2AMessage(
                message_id=str(uuid.uuid4()),
                message_type=A2AMessageType.TASK_REQUEST,
                sender_id=self.profile.agent_id,
                receiver_id=target_agent_id,
                payload={
                    'task_type': task_type,
                    'task_data': task_data,
                    'requester_capabilities': [cap.value for cap in self.profile.capabilities]
                },
                priority=priority,
                expires_at=datetime.now() + timedelta(minutes=30)
            )
            
            await self.send_message(message)
            
            # 简化返回（实际应该等待响应）
            return {
                'success': True,
                'message_id': message.message_id,
                'status': 'request_sent'
            }
            
        except Exception as e:
            self.logger.error(f"任务请求失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def share_knowledge(self, knowledge_type: str, knowledge_data: Dict[str, Any],
                            target_agent_id: Optional[str] = None) -> None:
        """分享知识"""
        try:
            message = A2AMessage(
                message_id=str(uuid.uuid4()),
                message_type=A2AMessageType.KNOWLEDGE_SHARE,
                sender_id=self.profile.agent_id,
                receiver_id=target_agent_id,  # None表示广播
                payload={
                    'knowledge_type': knowledge_type,
                    'knowledge_data': knowledge_data,
                    'source_agent': self.profile.agent_id,
                    'timestamp': datetime.now().isoformat()
                }
            )
            
            await self.send_message(message)
            self.logger.info(f"知识分享: {knowledge_type}")
            
        except Exception as e:
            self.logger.error(f"知识分享失败: {e}")
    
    async def query_capabilities(self, target_agent_id: Optional[str] = None) -> Dict[str, Any]:
        """查询Agent能力"""
        try:
            message = A2AMessage(
                message_id=str(uuid.uuid4()),
                message_type=A2AMessageType.CAPABILITY_QUERY,
                sender_id=self.profile.agent_id,
                receiver_id=target_agent_id,
                payload={'query_type': 'capabilities'}
            )
            
            await self.send_message(message)
            
            return {
                'success': True,
                'message_id': message.message_id
            }
            
        except Exception as e:
            self.logger.error(f"能力查询失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def invite_collaboration(self, target_agent_id: str, 
                                 collaboration_type: str, 
                                 collaboration_data: Dict[str, Any]) -> Dict[str, Any]:
        """邀请协作"""
        try:
            session_id = str(uuid.uuid4())
            
            message = A2AMessage(
                message_id=str(uuid.uuid4()),
                message_type=A2AMessageType.COLLABORATION_INVITE,
                sender_id=self.profile.agent_id,
                receiver_id=target_agent_id,
                payload={
                    'session_id': session_id,
                    'collaboration_type': collaboration_type,
                    'collaboration_data': collaboration_data,
                    'initiator': self.profile.agent_id
                }
            )
            
            await self.send_message(message)
            
            # 创建协作会话
            self.collaboration_sessions[session_id] = {
                'type': collaboration_type,
                'participants': [self.profile.agent_id, target_agent_id],
                'status': 'pending',
                'created_at': datetime.now(),
                'data': collaboration_data
            }
            
            return {
                'success': True,
                'session_id': session_id,
                'status': 'invitation_sent'
            }
            
        except Exception as e:
            self.logger.error(f"协作邀请失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    # 消息处理器方法
    async def _handle_task_request(self, message: A2AMessage) -> Optional[A2AMessage]:
        """处理任务请求"""
        try:
            payload = message.payload
            task_type = payload.get('task_type')
            task_data = payload.get('task_data', {})
            
            # 检查是否有能力处理该任务
            if not self._can_handle_task(task_type):
                return A2AMessage(
                    message_id=str(uuid.uuid4()),
                    message_type=A2AMessageType.TASK_RESPONSE,
                    sender_id=self.profile.agent_id,
                    receiver_id=message.sender_id,
                    payload={
                        'success': False,
                        'error': f'无法处理任务类型: {task_type}',
                        'original_message_id': message.message_id
                    },
                    correlation_id=message.correlation_id
                )
            
            # 检查负载
            if self.profile.current_load >= self.profile.max_concurrent_tasks:
                return A2AMessage(
                    message_id=str(uuid.uuid4()),
                    message_type=A2AMessageType.TASK_RESPONSE,
                    sender_id=self.profile.agent_id,
                    receiver_id=message.sender_id,
                    payload={
                        'success': False,
                        'error': '当前负载过高，无法接受新任务',
                        'current_load': self.profile.current_load,
                        'max_load': self.profile.max_concurrent_tasks
                    },
                    correlation_id=message.correlation_id
                )
            
            # 执行任务
            self.profile.current_load += 1
            try:
                result = await self.execute_task(task_type, **task_data)
                
                return A2AMessage(
                    message_id=str(uuid.uuid4()),
                    message_type=A2AMessageType.TASK_RESPONSE,
                    sender_id=self.profile.agent_id,
                    receiver_id=message.sender_id,
                    payload={
                        'success': True,
                        'result': result,
                        'task_type': task_type,
                        'execution_time': 1.5  # 模拟执行时间
                    },
                    correlation_id=message.correlation_id
                )
                
            finally:
                self.profile.current_load -= 1
                
        except Exception as e:
            self.profile.current_load = max(0, self.profile.current_load - 1)
            self.logger.error(f"任务执行失败: {e}")
            return A2AMessage(
                message_id=str(uuid.uuid4()),
                message_type=A2AMessageType.TASK_RESPONSE,
                sender_id=self.profile.agent_id,
                receiver_id=message.sender_id,
                payload={
                    'success': False,
                    'error': str(e)
                },
                correlation_id=message.correlation_id
            )
    
    async def _handle_knowledge_share(self, message: A2AMessage) -> None:
        """处理知识分享"""
        try:
            payload = message.payload
            knowledge_type = payload.get('knowledge_type')
            knowledge_data = payload.get('knowledge_data')
            
            # 存储知识
            if knowledge_type not in self.knowledge_base:
                self.knowledge_base[knowledge_type] = []
            
            self.knowledge_base[knowledge_type].append({
                'data': knowledge_data,
                'source': payload.get('source_agent'),
                'timestamp': payload.get('timestamp'),
                'received_at': datetime.now().isoformat()
            })
            
            self.logger.info(f"接收到知识分享: {knowledge_type} from {payload.get('source_agent')}")
            
        except Exception as e:
            self.logger.error(f"处理知识分享失败: {e}")
    
    async def _handle_capability_query(self, message: A2AMessage) -> A2AMessage:
        """处理能力查询"""
        return A2AMessage(
            message_id=str(uuid.uuid4()),
            message_type=A2AMessageType.CAPABILITY_RESPONSE,
            sender_id=self.profile.agent_id,
            receiver_id=message.sender_id,
            payload={
                'agent_profile': self.profile.to_dict(),
                'knowledge_types': list(self.knowledge_base.keys()),
                'active_collaborations': len(self.collaboration_sessions)
            },
            correlation_id=message.correlation_id
        )
    
    async def _handle_collaboration_invite(self, message: A2AMessage) -> A2AMessage:
        """处理协作邀请"""
        try:
            payload = message.payload
            session_id = payload.get('session_id')
            collaboration_type = payload.get('collaboration_type')
            
            # 简化决策逻辑：如果负载不高就接受
            if self.profile.current_load < self.profile.max_concurrent_tasks * 0.8:
                # 接受协作
                self.collaboration_sessions[session_id] = {
                    'type': collaboration_type,
                    'participants': [message.sender_id, self.profile.agent_id],
                    'status': 'active',
                    'joined_at': datetime.now(),
                    'data': payload.get('collaboration_data', {})
                }
                
                return A2AMessage(
                    message_id=str(uuid.uuid4()),
                    message_type=A2AMessageType.COLLABORATION_ACCEPT,
                    sender_id=self.profile.agent_id,
                    receiver_id=message.sender_id,
                    payload={
                        'session_id': session_id,
                        'status': 'accepted',
                        'participant_capabilities': [cap.value for cap in self.profile.capabilities]
                    },
                    correlation_id=message.correlation_id
                )
            else:
                # 拒绝协作
                return A2AMessage(
                    message_id=str(uuid.uuid4()),
                    message_type=A2AMessageType.COLLABORATION_REJECT,
                    sender_id=self.profile.agent_id,
                    receiver_id=message.sender_id,
                    payload={
                        'session_id': session_id,
                        'status': 'rejected',
                        'reason': '当前负载过高'
                    },
                    correlation_id=message.correlation_id
                )
                
        except Exception as e:
            self.logger.error(f"处理协作邀请失败: {e}")
            return A2AMessage(
                message_id=str(uuid.uuid4()),
                message_type=A2AMessageType.ERROR,
                sender_id=self.profile.agent_id,
                receiver_id=message.sender_id,
                payload={'error': str(e)},
                correlation_id=message.correlation_id
            )
    
    async def _handle_status_update(self, message: A2AMessage) -> None:
        """处理状态更新"""
        try:
            payload = message.payload
            self.logger.info(f"收到状态更新 from {message.sender_id}: {payload}")
        except Exception as e:
            self.logger.error(f"处理状态更新失败: {e}")
    
    async def _handle_heartbeat(self, message: A2AMessage) -> A2AMessage:
        """处理心跳"""
        return A2AMessage(
            message_id=str(uuid.uuid4()),
            message_type=A2AMessageType.HEARTBEAT,
            sender_id=self.profile.agent_id,
            receiver_id=message.sender_id,
            payload={
                'status': 'alive',
                'load': self.profile.current_load,
                'timestamp': datetime.now().isoformat()
            }
        )
    
    def _can_handle_task(self, task_type: str) -> bool:
        """检查是否能处理任务"""
        # 简化逻辑：检查任务类型是否匹配Agent能力
        task_capability_mapping = {
            'collect_data': AgentCapability.DATA_COLLECTION,
            'analyze_financial': AgentCapability.FINANCIAL_ANALYSIS,
            'generate_report': AgentCapability.REPORT_GENERATION,
            'audit_quality': AgentCapability.QUALITY_AUDIT,
            'assess_risk': AgentCapability.RISK_ASSESSMENT
        }
        
        required_capability = task_capability_mapping.get(task_type)
        return required_capability in self.profile.capabilities if required_capability else True
    
    def get_status(self) -> Dict[str, Any]:
        """获取Agent状态"""
        return {
            'profile': self.profile.to_dict(),
            'connections': list(self.connections.keys()),
            'knowledge_base_size': {k: len(v) for k, v in self.knowledge_base.items()},
            'active_collaborations': len(self.collaboration_sessions),
            'message_queue_size': self.message_queue.qsize(),
            'running': self.running
        }


class A2ANetwork:
    """A2A网络管理器"""

    def __init__(self):
        """初始化A2A网络"""
        self.logger = logging.getLogger(__name__)
        self.agents = {}
        self.message_router = {}
        self.network_stats = {
            'total_messages': 0,
            'successful_tasks': 0,
            'failed_tasks': 0,
            'knowledge_shares': 0,
            'active_collaborations': 0
        }

    def register_agent(self, agent: A2AAgent) -> None:
        """注册Agent到网络"""
        self.agents[agent.profile.agent_id] = agent

        # 连接到其他Agent
        for other_agent in self.agents.values():
            if other_agent.profile.agent_id != agent.profile.agent_id:
                agent.connect_to(other_agent)

        self.logger.info(f"Agent {agent.profile.agent_id} 已注册到网络")

    def unregister_agent(self, agent_id: str) -> None:
        """从网络注销Agent"""
        if agent_id in self.agents:
            del self.agents[agent_id]
            self.logger.info(f"Agent {agent_id} 已从网络注销")

    async def broadcast_message(self, message: A2AMessage) -> None:
        """广播消息"""
        for agent in self.agents.values():
            if agent.profile.agent_id != message.sender_id:
                await agent.receive_message(message)

        self.network_stats['total_messages'] += 1

    def find_agents_by_capability(self, capability: AgentCapability) -> List[str]:
        """根据能力查找Agent"""
        matching_agents = []
        for agent in self.agents.values():
            if capability in agent.profile.capabilities:
                matching_agents.append(agent.profile.agent_id)
        return matching_agents

    def get_network_topology(self) -> Dict[str, Any]:
        """获取网络拓扑"""
        topology = {
            'nodes': [],
            'edges': [],
            'statistics': self.network_stats
        }

        for agent in self.agents.values():
            topology['nodes'].append({
                'id': agent.profile.agent_id,
                'type': agent.profile.agent_type,
                'capabilities': [cap.value for cap in agent.profile.capabilities],
                'load': agent.profile.current_load,
                'availability': agent.profile.availability
            })

            # 添加连接边
            for connected_agent_id in agent.connections.keys():
                topology['edges'].append({
                    'source': agent.profile.agent_id,
                    'target': connected_agent_id,
                    'type': 'connection'
                })

        return topology

    async def optimize_network(self) -> Dict[str, Any]:
        """优化网络性能"""
        optimization_results = {
            'load_balancing': [],
            'capability_gaps': [],
            'performance_improvements': []
        }

        # 负载均衡分析
        high_load_agents = []
        low_load_agents = []

        for agent in self.agents.values():
            load_ratio = agent.profile.current_load / agent.profile.max_concurrent_tasks
            if load_ratio > 0.8:
                high_load_agents.append(agent.profile.agent_id)
            elif load_ratio < 0.2:
                low_load_agents.append(agent.profile.agent_id)

        if high_load_agents:
            optimization_results['load_balancing'].append(f"高负载Agent: {high_load_agents}")
        if low_load_agents:
            optimization_results['load_balancing'].append(f"低负载Agent: {low_load_agents}")

        # 能力缺口分析
        all_capabilities = set(AgentCapability)
        network_capabilities = set()
        for agent in self.agents.values():
            network_capabilities.update(agent.profile.capabilities)

        missing_capabilities = all_capabilities - network_capabilities
        if missing_capabilities:
            optimization_results['capability_gaps'] = [cap.value for cap in missing_capabilities]

        return optimization_results
