#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级工具调用系统
实现智能工具发现、动态调用、结果验证和工具链编排
"""

import asyncio
import logging
import json
import inspect
import uuid
from typing import Dict, List, Any, Optional, Union, Callable, Type, Tuple
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
from abc import ABC, abstractmethod
import importlib
import traceback


class ToolType(Enum):
    """工具类型"""
    DATA_SOURCE = "data_source"
    ANALYSIS = "analysis"
    VISUALIZATION = "visualization"
    CALCULATION = "calculation"
    VALIDATION = "validation"
    TRANSFORMATION = "transformation"
    COMMUNICATION = "communication"
    UTILITY = "utility"


class ToolStatus(Enum):
    """工具状态"""
    AVAILABLE = "available"
    BUSY = "busy"
    ERROR = "error"
    MAINTENANCE = "maintenance"
    DEPRECATED = "deprecated"


@dataclass
class ToolParameter:
    """工具参数定义"""
    name: str
    param_type: Type
    required: bool = True
    default_value: Any = None
    description: str = ""
    validation_rules: List[str] = field(default_factory=list)
    
    def validate(self, value: Any) -> Tuple[bool, str]:
        """验证参数值"""
        try:
            # 类型检查
            if not isinstance(value, self.param_type):
                try:
                    value = self.param_type(value)
                except (ValueError, TypeError):
                    return False, f"参数 {self.name} 类型错误，期望 {self.param_type.__name__}"
            
            # 验证规则检查
            for rule in self.validation_rules:
                if not self._check_rule(value, rule):
                    return False, f"参数 {self.name} 不满足验证规则: {rule}"
            
            return True, ""
            
        except Exception as e:
            return False, f"参数验证失败: {str(e)}"
    
    def _check_rule(self, value: Any, rule: str) -> bool:
        """检查验证规则"""
        try:
            if rule.startswith("min:"):
                min_val = float(rule.split(":")[1])
                return value >= min_val
            elif rule.startswith("max:"):
                max_val = float(rule.split(":")[1])
                return value <= max_val
            elif rule.startswith("length:"):
                length = int(rule.split(":")[1])
                return len(str(value)) <= length
            elif rule == "positive":
                return value > 0
            elif rule == "non_empty":
                return bool(value)
            else:
                return True
        except:
            return False


@dataclass
class ToolMetadata:
    """工具元数据"""
    tool_id: str
    name: str
    description: str
    tool_type: ToolType
    version: str
    author: str
    parameters: List[ToolParameter]
    return_type: Type
    status: ToolStatus = ToolStatus.AVAILABLE
    dependencies: List[str] = field(default_factory=list)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    usage_count: int = 0
    last_used: Optional[datetime] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'tool_id': self.tool_id,
            'name': self.name,
            'description': self.description,
            'tool_type': self.tool_type.value,
            'version': self.version,
            'author': self.author,
            'parameters': [
                {
                    'name': p.name,
                    'type': p.param_type.__name__,
                    'required': p.required,
                    'description': p.description,
                    'validation_rules': p.validation_rules
                }
                for p in self.parameters
            ],
            'return_type': self.return_type.__name__,
            'status': self.status.value,
            'dependencies': self.dependencies,
            'performance_metrics': self.performance_metrics,
            'usage_count': self.usage_count,
            'last_used': self.last_used.isoformat() if self.last_used else None
        }


class BaseTool(ABC):
    """工具基类"""
    
    def __init__(self, metadata: ToolMetadata):
        """
        初始化工具
        
        Args:
            metadata: 工具元数据
        """
        self.metadata = metadata
        self.logger = logging.getLogger(f"{__name__}.{metadata.tool_id}")
        self.execution_history = []
    
    @abstractmethod
    async def execute(self, **kwargs) -> Dict[str, Any]:
        """执行工具"""
        pass
    
    def validate_parameters(self, **kwargs) -> Tuple[bool, List[str]]:
        """验证参数"""
        errors = []
        
        # 检查必需参数
        required_params = {p.name for p in self.metadata.parameters if p.required}
        provided_params = set(kwargs.keys())
        missing_params = required_params - provided_params
        
        if missing_params:
            errors.append(f"缺少必需参数: {', '.join(missing_params)}")
        
        # 验证每个参数
        for param in self.metadata.parameters:
            if param.name in kwargs:
                is_valid, error_msg = param.validate(kwargs[param.name])
                if not is_valid:
                    errors.append(error_msg)
        
        return len(errors) == 0, errors
    
    async def safe_execute(self, **kwargs) -> Dict[str, Any]:
        """安全执行工具"""
        execution_id = str(uuid.uuid4())
        start_time = datetime.now()
        
        try:
            # 参数验证
            is_valid, errors = self.validate_parameters(**kwargs)
            if not is_valid:
                return {
                    'success': False,
                    'error': f"参数验证失败: {'; '.join(errors)}",
                    'execution_id': execution_id
                }
            
            # 状态检查
            if self.metadata.status != ToolStatus.AVAILABLE:
                return {
                    'success': False,
                    'error': f"工具不可用，当前状态: {self.metadata.status.value}",
                    'execution_id': execution_id
                }
            
            # 执行工具
            self.metadata.status = ToolStatus.BUSY
            result = await self.execute(**kwargs)
            
            # 更新统计信息
            execution_time = (datetime.now() - start_time).total_seconds()
            self.metadata.usage_count += 1
            self.metadata.last_used = datetime.now()
            
            # 更新性能指标
            if 'avg_execution_time' not in self.metadata.performance_metrics:
                self.metadata.performance_metrics['avg_execution_time'] = execution_time
            else:
                current_avg = self.metadata.performance_metrics['avg_execution_time']
                new_avg = (current_avg * (self.metadata.usage_count - 1) + execution_time) / self.metadata.usage_count
                self.metadata.performance_metrics['avg_execution_time'] = new_avg
            
            # 记录执行历史
            self.execution_history.append({
                'execution_id': execution_id,
                'start_time': start_time.isoformat(),
                'execution_time': execution_time,
                'parameters': kwargs,
                'success': result.get('success', True)
            })
            
            # 保持历史记录在合理范围内
            if len(self.execution_history) > 100:
                self.execution_history.pop(0)
            
            return {
                'success': True,
                'result': result,
                'execution_id': execution_id,
                'execution_time': execution_time
            }
            
        except Exception as e:
            self.logger.error(f"工具执行失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'execution_id': execution_id,
                'traceback': traceback.format_exc()
            }
        finally:
            self.metadata.status = ToolStatus.AVAILABLE


class ToolRegistry:
    """工具注册表"""
    
    def __init__(self):
        """初始化工具注册表"""
        self.logger = logging.getLogger(__name__)
        self.tools = {}
        self.tool_chains = {}
        self.discovery_rules = []
    
    def register_tool(self, tool: BaseTool) -> None:
        """注册工具"""
        self.tools[tool.metadata.tool_id] = tool
        self.logger.info(f"工具已注册: {tool.metadata.name} ({tool.metadata.tool_id})")
    
    def unregister_tool(self, tool_id: str) -> None:
        """注销工具"""
        if tool_id in self.tools:
            del self.tools[tool_id]
            self.logger.info(f"工具已注销: {tool_id}")
    
    def get_tool(self, tool_id: str) -> Optional[BaseTool]:
        """获取工具"""
        return self.tools.get(tool_id)
    
    def discover_tools(self, task_description: str, required_capabilities: List[str] = None) -> List[str]:
        """智能工具发现"""
        matching_tools = []
        
        # 基于描述的关键词匹配
        keywords = task_description.lower().split()
        
        for tool_id, tool in self.tools.items():
            if tool.metadata.status != ToolStatus.AVAILABLE:
                continue
            
            score = 0
            
            # 名称匹配
            tool_name_words = tool.metadata.name.lower().split()
            for keyword in keywords:
                if any(keyword in word for word in tool_name_words):
                    score += 2
            
            # 描述匹配
            tool_desc_words = tool.metadata.description.lower().split()
            for keyword in keywords:
                if any(keyword in word for word in tool_desc_words):
                    score += 1
            
            # 类型匹配
            if required_capabilities:
                for capability in required_capabilities:
                    if capability.lower() in tool.metadata.tool_type.value:
                        score += 3
            
            if score > 0:
                matching_tools.append((tool_id, score))
        
        # 按分数排序
        matching_tools.sort(key=lambda x: x[1], reverse=True)
        return [tool_id for tool_id, _ in matching_tools]
    
    def get_tools_by_type(self, tool_type: ToolType) -> List[str]:
        """根据类型获取工具"""
        return [
            tool_id for tool_id, tool in self.tools.items()
            if tool.metadata.tool_type == tool_type and tool.metadata.status == ToolStatus.AVAILABLE
        ]
    
    def create_tool_chain(self, chain_id: str, tool_sequence: List[Dict[str, Any]]) -> None:
        """创建工具链"""
        self.tool_chains[chain_id] = {
            'sequence': tool_sequence,
            'created_at': datetime.now(),
            'usage_count': 0
        }
        self.logger.info(f"工具链已创建: {chain_id}")
    
    async def execute_tool_chain(self, chain_id: str, initial_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行工具链"""
        if chain_id not in self.tool_chains:
            return {
                'success': False,
                'error': f'工具链不存在: {chain_id}'
            }
        
        chain = self.tool_chains[chain_id]
        sequence = chain['sequence']
        current_data = initial_data.copy()
        results = []
        
        try:
            for step_index, step in enumerate(sequence):
                tool_id = step['tool_id']
                parameters = step.get('parameters', {})
                data_mapping = step.get('data_mapping', {})
                
                # 获取工具
                tool = self.get_tool(tool_id)
                if not tool:
                    return {
                        'success': False,
                        'error': f'工具不存在: {tool_id}',
                        'step_index': step_index
                    }
                
                # 映射数据
                mapped_params = parameters.copy()
                for param_name, data_key in data_mapping.items():
                    if data_key in current_data:
                        mapped_params[param_name] = current_data[data_key]
                
                # 执行工具
                result = await tool.safe_execute(**mapped_params)
                results.append({
                    'step_index': step_index,
                    'tool_id': tool_id,
                    'result': result
                })
                
                if not result.get('success', False):
                    return {
                        'success': False,
                        'error': f'工具链执行失败在步骤 {step_index}',
                        'results': results
                    }
                
                # 更新当前数据
                if 'result' in result and isinstance(result['result'], dict):
                    current_data.update(result['result'])
            
            # 更新使用统计
            chain['usage_count'] += 1
            
            return {
                'success': True,
                'results': results,
                'final_data': current_data
            }
            
        except Exception as e:
            self.logger.error(f"工具链执行失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'results': results
            }
    
    def get_registry_stats(self) -> Dict[str, Any]:
        """获取注册表统计"""
        tool_types = {}
        total_usage = 0
        available_tools = 0
        
        for tool in self.tools.values():
            tool_type = tool.metadata.tool_type.value
            tool_types[tool_type] = tool_types.get(tool_type, 0) + 1
            total_usage += tool.metadata.usage_count
            
            if tool.metadata.status == ToolStatus.AVAILABLE:
                available_tools += 1
        
        return {
            'total_tools': len(self.tools),
            'available_tools': available_tools,
            'tool_types': tool_types,
            'total_usage': total_usage,
            'tool_chains': len(self.tool_chains),
            'avg_usage_per_tool': total_usage / max(len(self.tools), 1)
        }


class AdvancedToolCaller:
    """高级工具调用器"""
    
    def __init__(self, registry: ToolRegistry):
        """
        初始化高级工具调用器
        
        Args:
            registry: 工具注册表
        """
        self.registry = registry
        self.logger = logging.getLogger(__name__)
        self.call_history = []
        self.optimization_rules = []
    
    async def intelligent_call(self, task_description: str, 
                             context: Dict[str, Any] = None,
                             preferences: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        智能工具调用
        
        Args:
            task_description: 任务描述
            context: 上下文信息
            preferences: 用户偏好
            
        Returns:
            调用结果
        """
        try:
            call_id = str(uuid.uuid4())
            start_time = datetime.now()
            
            # 1. 工具发现
            required_capabilities = self._extract_capabilities(task_description)
            candidate_tools = self.registry.discover_tools(task_description, required_capabilities)
            
            if not candidate_tools:
                return {
                    'success': False,
                    'error': '未找到合适的工具',
                    'call_id': call_id
                }
            
            # 2. 工具选择
            selected_tool_id = self._select_best_tool(candidate_tools, context, preferences)
            tool = self.registry.get_tool(selected_tool_id)
            
            if not tool:
                return {
                    'success': False,
                    'error': f'工具不可用: {selected_tool_id}',
                    'call_id': call_id
                }
            
            # 3. 参数推断
            parameters = self._infer_parameters(tool, task_description, context)
            
            # 4. 执行工具
            result = await tool.safe_execute(**parameters)
            
            # 5. 结果验证
            validated_result = self._validate_result(result, task_description)
            
            # 6. 记录调用历史
            call_record = {
                'call_id': call_id,
                'task_description': task_description,
                'selected_tool': selected_tool_id,
                'parameters': parameters,
                'result': validated_result,
                'execution_time': (datetime.now() - start_time).total_seconds(),
                'timestamp': datetime.now().isoformat()
            }
            
            self.call_history.append(call_record)
            if len(self.call_history) > 1000:
                self.call_history.pop(0)
            
            return {
                'success': True,
                'result': validated_result,
                'call_id': call_id,
                'tool_used': selected_tool_id,
                'execution_metadata': {
                    'candidate_tools': candidate_tools,
                    'inferred_parameters': parameters,
                    'execution_time': call_record['execution_time']
                }
            }
            
        except Exception as e:
            self.logger.error(f"智能工具调用失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'call_id': call_id if 'call_id' in locals() else 'unknown'
            }
    
    def _extract_capabilities(self, task_description: str) -> List[str]:
        """从任务描述中提取所需能力"""
        capabilities = []
        
        capability_keywords = {
            'data_source': ['获取', '收集', '抓取', 'fetch', 'collect', 'get'],
            'analysis': ['分析', '计算', '评估', 'analyze', 'calculate', 'evaluate'],
            'visualization': ['图表', '可视化', '绘制', 'chart', 'plot', 'visualize'],
            'transformation': ['转换', '处理', '清洗', 'transform', 'process', 'clean']
        }
        
        task_lower = task_description.lower()
        for capability, keywords in capability_keywords.items():
            if any(keyword in task_lower for keyword in keywords):
                capabilities.append(capability)
        
        return capabilities
    
    def _select_best_tool(self, candidate_tools: List[str], 
                         context: Dict[str, Any] = None,
                         preferences: Dict[str, Any] = None) -> str:
        """选择最佳工具"""
        if not candidate_tools:
            return None
        
        # 简化选择逻辑：优先选择性能最好的工具
        best_tool_id = candidate_tools[0]
        best_score = 0
        
        for tool_id in candidate_tools:
            tool = self.registry.get_tool(tool_id)
            if not tool:
                continue
            
            score = 0
            
            # 性能评分
            avg_time = tool.metadata.performance_metrics.get('avg_execution_time', 1.0)
            score += max(0, 10 - avg_time)  # 执行时间越短分数越高
            
            # 使用频率评分
            score += min(tool.metadata.usage_count / 10, 5)  # 使用越多分数越高（上限5分）
            
            # 偏好评分
            if preferences and 'preferred_tools' in preferences:
                if tool_id in preferences['preferred_tools']:
                    score += 10
            
            if score > best_score:
                best_score = score
                best_tool_id = tool_id
        
        return best_tool_id
    
    def _infer_parameters(self, tool: BaseTool, task_description: str, 
                         context: Dict[str, Any] = None) -> Dict[str, Any]:
        """推断工具参数"""
        parameters = {}
        context = context or {}
        
        # 简化参数推断逻辑
        for param in tool.metadata.parameters:
            if param.name in context:
                parameters[param.name] = context[param.name]
            elif not param.required and param.default_value is not None:
                parameters[param.name] = param.default_value
            elif param.param_type == str:
                parameters[param.name] = task_description
            elif param.param_type == int:
                parameters[param.name] = 1
            elif param.param_type == float:
                parameters[param.name] = 1.0
            elif param.param_type == bool:
                parameters[param.name] = True
            elif param.param_type == list:
                parameters[param.name] = []
            elif param.param_type == dict:
                parameters[param.name] = {}
        
        return parameters
    
    def _validate_result(self, result: Dict[str, Any], task_description: str) -> Dict[str, Any]:
        """验证结果"""
        # 简化验证逻辑
        if not result.get('success', False):
            return result
        
        # 添加验证元数据
        validated_result = result.copy()
        validated_result['validation'] = {
            'validated_at': datetime.now().isoformat(),
            'validation_passed': True,
            'validation_notes': []
        }
        
        return validated_result
    
    def get_call_statistics(self) -> Dict[str, Any]:
        """获取调用统计"""
        if not self.call_history:
            return {'total_calls': 0}
        
        total_calls = len(self.call_history)
        successful_calls = sum(1 for call in self.call_history if call['result'].get('success', False))
        
        tool_usage = {}
        for call in self.call_history:
            tool_id = call['selected_tool']
            tool_usage[tool_id] = tool_usage.get(tool_id, 0) + 1
        
        avg_execution_time = sum(call['execution_time'] for call in self.call_history) / total_calls
        
        return {
            'total_calls': total_calls,
            'successful_calls': successful_calls,
            'success_rate': (successful_calls / total_calls) * 100,
            'avg_execution_time': avg_execution_time,
            'tool_usage_distribution': tool_usage,
            'most_used_tool': max(tool_usage.items(), key=lambda x: x[1])[0] if tool_usage else None
        }
