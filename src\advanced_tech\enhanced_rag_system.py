#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强RAG (Retrieval-Augmented Generation) 系统
实现智能检索、多模态融合、动态知识更新和上下文感知生成
"""

import asyncio
import logging
import json
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import hashlib
import pickle
from abc import ABC, abstractmethod


class DocumentType(Enum):
    """文档类型"""
    TEXT = "text"
    TABLE = "table"
    CHART = "chart"
    IMAGE = "image"
    FINANCIAL_STATEMENT = "financial_statement"
    RESEARCH_REPORT = "research_report"
    NEWS_ARTICLE = "news_article"
    REGULATORY_FILING = "regulatory_filing"


class RetrievalStrategy(Enum):
    """检索策略"""
    SEMANTIC = "semantic"
    KEYWORD = "keyword"
    HYBRID = "hybrid"
    CONTEXTUAL = "contextual"
    TEMPORAL = "temporal"


@dataclass
class Document:
    """文档"""
    doc_id: str
    title: str
    content: str
    doc_type: DocumentType
    metadata: Dict[str, Any]
    embedding: Optional[np.ndarray] = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    access_count: int = 0
    relevance_score: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'doc_id': self.doc_id,
            'title': self.title,
            'content': self.content,
            'doc_type': self.doc_type.value,
            'metadata': self.metadata,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'access_count': self.access_count,
            'relevance_score': self.relevance_score
        }


@dataclass
class QueryContext:
    """查询上下文"""
    user_id: str
    session_id: str
    query_history: List[str]
    domain_context: Dict[str, Any]
    temporal_context: Dict[str, Any]
    user_preferences: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'user_id': self.user_id,
            'session_id': self.session_id,
            'query_history': self.query_history,
            'domain_context': self.domain_context,
            'temporal_context': self.temporal_context,
            'user_preferences': self.user_preferences
        }


class EmbeddingModel(ABC):
    """嵌入模型抽象基类"""
    
    @abstractmethod
    async def encode(self, text: str) -> np.ndarray:
        """编码文本为向量"""
        pass
    
    @abstractmethod
    async def encode_batch(self, texts: List[str]) -> List[np.ndarray]:
        """批量编码文本"""
        pass


class MockEmbeddingModel(EmbeddingModel):
    """模拟嵌入模型"""
    
    def __init__(self, dimension: int = 768):
        """
        初始化模拟嵌入模型
        
        Args:
            dimension: 向量维度
        """
        self.dimension = dimension
        self.logger = logging.getLogger(__name__)
    
    async def encode(self, text: str) -> np.ndarray:
        """编码文本为向量"""
        # 使用文本哈希生成确定性向量
        text_hash = hashlib.md5(text.encode()).hexdigest()
        np.random.seed(int(text_hash[:8], 16))
        vector = np.random.normal(0, 1, self.dimension)
        return vector / np.linalg.norm(vector)
    
    async def encode_batch(self, texts: List[str]) -> List[np.ndarray]:
        """批量编码文本"""
        return [await self.encode(text) for text in texts]


class VectorStore:
    """向量存储"""
    
    def __init__(self, dimension: int = 768):
        """
        初始化向量存储
        
        Args:
            dimension: 向量维度
        """
        self.dimension = dimension
        self.logger = logging.getLogger(__name__)
        self.documents = {}
        self.vectors = {}
        self.index_metadata = {
            'total_documents': 0,
            'last_updated': datetime.now(),
            'index_version': '1.0'
        }
    
    def add_document(self, document: Document) -> None:
        """添加文档"""
        if document.embedding is None:
            raise ValueError("文档必须包含嵌入向量")
        
        self.documents[document.doc_id] = document
        self.vectors[document.doc_id] = document.embedding
        self.index_metadata['total_documents'] += 1
        self.index_metadata['last_updated'] = datetime.now()
        
        self.logger.info(f"文档已添加: {document.doc_id}")
    
    def remove_document(self, doc_id: str) -> None:
        """移除文档"""
        if doc_id in self.documents:
            del self.documents[doc_id]
            del self.vectors[doc_id]
            self.index_metadata['total_documents'] -= 1
            self.index_metadata['last_updated'] = datetime.now()
            self.logger.info(f"文档已移除: {doc_id}")
    
    def search_similar(self, query_vector: np.ndarray, top_k: int = 10,
                      doc_type_filter: Optional[DocumentType] = None) -> List[Tuple[str, float]]:
        """搜索相似文档"""
        if not self.vectors:
            return []
        
        similarities = []
        
        for doc_id, vector in self.vectors.items():
            document = self.documents[doc_id]
            
            # 文档类型过滤
            if doc_type_filter and document.doc_type != doc_type_filter:
                continue
            
            # 计算余弦相似度
            similarity = np.dot(query_vector, vector) / (
                np.linalg.norm(query_vector) * np.linalg.norm(vector)
            )
            similarities.append((doc_id, float(similarity)))
        
        # 按相似度排序
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:top_k]
    
    def get_document(self, doc_id: str) -> Optional[Document]:
        """获取文档"""
        return self.documents.get(doc_id)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        doc_types = {}
        total_access = 0
        
        for document in self.documents.values():
            doc_type = document.doc_type.value
            doc_types[doc_type] = doc_types.get(doc_type, 0) + 1
            total_access += document.access_count
        
        return {
            'total_documents': len(self.documents),
            'document_types': doc_types,
            'total_access_count': total_access,
            'avg_access_per_document': total_access / max(len(self.documents), 1),
            'index_metadata': self.index_metadata
        }


class ContextualRetriever:
    """上下文感知检索器"""
    
    def __init__(self, vector_store: VectorStore, embedding_model: EmbeddingModel):
        """
        初始化上下文感知检索器
        
        Args:
            vector_store: 向量存储
            embedding_model: 嵌入模型
        """
        self.vector_store = vector_store
        self.embedding_model = embedding_model
        self.logger = logging.getLogger(__name__)
        self.retrieval_history = []
    
    async def retrieve(self, query: str, context: QueryContext,
                      strategy: RetrievalStrategy = RetrievalStrategy.HYBRID,
                      top_k: int = 10) -> List[Document]:
        """
        上下文感知检索
        
        Args:
            query: 查询文本
            context: 查询上下文
            strategy: 检索策略
            top_k: 返回文档数量
            
        Returns:
            检索到的文档列表
        """
        try:
            retrieval_id = f"retrieval_{int(datetime.now().timestamp() * 1000)}"
            start_time = datetime.now()
            
            # 1. 查询增强
            enhanced_query = await self._enhance_query(query, context)
            
            # 2. 根据策略检索
            if strategy == RetrievalStrategy.SEMANTIC:
                results = await self._semantic_retrieval(enhanced_query, top_k)
            elif strategy == RetrievalStrategy.KEYWORD:
                results = await self._keyword_retrieval(enhanced_query, top_k)
            elif strategy == RetrievalStrategy.HYBRID:
                results = await self._hybrid_retrieval(enhanced_query, context, top_k)
            elif strategy == RetrievalStrategy.CONTEXTUAL:
                results = await self._contextual_retrieval(enhanced_query, context, top_k)
            elif strategy == RetrievalStrategy.TEMPORAL:
                results = await self._temporal_retrieval(enhanced_query, context, top_k)
            else:
                results = await self._semantic_retrieval(enhanced_query, top_k)
            
            # 3. 后处理和重排序
            reranked_results = await self._rerank_results(results, query, context)
            
            # 4. 更新访问统计
            for document in reranked_results:
                document.access_count += 1
            
            # 5. 记录检索历史
            retrieval_record = {
                'retrieval_id': retrieval_id,
                'query': query,
                'enhanced_query': enhanced_query,
                'strategy': strategy.value,
                'results_count': len(reranked_results),
                'execution_time': (datetime.now() - start_time).total_seconds(),
                'timestamp': datetime.now().isoformat()
            }
            
            self.retrieval_history.append(retrieval_record)
            if len(self.retrieval_history) > 1000:
                self.retrieval_history.pop(0)
            
            self.logger.info(f"检索完成: {retrieval_id}, 返回 {len(reranked_results)} 个文档")
            
            return reranked_results
            
        except Exception as e:
            self.logger.error(f"检索失败: {e}")
            return []
    
    async def _enhance_query(self, query: str, context: QueryContext) -> str:
        """增强查询"""
        enhanced_parts = [query]
        
        # 添加历史查询上下文
        if context.query_history:
            recent_queries = context.query_history[-3:]  # 最近3个查询
            enhanced_parts.extend(recent_queries)
        
        # 添加领域上下文
        if context.domain_context:
            domain_terms = context.domain_context.get('key_terms', [])
            enhanced_parts.extend(domain_terms[:5])  # 最多5个关键词
        
        # 添加时间上下文
        if context.temporal_context:
            time_period = context.temporal_context.get('time_period', '')
            if time_period:
                enhanced_parts.append(f"时间范围: {time_period}")
        
        return " ".join(enhanced_parts)
    
    async def _semantic_retrieval(self, query: str, top_k: int) -> List[Document]:
        """语义检索"""
        query_vector = await self.embedding_model.encode(query)
        similar_docs = self.vector_store.search_similar(query_vector, top_k)
        
        results = []
        for doc_id, similarity in similar_docs:
            document = self.vector_store.get_document(doc_id)
            if document:
                document.relevance_score = similarity
                results.append(document)
        
        return results
    
    async def _keyword_retrieval(self, query: str, top_k: int) -> List[Document]:
        """关键词检索"""
        query_words = set(query.lower().split())
        scored_docs = []
        
        for doc_id, document in self.vector_store.documents.items():
            content_words = set(document.content.lower().split())
            title_words = set(document.title.lower().split())
            
            # 计算关键词匹配分数
            content_matches = len(query_words & content_words)
            title_matches = len(query_words & title_words) * 2  # 标题匹配权重更高
            
            total_score = content_matches + title_matches
            if total_score > 0:
                document.relevance_score = total_score / len(query_words)
                scored_docs.append(document)
        
        # 按分数排序
        scored_docs.sort(key=lambda x: x.relevance_score, reverse=True)
        return scored_docs[:top_k]
    
    async def _hybrid_retrieval(self, query: str, context: QueryContext, top_k: int) -> List[Document]:
        """混合检索"""
        # 语义检索结果
        semantic_results = await self._semantic_retrieval(query, top_k * 2)
        
        # 关键词检索结果
        keyword_results = await self._keyword_retrieval(query, top_k * 2)
        
        # 合并和去重
        combined_docs = {}
        
        # 添加语义检索结果
        for doc in semantic_results:
            combined_docs[doc.doc_id] = doc
            doc.relevance_score *= 0.7  # 语义权重
        
        # 添加关键词检索结果
        for doc in keyword_results:
            if doc.doc_id in combined_docs:
                # 合并分数
                combined_docs[doc.doc_id].relevance_score += doc.relevance_score * 0.3
            else:
                doc.relevance_score *= 0.3  # 关键词权重
                combined_docs[doc.doc_id] = doc
        
        # 排序并返回
        results = list(combined_docs.values())
        results.sort(key=lambda x: x.relevance_score, reverse=True)
        return results[:top_k]
    
    async def _contextual_retrieval(self, query: str, context: QueryContext, top_k: int) -> List[Document]:
        """上下文检索"""
        # 基于混合检索
        base_results = await self._hybrid_retrieval(query, context, top_k * 2)
        
        # 上下文加权
        for document in base_results:
            context_boost = 0
            
            # 用户偏好加权
            if context.user_preferences:
                preferred_types = context.user_preferences.get('document_types', [])
                if document.doc_type.value in preferred_types:
                    context_boost += 0.2
            
            # 领域相关性加权
            if context.domain_context:
                domain_keywords = context.domain_context.get('key_terms', [])
                doc_content_lower = document.content.lower()
                matching_keywords = sum(1 for keyword in domain_keywords if keyword.lower() in doc_content_lower)
                context_boost += (matching_keywords / max(len(domain_keywords), 1)) * 0.3
            
            # 时间相关性加权
            if context.temporal_context:
                time_weight = self._calculate_temporal_relevance(document, context.temporal_context)
                context_boost += time_weight * 0.1
            
            document.relevance_score += context_boost
        
        # 重新排序
        base_results.sort(key=lambda x: x.relevance_score, reverse=True)
        return base_results[:top_k]
    
    async def _temporal_retrieval(self, query: str, context: QueryContext, top_k: int) -> List[Document]:
        """时间感知检索"""
        # 基于上下文检索
        base_results = await self._contextual_retrieval(query, context, top_k * 2)
        
        # 时间过滤和加权
        temporal_context = context.temporal_context
        if not temporal_context:
            return base_results[:top_k]
        
        filtered_results = []
        for document in base_results:
            # 时间范围过滤
            if 'start_date' in temporal_context and 'end_date' in temporal_context:
                start_date = datetime.fromisoformat(temporal_context['start_date'])
                end_date = datetime.fromisoformat(temporal_context['end_date'])
                
                if not (start_date <= document.created_at <= end_date):
                    continue
            
            # 时间衰减加权
            time_decay = self._calculate_time_decay(document.created_at)
            document.relevance_score *= time_decay
            
            filtered_results.append(document)
        
        # 排序
        filtered_results.sort(key=lambda x: x.relevance_score, reverse=True)
        return filtered_results[:top_k]
    
    def _calculate_temporal_relevance(self, document: Document, temporal_context: Dict[str, Any]) -> float:
        """计算时间相关性"""
        if 'reference_date' not in temporal_context:
            return 0.0
        
        reference_date = datetime.fromisoformat(temporal_context['reference_date'])
        doc_date = document.created_at
        
        # 计算时间差（天）
        time_diff = abs((reference_date - doc_date).days)
        
        # 时间相关性衰减
        if time_diff <= 7:
            return 1.0
        elif time_diff <= 30:
            return 0.8
        elif time_diff <= 90:
            return 0.6
        elif time_diff <= 365:
            return 0.4
        else:
            return 0.2
    
    def _calculate_time_decay(self, doc_date: datetime) -> float:
        """计算时间衰减因子"""
        now = datetime.now()
        days_old = (now - doc_date).days
        
        # 指数衰减
        decay_rate = 0.01  # 每天衰减1%
        return max(0.1, 1.0 - (days_old * decay_rate))
    
    async def _rerank_results(self, results: List[Document], original_query: str, 
                            context: QueryContext) -> List[Document]:
        """重排序结果"""
        # 简化重排序逻辑
        for document in results:
            # 查询匹配度加权
            query_words = set(original_query.lower().split())
            doc_words = set(document.content.lower().split())
            query_coverage = len(query_words & doc_words) / len(query_words)
            
            # 文档质量加权
            quality_score = min(document.access_count / 100, 1.0)  # 访问次数反映质量
            
            # 综合重排序分数
            rerank_score = (
                document.relevance_score * 0.7 +
                query_coverage * 0.2 +
                quality_score * 0.1
            )
            
            document.relevance_score = rerank_score
        
        # 最终排序
        results.sort(key=lambda x: x.relevance_score, reverse=True)
        return results
    
    def get_retrieval_statistics(self) -> Dict[str, Any]:
        """获取检索统计"""
        if not self.retrieval_history:
            return {'total_retrievals': 0}
        
        total_retrievals = len(self.retrieval_history)
        avg_execution_time = sum(r['execution_time'] for r in self.retrieval_history) / total_retrievals
        avg_results_count = sum(r['results_count'] for r in self.retrieval_history) / total_retrievals
        
        strategy_usage = {}
        for record in self.retrieval_history:
            strategy = record['strategy']
            strategy_usage[strategy] = strategy_usage.get(strategy, 0) + 1
        
        return {
            'total_retrievals': total_retrievals,
            'avg_execution_time': avg_execution_time,
            'avg_results_count': avg_results_count,
            'strategy_usage': strategy_usage,
            'most_used_strategy': max(strategy_usage.items(), key=lambda x: x[1])[0] if strategy_usage else None
        }


class KnowledgeGraph:
    """知识图谱"""

    def __init__(self):
        """初始化知识图谱"""
        self.logger = logging.getLogger(__name__)
        self.entities = {}
        self.relationships = {}
        self.entity_embeddings = {}

    def add_entity(self, entity_id: str, entity_type: str, properties: Dict[str, Any]) -> None:
        """添加实体"""
        self.entities[entity_id] = {
            'type': entity_type,
            'properties': properties,
            'created_at': datetime.now().isoformat()
        }
        self.logger.info(f"实体已添加: {entity_id}")

    def add_relationship(self, source_id: str, target_id: str, relation_type: str,
                        properties: Dict[str, Any] = None) -> None:
        """添加关系"""
        rel_id = f"{source_id}_{relation_type}_{target_id}"
        self.relationships[rel_id] = {
            'source': source_id,
            'target': target_id,
            'type': relation_type,
            'properties': properties or {},
            'created_at': datetime.now().isoformat()
        }
        self.logger.info(f"关系已添加: {rel_id}")

    def find_related_entities(self, entity_id: str, max_depth: int = 2) -> List[str]:
        """查找相关实体"""
        related = set()
        current_level = {entity_id}

        for depth in range(max_depth):
            next_level = set()
            for current_entity in current_level:
                # 查找所有相关关系
                for rel_id, relationship in self.relationships.items():
                    if relationship['source'] == current_entity:
                        next_level.add(relationship['target'])
                    elif relationship['target'] == current_entity:
                        next_level.add(relationship['source'])

            related.update(next_level)
            current_level = next_level - related

            if not current_level:
                break

        return list(related)


class EnhancedRAGSystem:
    """增强RAG系统主类"""

    def __init__(self, embedding_model: Optional[EmbeddingModel] = None):
        """
        初始化增强RAG系统

        Args:
            embedding_model: 嵌入模型
        """
        self.logger = logging.getLogger(__name__)
        self.embedding_model = embedding_model or MockEmbeddingModel()
        self.vector_store = VectorStore()
        self.retriever = ContextualRetriever(self.vector_store, self.embedding_model)
        self.knowledge_graph = KnowledgeGraph()

        # 系统统计
        self.system_stats = {
            'total_queries': 0,
            'successful_generations': 0,
            'avg_response_time': 0.0,
            'knowledge_base_size': 0
        }

    async def add_document(self, title: str, content: str, doc_type: DocumentType,
                          metadata: Dict[str, Any] = None) -> str:
        """
        添加文档到知识库

        Args:
            title: 文档标题
            content: 文档内容
            doc_type: 文档类型
            metadata: 元数据

        Returns:
            文档ID
        """
        try:
            doc_id = f"doc_{int(datetime.now().timestamp() * 1000)}"

            # 生成嵌入向量
            embedding = await self.embedding_model.encode(f"{title} {content}")

            # 创建文档
            document = Document(
                doc_id=doc_id,
                title=title,
                content=content,
                doc_type=doc_type,
                metadata=metadata or {},
                embedding=embedding
            )

            # 添加到向量存储
            self.vector_store.add_document(document)

            # 提取实体和关系（简化实现）
            await self._extract_knowledge(document)

            self.system_stats['knowledge_base_size'] += 1
            self.logger.info(f"文档已添加: {doc_id}")

            return doc_id

        except Exception as e:
            self.logger.error(f"添加文档失败: {e}")
            raise e

    async def generate_response(self, query: str, context: QueryContext,
                              max_docs: int = 5, include_sources: bool = True) -> Dict[str, Any]:
        """
        生成增强响应

        Args:
            query: 用户查询
            context: 查询上下文
            max_docs: 最大检索文档数
            include_sources: 是否包含来源信息

        Returns:
            生成的响应
        """
        try:
            generation_id = f"gen_{int(datetime.now().timestamp() * 1000)}"
            start_time = datetime.now()

            self.system_stats['total_queries'] += 1

            # 1. 检索相关文档
            retrieved_docs = await self.retriever.retrieve(
                query, context, RetrievalStrategy.HYBRID, max_docs
            )

            # 2. 知识图谱增强
            enhanced_context = await self._enhance_with_knowledge_graph(query, retrieved_docs)

            # 3. 生成响应
            response = await self._generate_contextual_response(
                query, retrieved_docs, enhanced_context
            )

            # 4. 后处理
            final_response = await self._post_process_response(response, retrieved_docs, include_sources)

            # 5. 更新统计
            execution_time = (datetime.now() - start_time).total_seconds()
            self._update_system_stats(execution_time, True)

            return {
                'generation_id': generation_id,
                'query': query,
                'response': final_response,
                'retrieved_documents': [doc.to_dict() for doc in retrieved_docs] if include_sources else [],
                'metadata': {
                    'execution_time': execution_time,
                    'documents_used': len(retrieved_docs),
                    'knowledge_enhanced': bool(enhanced_context),
                    'timestamp': datetime.now().isoformat()
                }
            }

        except Exception as e:
            self.logger.error(f"生成响应失败: {e}")
            self._update_system_stats(0, False)
            return {
                'generation_id': generation_id if 'generation_id' in locals() else 'error',
                'query': query,
                'response': f"抱歉，生成响应时发生错误: {str(e)}",
                'error': str(e)
            }

    async def _extract_knowledge(self, document: Document) -> None:
        """从文档中提取知识"""
        try:
            # 简化的实体提取
            content_words = document.content.split()

            # 提取可能的实体（简化：大写开头的词）
            entities = []
            for word in content_words:
                if word[0].isupper() and len(word) > 2:
                    entities.append(word.strip('.,!?'))

            # 添加实体到知识图谱
            for entity in set(entities):
                entity_id = f"entity_{entity.lower()}"
                self.knowledge_graph.add_entity(
                    entity_id,
                    'CONCEPT',
                    {'name': entity, 'source_doc': document.doc_id}
                )

                # 添加文档-实体关系
                self.knowledge_graph.add_relationship(
                    document.doc_id,
                    entity_id,
                    'MENTIONS',
                    {'confidence': 0.8}
                )

        except Exception as e:
            self.logger.error(f"知识提取失败: {e}")

    async def _enhance_with_knowledge_graph(self, query: str, documents: List[Document]) -> Dict[str, Any]:
        """使用知识图谱增强上下文"""
        try:
            enhanced_context = {
                'related_entities': [],
                'entity_relationships': [],
                'domain_concepts': []
            }

            # 从查询中提取实体
            query_words = query.split()
            query_entities = []
            for word in query_words:
                if word[0].isupper():
                    entity_id = f"entity_{word.lower()}"
                    if entity_id in self.knowledge_graph.entities:
                        query_entities.append(entity_id)

            # 查找相关实体
            for entity_id in query_entities:
                related = self.knowledge_graph.find_related_entities(entity_id, max_depth=2)
                enhanced_context['related_entities'].extend(related)

            # 从检索文档中提取相关概念
            for document in documents:
                doc_entities = []
                content_words = document.content.split()
                for word in content_words:
                    if word[0].isupper() and len(word) > 2:
                        doc_entities.append(word.strip('.,!?'))

                enhanced_context['domain_concepts'].extend(doc_entities[:5])  # 前5个概念

            return enhanced_context

        except Exception as e:
            self.logger.error(f"知识图谱增强失败: {e}")
            return {}

    async def _generate_contextual_response(self, query: str, documents: List[Document],
                                          enhanced_context: Dict[str, Any]) -> str:
        """生成上下文感知响应"""
        try:
            # 构建上下文
            context_parts = []

            # 添加检索文档内容
            for i, doc in enumerate(documents[:3]):  # 使用前3个最相关文档
                context_parts.append(f"参考文档{i+1}: {doc.title}")
                context_parts.append(doc.content[:500])  # 限制长度

            # 添加知识图谱信息
            if enhanced_context.get('domain_concepts'):
                concepts = enhanced_context['domain_concepts'][:5]
                context_parts.append(f"相关概念: {', '.join(set(concepts))}")

            # 构建提示
            context_text = "\n\n".join(context_parts)

            # 简化的响应生成（实际应该使用LLM）
            response = self._generate_mock_response(query, context_text)

            return response

        except Exception as e:
            self.logger.error(f"响应生成失败: {e}")
            return f"基于检索到的信息，我无法生成完整的响应。错误: {str(e)}"

    def _generate_mock_response(self, query: str, context: str) -> str:
        """生成模拟响应"""
        # 简化的响应生成逻辑
        if "分析" in query:
            return f"基于检索到的信息，我对您的查询进行了分析。根据相关文档，{context[:200]}... 这些信息表明了重要的趋势和模式。"
        elif "建议" in query:
            return f"根据检索到的资料，我建议您考虑以下几点：1) {context[:100]}... 2) 需要进一步关注相关风险因素。"
        elif "比较" in query:
            return f"通过对比分析检索到的信息，{context[:150]}... 可以看出不同方案各有优劣。"
        else:
            return f"根据检索到的相关信息，{context[:200]}... 希望这些信息对您有帮助。"

    async def _post_process_response(self, response: str, documents: List[Document],
                                   include_sources: bool) -> Dict[str, Any]:
        """后处理响应"""
        processed_response = {
            'text': response,
            'confidence_score': 0.8,  # 模拟置信度
            'response_type': 'informative',
            'word_count': len(response.split())
        }

        if include_sources:
            processed_response['sources'] = [
                {
                    'doc_id': doc.doc_id,
                    'title': doc.title,
                    'relevance_score': doc.relevance_score,
                    'doc_type': doc.doc_type.value
                }
                for doc in documents
            ]

        return processed_response

    def _update_system_stats(self, execution_time: float, success: bool) -> None:
        """更新系统统计"""
        if success:
            self.system_stats['successful_generations'] += 1

        # 更新平均响应时间
        total_queries = self.system_stats['total_queries']
        current_avg = self.system_stats['avg_response_time']
        new_avg = (current_avg * (total_queries - 1) + execution_time) / total_queries
        self.system_stats['avg_response_time'] = new_avg

    async def update_knowledge_base(self, updates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """动态更新知识库"""
        try:
            update_results = {
                'successful_updates': 0,
                'failed_updates': 0,
                'updated_documents': [],
                'errors': []
            }

            for update in updates:
                try:
                    update_type = update.get('type', 'add')

                    if update_type == 'add':
                        doc_id = await self.add_document(
                            update['title'],
                            update['content'],
                            DocumentType(update['doc_type']),
                            update.get('metadata', {})
                        )
                        update_results['updated_documents'].append(doc_id)
                        update_results['successful_updates'] += 1

                    elif update_type == 'remove':
                        doc_id = update['doc_id']
                        self.vector_store.remove_document(doc_id)
                        update_results['updated_documents'].append(doc_id)
                        update_results['successful_updates'] += 1

                    elif update_type == 'modify':
                        # 简化：删除旧文档，添加新文档
                        old_doc_id = update['doc_id']
                        self.vector_store.remove_document(old_doc_id)

                        new_doc_id = await self.add_document(
                            update['title'],
                            update['content'],
                            DocumentType(update['doc_type']),
                            update.get('metadata', {})
                        )
                        update_results['updated_documents'].append(new_doc_id)
                        update_results['successful_updates'] += 1

                except Exception as e:
                    update_results['failed_updates'] += 1
                    update_results['errors'].append(str(e))

            return update_results

        except Exception as e:
            self.logger.error(f"知识库更新失败: {e}")
            return {
                'successful_updates': 0,
                'failed_updates': len(updates),
                'errors': [str(e)]
            }

    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        vector_stats = self.vector_store.get_statistics()
        retrieval_stats = self.retriever.get_retrieval_statistics()

        success_rate = 0
        if self.system_stats['total_queries'] > 0:
            success_rate = (self.system_stats['successful_generations'] /
                          self.system_stats['total_queries']) * 100

        return {
            'system_statistics': self.system_stats,
            'success_rate': success_rate,
            'vector_store_stats': vector_stats,
            'retrieval_stats': retrieval_stats,
            'knowledge_graph_stats': {
                'total_entities': len(self.knowledge_graph.entities),
                'total_relationships': len(self.knowledge_graph.relationships)
            },
            'performance_metrics': {
                'avg_response_time': self.system_stats['avg_response_time'],
                'knowledge_base_utilization': vector_stats.get('total_access_count', 0) / max(vector_stats.get('total_documents', 1), 1)
            }
        }

    async def optimize_system(self) -> Dict[str, Any]:
        """系统优化"""
        optimization_results = {
            'optimizations_applied': [],
            'performance_improvements': {},
            'recommendations': []
        }

        # 检查知识库大小
        total_docs = self.vector_store.index_metadata['total_documents']
        if total_docs > 10000:
            optimization_results['recommendations'].append("考虑实施文档分片和索引优化")

        # 检查检索性能
        retrieval_stats = self.retriever.get_retrieval_statistics()
        if retrieval_stats.get('avg_execution_time', 0) > 2.0:
            optimization_results['recommendations'].append("优化检索算法以提高响应速度")

        # 检查成功率
        success_rate = (self.system_stats['successful_generations'] /
                       max(self.system_stats['total_queries'], 1)) * 100
        if success_rate < 90:
            optimization_results['recommendations'].append("改进错误处理和容错机制")

        optimization_results['recommendations'].extend([
            "定期更新嵌入模型以提高语义理解",
            "扩展知识图谱以增强上下文理解",
            "实施缓存机制以提高重复查询性能",
            "建立用户反馈循环以持续改进"
        ])

        return optimization_results
