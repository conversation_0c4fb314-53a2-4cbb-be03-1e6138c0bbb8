#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP (Model Context Protocol) 集成
实现模型上下文协议，支持多模型协作和上下文共享
"""

import asyncio
import logging
import json
import uuid
from typing import Dict, List, Any, Optional, Union, Callable
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import threading
from abc import ABC, abstractmethod


class MCPMessageType(Enum):
    """MCP消息类型"""
    CONTEXT_REQUEST = "context_request"
    CONTEXT_RESPONSE = "context_response"
    MODEL_INVOKE = "model_invoke"
    MODEL_RESPONSE = "model_response"
    CONTEXT_UPDATE = "context_update"
    CONTEXT_SYNC = "context_sync"
    ERROR = "error"


@dataclass
class MCPMessage:
    """MCP消息"""
    message_id: str
    message_type: MCPMessageType
    sender_id: str
    receiver_id: Optional[str]
    payload: Dict[str, Any]
    timestamp: datetime = field(default_factory=datetime.now)
    correlation_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'message_id': self.message_id,
            'message_type': self.message_type.value,
            'sender_id': self.sender_id,
            'receiver_id': self.receiver_id,
            'payload': self.payload,
            'timestamp': self.timestamp.isoformat(),
            'correlation_id': self.correlation_id
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MCPMessage':
        """从字典创建"""
        return cls(
            message_id=data['message_id'],
            message_type=MCPMessageType(data['message_type']),
            sender_id=data['sender_id'],
            receiver_id=data.get('receiver_id'),
            payload=data['payload'],
            timestamp=datetime.fromisoformat(data['timestamp']),
            correlation_id=data.get('correlation_id')
        )


class MCPNode(ABC):
    """MCP节点抽象基类"""
    
    def __init__(self, node_id: str):
        """
        初始化MCP节点
        
        Args:
            node_id: 节点ID
        """
        self.node_id = node_id
        self.logger = logging.getLogger(f"{__name__}.{node_id}")
        self.message_handlers = {}
        self.context_store = {}
        self.connections = {}
        self.running = False
    
    @abstractmethod
    async def process_message(self, message: MCPMessage) -> Optional[MCPMessage]:
        """处理消息"""
        pass
    
    def register_handler(self, message_type: MCPMessageType, handler: Callable):
        """注册消息处理器"""
        self.message_handlers[message_type] = handler
    
    async def send_message(self, message: MCPMessage) -> None:
        """发送消息"""
        if message.receiver_id in self.connections:
            connection = self.connections[message.receiver_id]
            await connection.receive_message(message)
        else:
            self.logger.warning(f"未找到接收者连接: {message.receiver_id}")
    
    async def receive_message(self, message: MCPMessage) -> None:
        """接收消息"""
        try:
            response = await self.process_message(message)
            if response:
                await self.send_message(response)
        except Exception as e:
            self.logger.error(f"处理消息失败: {e}")
            # 发送错误响应
            error_response = MCPMessage(
                message_id=str(uuid.uuid4()),
                message_type=MCPMessageType.ERROR,
                sender_id=self.node_id,
                receiver_id=message.sender_id,
                payload={'error': str(e), 'original_message_id': message.message_id},
                correlation_id=message.correlation_id
            )
            await self.send_message(error_response)
    
    def connect_to(self, other_node: 'MCPNode') -> None:
        """连接到其他节点"""
        self.connections[other_node.node_id] = other_node
        other_node.connections[self.node_id] = self
        self.logger.info(f"已连接到节点: {other_node.node_id}")


class MCPModelNode(MCPNode):
    """MCP模型节点"""
    
    def __init__(self, node_id: str, model_name: str, model_instance: Any):
        """
        初始化模型节点
        
        Args:
            node_id: 节点ID
            model_name: 模型名称
            model_instance: 模型实例
        """
        super().__init__(node_id)
        self.model_name = model_name
        self.model_instance = model_instance
        self.context_window = []
        self.max_context_length = 4000
        
        # 注册消息处理器
        self.register_handler(MCPMessageType.MODEL_INVOKE, self._handle_model_invoke)
        self.register_handler(MCPMessageType.CONTEXT_UPDATE, self._handle_context_update)
    
    async def process_message(self, message: MCPMessage) -> Optional[MCPMessage]:
        """处理消息"""
        handler = self.message_handlers.get(message.message_type)
        if handler:
            return await handler(message)
        else:
            self.logger.warning(f"未知消息类型: {message.message_type}")
            return None
    
    async def _handle_model_invoke(self, message: MCPMessage) -> MCPMessage:
        """处理模型调用"""
        try:
            payload = message.payload
            prompt = payload.get('prompt', '')
            context = payload.get('context', {})
            parameters = payload.get('parameters', {})
            
            # 更新上下文
            self._update_context(context)
            
            # 构建完整提示
            full_prompt = self._build_prompt_with_context(prompt)
            
            # 调用模型
            if hasattr(self.model_instance, 'generate_async'):
                response = await self.model_instance.generate_async(full_prompt, **parameters)
            elif hasattr(self.model_instance, 'generate'):
                response = self.model_instance.generate(full_prompt, **parameters)
            else:
                response = str(self.model_instance(full_prompt))
            
            # 更新上下文窗口
            self.context_window.append({
                'type': 'user',
                'content': prompt,
                'timestamp': datetime.now().isoformat()
            })
            self.context_window.append({
                'type': 'assistant',
                'content': response,
                'timestamp': datetime.now().isoformat()
            })
            
            # 保持上下文窗口大小
            self._trim_context_window()
            
            return MCPMessage(
                message_id=str(uuid.uuid4()),
                message_type=MCPMessageType.MODEL_RESPONSE,
                sender_id=self.node_id,
                receiver_id=message.sender_id,
                payload={
                    'response': response,
                    'model_name': self.model_name,
                    'context_length': len(self.context_window)
                },
                correlation_id=message.correlation_id
            )
            
        except Exception as e:
            self.logger.error(f"模型调用失败: {e}")
            return MCPMessage(
                message_id=str(uuid.uuid4()),
                message_type=MCPMessageType.ERROR,
                sender_id=self.node_id,
                receiver_id=message.sender_id,
                payload={'error': str(e)},
                correlation_id=message.correlation_id
            )
    
    async def _handle_context_update(self, message: MCPMessage) -> Optional[MCPMessage]:
        """处理上下文更新"""
        try:
            context_data = message.payload.get('context', {})
            self._update_context(context_data)
            
            return MCPMessage(
                message_id=str(uuid.uuid4()),
                message_type=MCPMessageType.CONTEXT_RESPONSE,
                sender_id=self.node_id,
                receiver_id=message.sender_id,
                payload={'status': 'context_updated'},
                correlation_id=message.correlation_id
            )
            
        except Exception as e:
            self.logger.error(f"上下文更新失败: {e}")
            return None
    
    def _update_context(self, context: Dict[str, Any]) -> None:
        """更新上下文"""
        for key, value in context.items():
            self.context_store[key] = value
    
    def _build_prompt_with_context(self, prompt: str) -> str:
        """构建包含上下文的提示"""
        context_parts = []
        
        # 添加系统上下文
        if 'system_context' in self.context_store:
            context_parts.append(f"系统上下文: {self.context_store['system_context']}")
        
        # 添加历史对话
        if self.context_window:
            context_parts.append("历史对话:")
            for item in self.context_window[-6:]:  # 最近3轮对话
                role = "用户" if item['type'] == 'user' else "助手"
                context_parts.append(f"{role}: {item['content']}")
        
        # 添加当前任务上下文
        if 'task_context' in self.context_store:
            context_parts.append(f"任务上下文: {self.context_store['task_context']}")
        
        # 组合完整提示
        if context_parts:
            full_prompt = "\n".join(context_parts) + f"\n\n当前问题: {prompt}"
        else:
            full_prompt = prompt
        
        return full_prompt
    
    def _trim_context_window(self) -> None:
        """修剪上下文窗口"""
        while len(self.context_window) > self.max_context_length:
            self.context_window.pop(0)


class MCPCoordinator(MCPNode):
    """MCP协调器"""
    
    def __init__(self, coordinator_id: str):
        """
        初始化协调器
        
        Args:
            coordinator_id: 协调器ID
        """
        super().__init__(coordinator_id)
        self.model_nodes = {}
        self.routing_table = {}
        self.load_balancer = {}
        
        # 注册消息处理器
        self.register_handler(MCPMessageType.CONTEXT_REQUEST, self._handle_context_request)
        self.register_handler(MCPMessageType.MODEL_INVOKE, self._handle_model_route)
    
    async def process_message(self, message: MCPMessage) -> Optional[MCPMessage]:
        """处理消息"""
        handler = self.message_handlers.get(message.message_type)
        if handler:
            return await handler(message)
        else:
            # 转发到适当的模型节点
            return await self._route_message(message)
    
    def register_model_node(self, model_node: MCPModelNode, capabilities: List[str]) -> None:
        """注册模型节点"""
        self.model_nodes[model_node.node_id] = {
            'node': model_node,
            'capabilities': capabilities,
            'load': 0,
            'last_used': datetime.now()
        }
        
        # 建立连接
        self.connect_to(model_node)
        
        # 更新路由表
        for capability in capabilities:
            if capability not in self.routing_table:
                self.routing_table[capability] = []
            self.routing_table[capability].append(model_node.node_id)
        
        self.logger.info(f"注册模型节点: {model_node.node_id}, 能力: {capabilities}")
    
    async def _handle_context_request(self, message: MCPMessage) -> MCPMessage:
        """处理上下文请求"""
        try:
            requested_context = message.payload.get('context_keys', [])
            context_data = {}
            
            for key in requested_context:
                if key in self.context_store:
                    context_data[key] = self.context_store[key]
            
            return MCPMessage(
                message_id=str(uuid.uuid4()),
                message_type=MCPMessageType.CONTEXT_RESPONSE,
                sender_id=self.node_id,
                receiver_id=message.sender_id,
                payload={'context': context_data},
                correlation_id=message.correlation_id
            )
            
        except Exception as e:
            self.logger.error(f"处理上下文请求失败: {e}")
            return MCPMessage(
                message_id=str(uuid.uuid4()),
                message_type=MCPMessageType.ERROR,
                sender_id=self.node_id,
                receiver_id=message.sender_id,
                payload={'error': str(e)},
                correlation_id=message.correlation_id
            )
    
    async def _handle_model_route(self, message: MCPMessage) -> Optional[MCPMessage]:
        """处理模型路由"""
        try:
            task_type = message.payload.get('task_type', 'general')
            target_node_id = self._select_model_node(task_type)
            
            if target_node_id:
                # 更新负载
                self.model_nodes[target_node_id]['load'] += 1
                self.model_nodes[target_node_id]['last_used'] = datetime.now()
                
                # 转发消息
                message.receiver_id = target_node_id
                await self.send_message(message)
                
                return None  # 不直接返回，等待模型节点响应
            else:
                return MCPMessage(
                    message_id=str(uuid.uuid4()),
                    message_type=MCPMessageType.ERROR,
                    sender_id=self.node_id,
                    receiver_id=message.sender_id,
                    payload={'error': f'未找到适合的模型节点: {task_type}'},
                    correlation_id=message.correlation_id
                )
                
        except Exception as e:
            self.logger.error(f"模型路由失败: {e}")
            return MCPMessage(
                message_id=str(uuid.uuid4()),
                message_type=MCPMessageType.ERROR,
                sender_id=self.node_id,
                receiver_id=message.sender_id,
                payload={'error': str(e)},
                correlation_id=message.correlation_id
            )
    
    def _select_model_node(self, task_type: str) -> Optional[str]:
        """选择模型节点"""
        # 查找支持该任务类型的节点
        candidate_nodes = self.routing_table.get(task_type, [])
        
        if not candidate_nodes:
            # 如果没有专门的节点，使用通用节点
            candidate_nodes = self.routing_table.get('general', [])
        
        if not candidate_nodes:
            return None
        
        # 负载均衡选择
        best_node = None
        min_load = float('inf')
        
        for node_id in candidate_nodes:
            if node_id in self.model_nodes:
                node_info = self.model_nodes[node_id]
                if node_info['load'] < min_load:
                    min_load = node_info['load']
                    best_node = node_id
        
        return best_node
    
    async def _route_message(self, message: MCPMessage) -> Optional[MCPMessage]:
        """路由消息"""
        # 简单转发到指定接收者
        if message.receiver_id and message.receiver_id in self.connections:
            await self.send_message(message)
            return None
        else:
            return MCPMessage(
                message_id=str(uuid.uuid4()),
                message_type=MCPMessageType.ERROR,
                sender_id=self.node_id,
                receiver_id=message.sender_id,
                payload={'error': '无法路由消息'},
                correlation_id=message.correlation_id
            )
    
    def get_cluster_status(self) -> Dict[str, Any]:
        """获取集群状态"""
        return {
            'coordinator_id': self.node_id,
            'total_nodes': len(self.model_nodes),
            'routing_table': self.routing_table,
            'node_status': {
                node_id: {
                    'capabilities': info['capabilities'],
                    'current_load': info['load'],
                    'last_used': info['last_used'].isoformat()
                }
                for node_id, info in self.model_nodes.items()
            }
        }


class MCPClient:
    """MCP客户端"""
    
    def __init__(self, client_id: str, coordinator: MCPCoordinator):
        """
        初始化客户端
        
        Args:
            client_id: 客户端ID
            coordinator: 协调器实例
        """
        self.client_id = client_id
        self.coordinator = coordinator
        self.logger = logging.getLogger(f"{__name__}.{client_id}")
        self.pending_requests = {}
    
    async def invoke_model(self, prompt: str, task_type: str = 'general', 
                          context: Optional[Dict[str, Any]] = None,
                          parameters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        调用模型
        
        Args:
            prompt: 提示文本
            task_type: 任务类型
            context: 上下文信息
            parameters: 模型参数
            
        Returns:
            模型响应
        """
        try:
            message_id = str(uuid.uuid4())
            correlation_id = str(uuid.uuid4())
            
            message = MCPMessage(
                message_id=message_id,
                message_type=MCPMessageType.MODEL_INVOKE,
                sender_id=self.client_id,
                receiver_id=self.coordinator.node_id,
                payload={
                    'prompt': prompt,
                    'task_type': task_type,
                    'context': context or {},
                    'parameters': parameters or {}
                },
                correlation_id=correlation_id
            )
            
            # 发送消息
            await self.coordinator.receive_message(message)
            
            # 等待响应（简化实现，实际应该使用异步等待）
            await asyncio.sleep(0.1)  # 模拟等待
            
            return {
                'success': True,
                'response': f"模拟响应: {prompt}",
                'correlation_id': correlation_id
            }
            
        except Exception as e:
            self.logger.error(f"模型调用失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def request_context(self, context_keys: List[str]) -> Dict[str, Any]:
        """
        请求上下文
        
        Args:
            context_keys: 上下文键列表
            
        Returns:
            上下文数据
        """
        try:
            message = MCPMessage(
                message_id=str(uuid.uuid4()),
                message_type=MCPMessageType.CONTEXT_REQUEST,
                sender_id=self.client_id,
                receiver_id=self.coordinator.node_id,
                payload={'context_keys': context_keys}
            )
            
            await self.coordinator.receive_message(message)
            
            # 简化返回
            return {
                'success': True,
                'context': {'mock_context': 'mock_value'}
            }
            
        except Exception as e:
            self.logger.error(f"上下文请求失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }
