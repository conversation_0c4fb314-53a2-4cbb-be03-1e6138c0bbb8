#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Agent基类
定义所有Agent的通用接口和功能
"""

import logging
import asyncio
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path


class BaseAgent(ABC):
    """Agent基类"""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        """
        初始化Agent
        
        Args:
            name: Agent名称
            config: 配置参数
        """
        self.name = name
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{name}")
        
        # Agent状态
        self.status = "initialized"
        self.created_at = datetime.now()
        self.last_activity = datetime.now()
        
        # 性能统计
        self.stats = {
            'tasks_completed': 0,
            'tasks_failed': 0,
            'total_execution_time': 0,
            'average_execution_time': 0
        }
        
        self.logger.info(f"Agent {name} 初始化完成")
    
    async def execute_task(self, task_type: str, **kwargs) -> Dict[str, Any]:
        """
        执行任务的通用接口
        
        Args:
            task_type: 任务类型
            **kwargs: 任务参数
            
        Returns:
            任务执行结果
        """
        try:
            start_time = datetime.now()
            self.status = "running"
            self.last_activity = start_time
            
            self.logger.info(f"Agent {self.name} 开始执行任务: {task_type}")
            
            # 调用具体的任务处理方法
            result = await self._handle_task(task_type, **kwargs)
            
            # 更新统计信息
            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()
            
            self.stats['tasks_completed'] += 1
            self.stats['total_execution_time'] += execution_time
            self.stats['average_execution_time'] = (
                self.stats['total_execution_time'] / self.stats['tasks_completed']
            )
            
            self.status = "idle"
            self.last_activity = end_time
            
            self.logger.info(f"Agent {self.name} 任务执行完成: {task_type}, 耗时: {execution_time:.2f}秒")
            
            return {
                'success': True,
                'result': result,
                'execution_time': execution_time,
                'timestamp': end_time.isoformat()
            }
            
        except Exception as e:
            self.stats['tasks_failed'] += 1
            self.status = "error"
            self.logger.error(f"Agent {self.name} 任务执行失败: {task_type}, 错误: {e}")
            
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    @abstractmethod
    async def _handle_task(self, task_type: str, **kwargs) -> Any:
        """
        处理具体任务的抽象方法，子类必须实现
        
        Args:
            task_type: 任务类型
            **kwargs: 任务参数
            
        Returns:
            任务处理结果
        """
        pass
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取Agent状态信息
        
        Returns:
            状态信息字典
        """
        return {
            'name': self.name,
            'status': self.status,
            'created_at': self.created_at.isoformat(),
            'last_activity': self.last_activity.isoformat(),
            'stats': self.stats.copy(),
            'config': self.config.copy()
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'tasks_completed': 0,
            'tasks_failed': 0,
            'total_execution_time': 0,
            'average_execution_time': 0
        }
        self.logger.info(f"Agent {self.name} 统计信息已重置")
    
    async def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            健康状态信息
        """
        try:
            # 基本健康检查
            health_status = {
                'healthy': True,
                'status': self.status,
                'uptime_seconds': (datetime.now() - self.created_at).total_seconds(),
                'last_activity_seconds_ago': (datetime.now() - self.last_activity).total_seconds(),
                'success_rate': 0,
                'issues': []
            }
            
            # 计算成功率
            total_tasks = self.stats['tasks_completed'] + self.stats['tasks_failed']
            if total_tasks > 0:
                health_status['success_rate'] = self.stats['tasks_completed'] / total_tasks * 100
            
            # 检查潜在问题
            if self.stats['tasks_failed'] > self.stats['tasks_completed']:
                health_status['healthy'] = False
                health_status['issues'].append('失败任务数量过多')
            
            if health_status['last_activity_seconds_ago'] > 3600:  # 1小时无活动
                health_status['issues'].append('长时间无活动')
            
            if self.status == "error":
                health_status['healthy'] = False
                health_status['issues'].append('Agent处于错误状态')
            
            return health_status
            
        except Exception as e:
            return {
                'healthy': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def configure(self, new_config: Dict[str, Any]):
        """
        更新配置
        
        Args:
            new_config: 新的配置参数
        """
        self.config.update(new_config)
        self.logger.info(f"Agent {self.name} 配置已更新")
    
    def get_capabilities(self) -> List[str]:
        """
        获取Agent能力列表
        
        Returns:
            能力列表
        """
        # 基类提供通用能力
        return [
            'task_execution',
            'status_monitoring',
            'health_check',
            'configuration_management'
        ]
    
    async def shutdown(self):
        """优雅关闭Agent"""
        try:
            self.logger.info(f"Agent {self.name} 开始关闭...")
            
            # 等待当前任务完成
            if self.status == "running":
                self.logger.info(f"等待Agent {self.name} 当前任务完成...")
                while self.status == "running":
                    await asyncio.sleep(0.1)
            
            self.status = "shutdown"
            self.logger.info(f"Agent {self.name} 已关闭")
            
        except Exception as e:
            self.logger.error(f"Agent {self.name} 关闭失败: {e}")
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"Agent({self.name}, status={self.status})"
    
    def __repr__(self) -> str:
        """详细字符串表示"""
        return (f"Agent(name='{self.name}', status='{self.status}', "
                f"tasks_completed={self.stats['tasks_completed']}, "
                f"tasks_failed={self.stats['tasks_failed']})")


class SpecializedAgent(BaseAgent):
    """专业化Agent基类，提供更多专业功能"""
    
    def __init__(self, name: str, config: Dict[str, Any], specialization: str):
        """
        初始化专业化Agent
        
        Args:
            name: Agent名称
            config: 配置参数
            specialization: 专业领域
        """
        super().__init__(name, config)
        self.specialization = specialization
        self.expertise_level = config.get('expertise_level', 'intermediate')
        
        # 专业化统计
        self.specialized_stats = {
            'domain_tasks_completed': 0,
            'domain_success_rate': 0,
            'expertise_score': 0
        }
    
    def get_specialization_info(self) -> Dict[str, Any]:
        """
        获取专业化信息
        
        Returns:
            专业化信息字典
        """
        return {
            'specialization': self.specialization,
            'expertise_level': self.expertise_level,
            'specialized_stats': self.specialized_stats.copy()
        }
    
    def update_expertise(self, performance_score: float):
        """
        根据表现更新专业水平
        
        Args:
            performance_score: 表现评分 (0-100)
        """
        try:
            # 简化的专业水平更新逻辑
            current_score = self.specialized_stats.get('expertise_score', 50)
            new_score = (current_score * 0.9) + (performance_score * 0.1)
            self.specialized_stats['expertise_score'] = new_score
            
            # 更新专业等级
            if new_score >= 90:
                self.expertise_level = 'expert'
            elif new_score >= 70:
                self.expertise_level = 'advanced'
            elif new_score >= 50:
                self.expertise_level = 'intermediate'
            else:
                self.expertise_level = 'beginner'
            
            self.logger.info(f"Agent {self.name} 专业水平更新为: {self.expertise_level}")
            
        except Exception as e:
            self.logger.warning(f"更新专业水平失败: {e}")
    
    def get_capabilities(self) -> List[str]:
        """获取专业化Agent能力列表"""
        base_capabilities = super().get_capabilities()
        specialized_capabilities = [
            f'{self.specialization}_analysis',
            f'{self.specialization}_reporting',
            'expertise_management',
            'specialized_task_handling'
        ]
        return base_capabilities + specialized_capabilities
