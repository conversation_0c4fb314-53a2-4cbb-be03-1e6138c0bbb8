#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财务分析引擎
提供专业的财务分析功能
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple


class FinancialAnalysisEngine:
    """财务分析引擎"""
    
    def __init__(self):
        """初始化财务分析引擎"""
        self.logger = logging.getLogger(__name__)
        
    def calculate_financial_ratios(self, financial_data: Dict[str, Any]) -> Dict[str, float]:
        """
        计算财务比率
        
        Args:
            financial_data: 财务数据
            
        Returns:
            财务比率字典
        """
        try:
            # 提取财务数据
            revenue = financial_data.get('revenue', 0)
            net_income = financial_data.get('net_income', 0)
            total_assets = financial_data.get('total_assets', 0)
            total_equity = financial_data.get('total_equity', 0)
            current_assets = financial_data.get('current_assets', 0)
            current_liabilities = financial_data.get('current_liabilities', 0)
            total_debt = financial_data.get('total_debt', 0)
            
            ratios = {}
            
            # 盈利能力比率
            ratios['roe'] = (net_income / total_equity * 100) if total_equity > 0 else 0
            ratios['roa'] = (net_income / total_assets * 100) if total_assets > 0 else 0
            ratios['net_profit_margin'] = (net_income / revenue * 100) if revenue > 0 else 0
            ratios['gross_profit_margin'] = financial_data.get('gross_profit_margin', 0)
            
            # 偿债能力比率
            ratios['current_ratio'] = (current_assets / current_liabilities) if current_liabilities > 0 else 0
            ratios['debt_to_equity'] = (total_debt / total_equity) if total_equity > 0 else 0
            ratios['debt_to_assets'] = (total_debt / total_assets) if total_assets > 0 else 0
            ratios['interest_coverage'] = financial_data.get('interest_coverage', 0)
            
            # 运营效率比率
            ratios['asset_turnover'] = (revenue / total_assets) if total_assets > 0 else 0
            ratios['inventory_turnover'] = financial_data.get('inventory_turnover', 0)
            ratios['receivables_turnover'] = financial_data.get('receivables_turnover', 0)
            
            # 市场表现比率
            ratios['pe_ratio'] = financial_data.get('pe_ratio', 0)
            ratios['pb_ratio'] = financial_data.get('pb_ratio', 0)
            ratios['dividend_yield'] = financial_data.get('dividend_yield', 0)
            
            return ratios
            
        except Exception as e:
            self.logger.error(f"计算财务比率失败: {e}")
            return {}
    
    def analyze_profitability(self, financial_data: Dict[str, Any], 
                            historical_data: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        盈利能力分析
        
        Args:
            financial_data: 当前财务数据
            historical_data: 历史财务数据
            
        Returns:
            盈利能力分析结果
        """
        try:
            ratios = self.calculate_financial_ratios(financial_data)
            
            analysis = {
                'current_profitability': {
                    'roe': ratios.get('roe', 0),
                    'roa': ratios.get('roa', 0),
                    'net_profit_margin': ratios.get('net_profit_margin', 0),
                    'gross_profit_margin': ratios.get('gross_profit_margin', 0)
                },
                'profitability_score': self._calculate_profitability_score(ratios),
                'profitability_trend': 'stable',
                'benchmark_comparison': {},
                'improvement_areas': []
            }
            
            # 如果有历史数据，分析趋势
            if historical_data:
                analysis['profitability_trend'] = self._analyze_profitability_trend(historical_data)
            
            # 基准比较
            analysis['benchmark_comparison'] = self._compare_with_benchmark(ratios, 'profitability')
            
            # 改进建议
            analysis['improvement_areas'] = self._identify_improvement_areas(ratios, 'profitability')
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"盈利能力分析失败: {e}")
            return {}
    
    def analyze_solvency(self, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        偿债能力分析
        
        Args:
            financial_data: 财务数据
            
        Returns:
            偿债能力分析结果
        """
        try:
            ratios = self.calculate_financial_ratios(financial_data)
            
            analysis = {
                'liquidity_ratios': {
                    'current_ratio': ratios.get('current_ratio', 0),
                    'quick_ratio': financial_data.get('quick_ratio', 0),
                    'cash_ratio': financial_data.get('cash_ratio', 0)
                },
                'leverage_ratios': {
                    'debt_to_equity': ratios.get('debt_to_equity', 0),
                    'debt_to_assets': ratios.get('debt_to_assets', 0),
                    'interest_coverage': ratios.get('interest_coverage', 0)
                },
                'solvency_score': self._calculate_solvency_score(ratios),
                'risk_level': self._assess_financial_risk(ratios),
                'recommendations': []
            }
            
            # 风险评估
            analysis['risk_level'] = self._assess_financial_risk(ratios)
            
            # 改进建议
            analysis['recommendations'] = self._generate_solvency_recommendations(ratios)
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"偿债能力分析失败: {e}")
            return {}
    
    def analyze_efficiency(self, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        运营效率分析
        
        Args:
            financial_data: 财务数据
            
        Returns:
            运营效率分析结果
        """
        try:
            ratios = self.calculate_financial_ratios(financial_data)
            
            analysis = {
                'turnover_ratios': {
                    'asset_turnover': ratios.get('asset_turnover', 0),
                    'inventory_turnover': ratios.get('inventory_turnover', 0),
                    'receivables_turnover': ratios.get('receivables_turnover', 0)
                },
                'efficiency_score': self._calculate_efficiency_score(ratios),
                'operational_efficiency': 'good',
                'cycle_analysis': {},
                'improvement_opportunities': []
            }
            
            # 营运周期分析
            analysis['cycle_analysis'] = self._analyze_operating_cycle(financial_data)
            
            # 改进机会
            analysis['improvement_opportunities'] = self._identify_efficiency_improvements(ratios)
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"运营效率分析失败: {e}")
            return {}
    
    def analyze_growth(self, financial_data: Dict[str, Any], 
                      historical_data: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        成长性分析
        
        Args:
            financial_data: 当前财务数据
            historical_data: 历史财务数据
            
        Returns:
            成长性分析结果
        """
        try:
            analysis = {
                'growth_rates': {},
                'growth_quality': 'moderate',
                'growth_sustainability': 'moderate',
                'growth_drivers': [],
                'growth_risks': []
            }
            
            if historical_data and len(historical_data) >= 2:
                # 计算增长率
                analysis['growth_rates'] = self._calculate_growth_rates(financial_data, historical_data)
                
                # 评估增长质量
                analysis['growth_quality'] = self._assess_growth_quality(analysis['growth_rates'])
                
                # 评估增长可持续性
                analysis['growth_sustainability'] = self._assess_growth_sustainability(historical_data)
            
            # 识别增长驱动因素
            analysis['growth_drivers'] = self._identify_growth_drivers(financial_data)
            
            # 识别增长风险
            analysis['growth_risks'] = self._identify_growth_risks(financial_data)
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"成长性分析失败: {e}")
            return {}
    
    def perform_dupont_analysis(self, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        杜邦分析
        
        Args:
            financial_data: 财务数据
            
        Returns:
            杜邦分析结果
        """
        try:
            ratios = self.calculate_financial_ratios(financial_data)
            
            # 杜邦分析组成部分
            net_profit_margin = ratios.get('net_profit_margin', 0) / 100
            asset_turnover = ratios.get('asset_turnover', 0)
            equity_multiplier = 1 + ratios.get('debt_to_equity', 0)
            
            # 计算ROE
            roe_calculated = net_profit_margin * asset_turnover * equity_multiplier * 100
            
            analysis = {
                'roe_breakdown': {
                    'net_profit_margin': net_profit_margin * 100,
                    'asset_turnover': asset_turnover,
                    'equity_multiplier': equity_multiplier,
                    'calculated_roe': roe_calculated
                },
                'performance_drivers': self._identify_roe_drivers(ratios),
                'improvement_strategies': self._suggest_roe_improvements(ratios)
            }
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"杜邦分析失败: {e}")
            return {}
    
    def _calculate_profitability_score(self, ratios: Dict[str, float]) -> float:
        """计算盈利能力评分"""
        try:
            roe = ratios.get('roe', 0)
            roa = ratios.get('roa', 0)
            npm = ratios.get('net_profit_margin', 0)
            
            # 简化评分逻辑
            score = min(100, (roe * 0.4 + roa * 0.3 + npm * 0.3))
            return max(0, score)
        except:
            return 0
    
    def _calculate_solvency_score(self, ratios: Dict[str, float]) -> float:
        """计算偿债能力评分"""
        try:
            current_ratio = ratios.get('current_ratio', 0)
            debt_to_equity = ratios.get('debt_to_equity', 0)
            
            # 简化评分逻辑
            liquidity_score = min(50, current_ratio * 25)
            leverage_score = max(0, 50 - debt_to_equity * 10)
            
            return liquidity_score + leverage_score
        except:
            return 0
    
    def _calculate_efficiency_score(self, ratios: Dict[str, float]) -> float:
        """计算运营效率评分"""
        try:
            asset_turnover = ratios.get('asset_turnover', 0)
            inventory_turnover = ratios.get('inventory_turnover', 0)
            
            # 简化评分逻辑
            score = min(100, (asset_turnover * 30 + inventory_turnover * 5))
            return max(0, score)
        except:
            return 0
    
    def _analyze_profitability_trend(self, historical_data: List[Dict[str, Any]]) -> str:
        """分析盈利能力趋势"""
        try:
            if len(historical_data) < 2:
                return 'insufficient_data'
            
            # 简化趋势分析
            recent_roe = historical_data[-1].get('roe', 0)
            previous_roe = historical_data[-2].get('roe', 0)
            
            if recent_roe > previous_roe * 1.05:
                return 'improving'
            elif recent_roe < previous_roe * 0.95:
                return 'declining'
            else:
                return 'stable'
        except:
            return 'unknown'
    
    def _compare_with_benchmark(self, ratios: Dict[str, float], category: str) -> Dict[str, Any]:
        """与基准比较"""
        # 简化基准比较
        benchmarks = {
            'profitability': {
                'roe': 15.0,
                'roa': 5.0,
                'net_profit_margin': 10.0
            }
        }
        
        benchmark = benchmarks.get(category, {})
        comparison = {}
        
        for metric, value in ratios.items():
            if metric in benchmark:
                benchmark_value = benchmark[metric]
                comparison[metric] = {
                    'company_value': value,
                    'benchmark_value': benchmark_value,
                    'relative_performance': 'above' if value > benchmark_value else 'below'
                }
        
        return comparison
    
    def _identify_improvement_areas(self, ratios: Dict[str, float], category: str) -> List[str]:
        """识别改进领域"""
        improvements = []
        
        if category == 'profitability':
            if ratios.get('net_profit_margin', 0) < 5:
                improvements.append('提高净利润率')
            if ratios.get('roe', 0) < 10:
                improvements.append('提高净资产收益率')
        
        return improvements
    
    def _assess_financial_risk(self, ratios: Dict[str, float]) -> str:
        """评估财务风险"""
        debt_to_equity = ratios.get('debt_to_equity', 0)
        current_ratio = ratios.get('current_ratio', 0)
        
        if debt_to_equity > 2 or current_ratio < 1:
            return 'high'
        elif debt_to_equity > 1 or current_ratio < 1.5:
            return 'moderate'
        else:
            return 'low'
    
    def _generate_solvency_recommendations(self, ratios: Dict[str, float]) -> List[str]:
        """生成偿债能力建议"""
        recommendations = []
        
        if ratios.get('current_ratio', 0) < 1.2:
            recommendations.append('提高流动比率，增强短期偿债能力')
        
        if ratios.get('debt_to_equity', 0) > 1.5:
            recommendations.append('降低负债比率，优化资本结构')
        
        return recommendations
    
    def _analyze_operating_cycle(self, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析营运周期"""
        return {
            'days_sales_outstanding': financial_data.get('dso', 0),
            'days_inventory_outstanding': financial_data.get('dio', 0),
            'days_payable_outstanding': financial_data.get('dpo', 0),
            'cash_conversion_cycle': financial_data.get('ccc', 0)
        }
    
    def _identify_efficiency_improvements(self, ratios: Dict[str, float]) -> List[str]:
        """识别效率改进机会"""
        improvements = []
        
        if ratios.get('asset_turnover', 0) < 0.5:
            improvements.append('提高资产周转率')
        
        if ratios.get('inventory_turnover', 0) < 4:
            improvements.append('加快存货周转')
        
        return improvements
    
    def _calculate_growth_rates(self, current_data: Dict[str, Any], 
                              historical_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """计算增长率"""
        growth_rates = {}
        
        if len(historical_data) >= 1:
            previous_data = historical_data[-1]
            
            for metric in ['revenue', 'net_income', 'total_assets']:
                current_value = current_data.get(metric, 0)
                previous_value = previous_data.get(metric, 0)
                
                if previous_value > 0:
                    growth_rate = (current_value - previous_value) / previous_value * 100
                    growth_rates[f'{metric}_growth'] = growth_rate
        
        return growth_rates
    
    def _assess_growth_quality(self, growth_rates: Dict[str, float]) -> str:
        """评估增长质量"""
        revenue_growth = growth_rates.get('revenue_growth', 0)
        income_growth = growth_rates.get('net_income_growth', 0)
        
        if income_growth > revenue_growth and revenue_growth > 0:
            return 'high'
        elif revenue_growth > 0:
            return 'moderate'
        else:
            return 'low'
    
    def _assess_growth_sustainability(self, historical_data: List[Dict[str, Any]]) -> str:
        """评估增长可持续性"""
        # 简化可持续性评估
        return 'moderate'
    
    def _identify_growth_drivers(self, financial_data: Dict[str, Any]) -> List[str]:
        """识别增长驱动因素"""
        return ['市场扩张', '产品创新', '运营效率提升']
    
    def _identify_growth_risks(self, financial_data: Dict[str, Any]) -> List[str]:
        """识别增长风险"""
        return ['市场竞争加剧', '成本上升', '监管变化']
    
    def _identify_roe_drivers(self, ratios: Dict[str, float]) -> List[str]:
        """识别ROE驱动因素"""
        drivers = []
        
        if ratios.get('net_profit_margin', 0) > 10:
            drivers.append('高净利润率')
        
        if ratios.get('asset_turnover', 0) > 1:
            drivers.append('高资产周转率')
        
        return drivers
    
    def _suggest_roe_improvements(self, ratios: Dict[str, float]) -> List[str]:
        """建议ROE改进策略"""
        suggestions = []
        
        if ratios.get('net_profit_margin', 0) < 5:
            suggestions.append('提高净利润率')
        
        if ratios.get('asset_turnover', 0) < 0.5:
            suggestions.append('提高资产周转率')
        
        return suggestions
