#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业财务分析引擎
基于用户提供的财务知识，实现ROE分解、财务比率计算、行业对比分析、估值模型等专业分析功能
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import math

class AnalysisType(Enum):
    """分析类型"""
    DUPONT_ROE = "dupont_roe"
    FINANCIAL_RATIOS = "financial_ratios"
    INDUSTRY_COMPARISON = "industry_comparison"
    VALUATION_MODEL = "valuation_model"
    TREND_ANALYSIS = "trend_analysis"
    RISK_ASSESSMENT = "risk_assessment"
    CASH_FLOW_ANALYSIS = "cash_flow_analysis"
    PROFITABILITY_ANALYSIS = "profitability_analysis"

@dataclass
class FinancialData:
    """财务数据结构"""
    # 资产负债表数据
    total_assets: float
    total_equity: float
    total_liabilities: float
    current_assets: float
    current_liabilities: float
    inventory: float
    accounts_receivable: float
    cash_and_equivalents: float
    
    # 利润表数据
    revenue: float
    net_income: float
    gross_profit: float
    operating_income: float
    ebit: float
    ebitda: float
    
    # 现金流量表数据
    operating_cash_flow: float
    investing_cash_flow: float
    financing_cash_flow: float
    free_cash_flow: float
    
    # 市场数据
    market_cap: float
    shares_outstanding: float
    stock_price: float
    
    # 时间标识
    period: str
    currency: str = "HKD"

@dataclass
class AnalysisResult:
    """分析结果结构"""
    analysis_type: AnalysisType
    metrics: Dict[str, float]
    interpretation: Dict[str, str]
    recommendations: List[str]
    risk_factors: List[str]
    data_quality: float
    timestamp: datetime

class ProfessionalFinancialAnalyzer:
    """专业财务分析引擎"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化财务分析引擎
        
        Args:
            config: 配置参数
        """
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # 行业基准数据
        self.industry_benchmarks = {
            'ai_software': {
                'roe': 0.15,
                'roa': 0.08,
                'gross_margin': 0.70,
                'operating_margin': 0.20,
                'current_ratio': 2.0,
                'debt_to_equity': 0.3,
                'pe_ratio': 25.0,
                'pb_ratio': 3.0
            },
            'fintech': {
                'roe': 0.18,
                'roa': 0.12,
                'gross_margin': 0.65,
                'operating_margin': 0.25,
                'current_ratio': 1.8,
                'debt_to_equity': 0.4,
                'pe_ratio': 20.0,
                'pb_ratio': 2.5
            }
        }
        
        # 分析阈值
        self.analysis_thresholds = {
            'excellent': 0.8,
            'good': 0.6,
            'fair': 0.4,
            'poor': 0.2
        }

    def dupont_roe_analysis(self, financial_data: FinancialData, industry: str = 'ai_software') -> AnalysisResult:
        """
        杜邦ROE分解分析
        ROE = 净利润率 × 资产周转率 × 权益乘数
        """
        try:
            # 计算杜邦分解组件
            net_profit_margin = financial_data.net_income / financial_data.revenue if financial_data.revenue > 0 else 0
            asset_turnover = financial_data.revenue / financial_data.total_assets if financial_data.total_assets > 0 else 0
            equity_multiplier = financial_data.total_assets / financial_data.total_equity if financial_data.total_equity > 0 else 0
            
            # 计算ROE
            roe = net_profit_margin * asset_turnover * equity_multiplier
            
            # 进一步分解净利润率
            gross_margin = financial_data.gross_profit / financial_data.revenue if financial_data.revenue > 0 else 0
            operating_margin = financial_data.operating_income / financial_data.revenue if financial_data.revenue > 0 else 0
            
            # 税负和利息负担
            tax_burden = financial_data.net_income / financial_data.ebit if financial_data.ebit != 0 else 0
            interest_burden = financial_data.ebit / financial_data.operating_income if financial_data.operating_income != 0 else 0
            
            metrics = {
                'roe': roe,
                'net_profit_margin': net_profit_margin,
                'asset_turnover': asset_turnover,
                'equity_multiplier': equity_multiplier,
                'gross_margin': gross_margin,
                'operating_margin': operating_margin,
                'tax_burden': tax_burden,
                'interest_burden': interest_burden
            }
            
            # 与行业基准对比
            benchmark = self.industry_benchmarks.get(industry, self.industry_benchmarks['ai_software'])
            
            interpretation = {
                'roe_level': self._interpret_ratio(roe, benchmark['roe'], 'higher_better'),
                'profitability': self._interpret_ratio(net_profit_margin, 0.1, 'higher_better'),
                'efficiency': self._interpret_ratio(asset_turnover, 0.5, 'higher_better'),
                'leverage': self._interpret_ratio(equity_multiplier, 2.0, 'moderate_better'),
                'overall_assessment': self._assess_dupont_performance(metrics, benchmark)
            }
            
            recommendations = self._generate_dupont_recommendations(metrics, benchmark)
            risk_factors = self._identify_dupont_risks(metrics)
            
            return AnalysisResult(
                analysis_type=AnalysisType.DUPONT_ROE,
                metrics=metrics,
                interpretation=interpretation,
                recommendations=recommendations,
                risk_factors=risk_factors,
                data_quality=self._assess_data_quality(financial_data),
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"杜邦ROE分析失败: {e}")
            raise e

    def comprehensive_ratio_analysis(self, financial_data: FinancialData, industry: str = 'ai_software') -> AnalysisResult:
        """
        综合财务比率分析
        包括盈利能力、偿债能力、运营效率、市场价值比率
        """
        try:
            # 盈利能力比率
            profitability_ratios = {
                'gross_margin': financial_data.gross_profit / financial_data.revenue if financial_data.revenue > 0 else 0,
                'operating_margin': financial_data.operating_income / financial_data.revenue if financial_data.revenue > 0 else 0,
                'net_margin': financial_data.net_income / financial_data.revenue if financial_data.revenue > 0 else 0,
                'roe': financial_data.net_income / financial_data.total_equity if financial_data.total_equity > 0 else 0,
                'roa': financial_data.net_income / financial_data.total_assets if financial_data.total_assets > 0 else 0,
                'roic': financial_data.operating_income / (financial_data.total_assets - financial_data.current_liabilities) if (financial_data.total_assets - financial_data.current_liabilities) > 0 else 0
            }
            
            # 偿债能力比率
            solvency_ratios = {
                'current_ratio': financial_data.current_assets / financial_data.current_liabilities if financial_data.current_liabilities > 0 else 0,
                'quick_ratio': (financial_data.current_assets - financial_data.inventory) / financial_data.current_liabilities if financial_data.current_liabilities > 0 else 0,
                'debt_to_equity': financial_data.total_liabilities / financial_data.total_equity if financial_data.total_equity > 0 else 0,
                'debt_to_assets': financial_data.total_liabilities / financial_data.total_assets if financial_data.total_assets > 0 else 0,
                'interest_coverage': financial_data.ebit / (financial_data.ebit - financial_data.operating_income) if (financial_data.ebit - financial_data.operating_income) != 0 else float('inf')
            }
            
            # 运营效率比率
            efficiency_ratios = {
                'asset_turnover': financial_data.revenue / financial_data.total_assets if financial_data.total_assets > 0 else 0,
                'inventory_turnover': financial_data.revenue / financial_data.inventory if financial_data.inventory > 0 else 0,
                'receivables_turnover': financial_data.revenue / financial_data.accounts_receivable if financial_data.accounts_receivable > 0 else 0,
                'cash_conversion_cycle': self._calculate_cash_conversion_cycle(financial_data)
            }
            
            # 市场价值比率
            market_ratios = {
                'pe_ratio': financial_data.market_cap / financial_data.net_income if financial_data.net_income > 0 else float('inf'),
                'pb_ratio': financial_data.market_cap / financial_data.total_equity if financial_data.total_equity > 0 else 0,
                'price_to_sales': financial_data.market_cap / financial_data.revenue if financial_data.revenue > 0 else 0,
                'ev_ebitda': (financial_data.market_cap + financial_data.total_liabilities - financial_data.cash_and_equivalents) / financial_data.ebitda if financial_data.ebitda > 0 else float('inf')
            }
            
            # 合并所有比率
            all_metrics = {
                **profitability_ratios,
                **solvency_ratios,
                **efficiency_ratios,
                **market_ratios
            }
            
            # 与行业基准对比和解释
            benchmark = self.industry_benchmarks.get(industry, self.industry_benchmarks['ai_software'])
            interpretation = self._interpret_comprehensive_ratios(all_metrics, benchmark)
            
            recommendations = self._generate_ratio_recommendations(all_metrics, benchmark)
            risk_factors = self._identify_ratio_risks(all_metrics)
            
            return AnalysisResult(
                analysis_type=AnalysisType.FINANCIAL_RATIOS,
                metrics=all_metrics,
                interpretation=interpretation,
                recommendations=recommendations,
                risk_factors=risk_factors,
                data_quality=self._assess_data_quality(financial_data),
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"综合比率分析失败: {e}")
            raise e

    def valuation_analysis(self, financial_data: FinancialData, growth_assumptions: Dict[str, float] = None) -> AnalysisResult:
        """
        估值分析
        包括DCF模型、相对估值法等
        """
        try:
            if growth_assumptions is None:
                growth_assumptions = {
                    'revenue_growth': 0.15,  # 15%营收增长
                    'margin_improvement': 0.02,  # 2%利润率改善
                    'terminal_growth': 0.03,  # 3%永续增长率
                    'discount_rate': 0.12  # 12%折现率
                }
            
            # DCF估值
            dcf_value = self._calculate_dcf_valuation(financial_data, growth_assumptions)
            
            # 相对估值
            relative_valuation = self._calculate_relative_valuation(financial_data)
            
            # 资产基础估值
            book_value = financial_data.total_equity
            
            metrics = {
                'dcf_value_per_share': dcf_value / financial_data.shares_outstanding if financial_data.shares_outstanding > 0 else 0,
                'current_price': financial_data.stock_price,
                'relative_value_pe': relative_valuation['pe_based_value'],
                'relative_value_pb': relative_valuation['pb_based_value'],
                'book_value_per_share': book_value / financial_data.shares_outstanding if financial_data.shares_outstanding > 0 else 0,
                'upside_downside_dcf': (dcf_value / financial_data.shares_outstanding - financial_data.stock_price) / financial_data.stock_price if financial_data.stock_price > 0 else 0
            }
            
            interpretation = self._interpret_valuation(metrics, financial_data)
            recommendations = self._generate_valuation_recommendations(metrics)
            risk_factors = self._identify_valuation_risks(financial_data, growth_assumptions)
            
            return AnalysisResult(
                analysis_type=AnalysisType.VALUATION_MODEL,
                metrics=metrics,
                interpretation=interpretation,
                recommendations=recommendations,
                risk_factors=risk_factors,
                data_quality=self._assess_data_quality(financial_data),
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"估值分析失败: {e}")
            raise e

    def _calculate_cash_conversion_cycle(self, financial_data: FinancialData) -> float:
        """计算现金转换周期"""
        try:
            # 应收账款周转天数
            receivables_days = (financial_data.accounts_receivable / financial_data.revenue) * 365 if financial_data.revenue > 0 else 0

            # 存货周转天数
            inventory_days = (financial_data.inventory / financial_data.revenue) * 365 if financial_data.revenue > 0 else 0

            # 应付账款周转天数（假设为流动负债的30%）
            payables_days = ((financial_data.current_liabilities * 0.3) / financial_data.revenue) * 365 if financial_data.revenue > 0 else 0

            return receivables_days + inventory_days - payables_days
        except:
            return 0

    def _calculate_dcf_valuation(self, financial_data: FinancialData, growth_assumptions: Dict[str, float]) -> float:
        """计算DCF估值"""
        try:
            # 预测期现金流（5年）
            forecast_years = 5
            terminal_growth = growth_assumptions['terminal_growth']
            discount_rate = growth_assumptions['discount_rate']

            # 基础自由现金流
            base_fcf = financial_data.free_cash_flow
            if base_fcf <= 0:
                # 如果FCF为负，使用调整后的净利润
                base_fcf = financial_data.net_income * 0.8

            # 预测现金流
            forecast_fcf = []
            current_fcf = base_fcf

            for year in range(1, forecast_years + 1):
                # 逐年递减的增长率
                growth_rate = growth_assumptions['revenue_growth'] * (0.9 ** (year - 1))
                current_fcf *= (1 + growth_rate)

                # 折现到现值
                pv_fcf = current_fcf / ((1 + discount_rate) ** year)
                forecast_fcf.append(pv_fcf)

            # 终值计算
            terminal_fcf = current_fcf * (1 + terminal_growth)
            terminal_value = terminal_fcf / (discount_rate - terminal_growth)
            pv_terminal_value = terminal_value / ((1 + discount_rate) ** forecast_years)

            # 企业价值
            enterprise_value = sum(forecast_fcf) + pv_terminal_value

            # 股权价值 = 企业价值 - 净债务
            net_debt = financial_data.total_liabilities - financial_data.cash_and_equivalents
            equity_value = enterprise_value - net_debt

            return max(0, equity_value)

        except Exception as e:
            self.logger.warning(f"DCF计算失败: {e}")
            return 0

    def _calculate_relative_valuation(self, financial_data: FinancialData) -> Dict[str, float]:
        """计算相对估值"""
        try:
            # 行业平均倍数（AI软件行业）
            industry_pe = 25.0
            industry_pb = 3.0
            industry_ps = 8.0

            # 基于PE的估值
            pe_based_value = financial_data.net_income * industry_pe if financial_data.net_income > 0 else 0

            # 基于PB的估值
            pb_based_value = financial_data.total_equity * industry_pb

            # 基于PS的估值
            ps_based_value = financial_data.revenue * industry_ps

            return {
                'pe_based_value': pe_based_value,
                'pb_based_value': pb_based_value,
                'ps_based_value': ps_based_value
            }

        except Exception as e:
            self.logger.warning(f"相对估值计算失败: {e}")
            return {'pe_based_value': 0, 'pb_based_value': 0, 'ps_based_value': 0}

    def _interpret_ratio(self, value: float, benchmark: float, comparison_type: str) -> str:
        """解释比率表现"""
        if comparison_type == 'higher_better':
            if value >= benchmark * 1.2:
                return 'excellent'
            elif value >= benchmark:
                return 'good'
            elif value >= benchmark * 0.8:
                return 'fair'
            else:
                return 'poor'
        elif comparison_type == 'lower_better':
            if value <= benchmark * 0.8:
                return 'excellent'
            elif value <= benchmark:
                return 'good'
            elif value <= benchmark * 1.2:
                return 'fair'
            else:
                return 'poor'
        elif comparison_type == 'moderate_better':
            if 0.8 <= value / benchmark <= 1.2:
                return 'excellent'
            elif 0.6 <= value / benchmark <= 1.4:
                return 'good'
            elif 0.4 <= value / benchmark <= 1.6:
                return 'fair'
            else:
                return 'poor'
        else:
            return 'unknown'

    def _assess_dupont_performance(self, metrics: Dict[str, float], benchmark: Dict[str, float]) -> str:
        """评估杜邦分析整体表现"""
        try:
            roe_score = 1 if metrics['roe'] >= benchmark['roe'] else 0
            margin_score = 1 if metrics['net_profit_margin'] >= 0.1 else 0
            turnover_score = 1 if metrics['asset_turnover'] >= 0.5 else 0
            leverage_score = 1 if 1.5 <= metrics['equity_multiplier'] <= 3.0 else 0

            total_score = roe_score + margin_score + turnover_score + leverage_score

            if total_score >= 3:
                return 'excellent'
            elif total_score >= 2:
                return 'good'
            elif total_score >= 1:
                return 'fair'
            else:
                return 'poor'
        except:
            return 'unknown'

    def _generate_dupont_recommendations(self, metrics: Dict[str, float], benchmark: Dict[str, float]) -> List[str]:
        """生成杜邦分析建议"""
        recommendations = []

        try:
            # ROE建议
            if metrics['roe'] < benchmark['roe']:
                recommendations.append("ROE低于行业平均，需要提升盈利能力、运营效率或适度增加财务杠杆")

            # 净利润率建议
            if metrics['net_profit_margin'] < 0.1:
                recommendations.append("净利润率偏低，建议优化成本结构，提升运营效率")

            # 资产周转率建议
            if metrics['asset_turnover'] < 0.5:
                recommendations.append("资产周转率较低，建议优化资产配置，提高资产使用效率")

            # 权益乘数建议
            if metrics['equity_multiplier'] < 1.5:
                recommendations.append("财务杠杆较低，可考虑适度增加债务融资以提升ROE")
            elif metrics['equity_multiplier'] > 3.0:
                recommendations.append("财务杠杆较高，需关注偿债风险")

            # 毛利率建议
            if metrics['gross_margin'] < benchmark['gross_margin']:
                recommendations.append("毛利率低于行业平均，建议提升产品定价能力或降低直接成本")

        except Exception as e:
            self.logger.warning(f"生成杜邦建议失败: {e}")
            recommendations.append("数据分析异常，建议进一步核实财务数据")

        return recommendations

    def _identify_dupont_risks(self, metrics: Dict[str, float]) -> List[str]:
        """识别杜邦分析风险"""
        risks = []

        try:
            if metrics['roe'] < 0:
                risks.append("ROE为负，公司盈利能力存在重大问题")

            if metrics['net_profit_margin'] < 0:
                risks.append("净利润率为负，公司处于亏损状态")

            if metrics['equity_multiplier'] > 4.0:
                risks.append("财务杠杆过高，存在较大的财务风险")

            if metrics['asset_turnover'] < 0.2:
                risks.append("资产周转率极低，资产使用效率堪忧")

            if metrics['tax_burden'] > 1.2:
                risks.append("税负异常，可能存在税务风险")

        except Exception as e:
            self.logger.warning(f"识别杜邦风险失败: {e}")
            risks.append("风险评估异常")

        return risks

    def _interpret_comprehensive_ratios(self, metrics: Dict[str, float], benchmark: Dict[str, float]) -> Dict[str, str]:
        """解释综合比率分析"""
        interpretation = {}

        try:
            # 盈利能力解释
            interpretation['profitability'] = self._interpret_ratio(metrics['roe'], benchmark['roe'], 'higher_better')
            interpretation['gross_margin'] = self._interpret_ratio(metrics['gross_margin'], benchmark['gross_margin'], 'higher_better')

            # 偿债能力解释
            interpretation['liquidity'] = self._interpret_ratio(metrics['current_ratio'], benchmark['current_ratio'], 'moderate_better')
            interpretation['leverage'] = self._interpret_ratio(metrics['debt_to_equity'], benchmark['debt_to_equity'], 'lower_better')

            # 运营效率解释
            interpretation['efficiency'] = self._interpret_ratio(metrics['asset_turnover'], 1.0, 'higher_better')

            # 市场估值解释
            interpretation['valuation'] = self._interpret_ratio(metrics['pe_ratio'], benchmark['pe_ratio'], 'moderate_better')

        except Exception as e:
            self.logger.warning(f"比率解释失败: {e}")
            interpretation['error'] = 'analysis_failed'

        return interpretation

    def _assess_data_quality(self, financial_data: FinancialData) -> float:
        """评估数据质量"""
        try:
            quality_score = 1.0

            # 检查关键数据完整性
            if financial_data.revenue <= 0:
                quality_score *= 0.7
            if financial_data.total_assets <= 0:
                quality_score *= 0.7
            if financial_data.total_equity <= 0:
                quality_score *= 0.8

            # 检查数据逻辑性
            if financial_data.total_assets < financial_data.total_equity:
                quality_score *= 0.8
            if financial_data.current_assets > financial_data.total_assets:
                quality_score *= 0.8

            return max(0.0, quality_score)

        except:
            return 0.5

    def _generate_ratio_recommendations(self, metrics: Dict[str, float], benchmark: Dict[str, float]) -> List[str]:
        """生成比率分析建议"""
        recommendations = []

        try:
            # 盈利能力建议
            if metrics['roe'] < benchmark['roe']:
                recommendations.append("ROE低于行业平均，建议通过提升净利润率、资产周转率或适度增加杠杆来改善")

            if metrics['gross_margin'] < benchmark['gross_margin']:
                recommendations.append("毛利率偏低，建议优化产品结构、提升定价能力或降低成本")

            # 偿债能力建议
            if metrics['current_ratio'] < 1.5:
                recommendations.append("流动比率偏低，需关注短期偿债能力")
            elif metrics['current_ratio'] > 3.0:
                recommendations.append("流动比率过高，可能存在资金使用效率问题")

            if metrics['debt_to_equity'] > 0.6:
                recommendations.append("负债权益比较高，需控制财务风险")

            # 运营效率建议
            if metrics['asset_turnover'] < 0.5:
                recommendations.append("资产周转率较低，建议提升资产使用效率")

            # 估值建议
            if metrics['pe_ratio'] > 30:
                recommendations.append("PE估值较高，需关注估值风险")
            elif metrics['pe_ratio'] < 15 and metrics['net_margin'] > 0:
                recommendations.append("PE估值相对较低，可能存在投资机会")

        except Exception as e:
            self.logger.warning(f"生成比率建议失败: {e}")
            recommendations.append("数据分析异常，建议进一步核实")

        return recommendations

    def _identify_ratio_risks(self, metrics: Dict[str, float]) -> List[str]:
        """识别比率分析风险"""
        risks = []

        try:
            # 盈利能力风险
            if metrics['net_margin'] < 0:
                risks.append("净利润率为负，公司盈利能力存在问题")

            if metrics['roe'] < 0:
                risks.append("ROE为负，股东回报率不佳")

            # 偿债能力风险
            if metrics['current_ratio'] < 1.0:
                risks.append("流动比率小于1，短期偿债能力不足")

            if metrics['debt_to_equity'] > 1.0:
                risks.append("负债权益比过高，财务杠杆风险较大")

            # 运营效率风险
            if metrics['asset_turnover'] < 0.3:
                risks.append("资产周转率过低，资产使用效率堪忧")

            # 估值风险
            if metrics['pe_ratio'] > 50:
                risks.append("PE估值过高，存在估值泡沫风险")

            if metrics['pb_ratio'] > 5:
                risks.append("PB估值过高，市场预期可能过于乐观")

        except Exception as e:
            self.logger.warning(f"识别比率风险失败: {e}")
            risks.append("风险评估异常")

        return risks

    def _interpret_valuation(self, metrics: Dict[str, float], financial_data: FinancialData) -> Dict[str, str]:
        """解释估值分析"""
        interpretation = {}

        try:
            # DCF估值解释
            dcf_upside = metrics['upside_downside_dcf']
            if dcf_upside > 0.2:
                interpretation['dcf_assessment'] = 'undervalued'
            elif dcf_upside > -0.2:
                interpretation['dcf_assessment'] = 'fairly_valued'
            else:
                interpretation['dcf_assessment'] = 'overvalued'

            # 相对估值解释
            current_pe = financial_data.market_cap / financial_data.net_income if financial_data.net_income > 0 else float('inf')
            if current_pe < 20:
                interpretation['relative_valuation'] = 'attractive'
            elif current_pe < 30:
                interpretation['relative_valuation'] = 'reasonable'
            else:
                interpretation['relative_valuation'] = 'expensive'

            # 综合评估
            if interpretation.get('dcf_assessment') == 'undervalued' and interpretation.get('relative_valuation') in ['attractive', 'reasonable']:
                interpretation['overall_assessment'] = 'buy'
            elif interpretation.get('dcf_assessment') == 'overvalued' or interpretation.get('relative_valuation') == 'expensive':
                interpretation['overall_assessment'] = 'sell'
            else:
                interpretation['overall_assessment'] = 'hold'

        except Exception as e:
            self.logger.warning(f"估值解释失败: {e}")
            interpretation['error'] = 'valuation_analysis_failed'

        return interpretation

    def _generate_valuation_recommendations(self, metrics: Dict[str, float]) -> List[str]:
        """生成估值建议"""
        recommendations = []

        try:
            dcf_upside = metrics['upside_downside_dcf']

            if dcf_upside > 0.3:
                recommendations.append("DCF估值显示股价被严重低估，建议积极买入")
            elif dcf_upside > 0.1:
                recommendations.append("DCF估值显示股价被低估，建议买入")
            elif dcf_upside > -0.1:
                recommendations.append("DCF估值显示股价基本合理，建议持有")
            elif dcf_upside > -0.3:
                recommendations.append("DCF估值显示股价被高估，建议减持")
            else:
                recommendations.append("DCF估值显示股价被严重高估，建议卖出")

            # 基于相对估值的建议
            if metrics['current_price'] < metrics['relative_value_pe'] * 0.8:
                recommendations.append("相对估值显示股价具有吸引力")

        except Exception as e:
            self.logger.warning(f"生成估值建议失败: {e}")
            recommendations.append("估值分析异常，建议谨慎投资")

        return recommendations

    def _identify_valuation_risks(self, financial_data: FinancialData, growth_assumptions: Dict[str, float]) -> List[str]:
        """识别估值风险"""
        risks = []

        try:
            # 增长假设风险
            if growth_assumptions['revenue_growth'] > 0.3:
                risks.append("营收增长假设过于乐观，存在预期风险")

            if growth_assumptions['discount_rate'] < 0.08:
                risks.append("折现率假设过低，可能低估投资风险")

            # 财务状况风险
            if financial_data.net_income < 0:
                risks.append("公司当前亏损，DCF估值存在较大不确定性")

            if financial_data.free_cash_flow < 0:
                risks.append("自由现金流为负，影响DCF估值可靠性")

            # 市场风险
            if financial_data.market_cap / financial_data.revenue > 10:
                risks.append("市销率过高，存在估值泡沫风险")

        except Exception as e:
            self.logger.warning(f"识别估值风险失败: {e}")
            risks.append("风险评估异常")

        return risks

    def industry_comparison_analysis(self, company_data: FinancialData, industry_data: List[FinancialData],
                                   industry_name: str = 'ai_software') -> AnalysisResult:
        """
        行业对比分析
        """
        try:
            # 计算行业平均指标
            industry_metrics = self._calculate_industry_averages(industry_data)

            # 计算公司指标
            company_metrics = {
                'roe': company_data.net_income / company_data.total_equity if company_data.total_equity > 0 else 0,
                'roa': company_data.net_income / company_data.total_assets if company_data.total_assets > 0 else 0,
                'gross_margin': company_data.gross_profit / company_data.revenue if company_data.revenue > 0 else 0,
                'operating_margin': company_data.operating_income / company_data.revenue if company_data.revenue > 0 else 0,
                'debt_to_equity': company_data.total_liabilities / company_data.total_equity if company_data.total_equity > 0 else 0,
                'current_ratio': company_data.current_assets / company_data.current_liabilities if company_data.current_liabilities > 0 else 0
            }

            # 计算相对排名
            rankings = self._calculate_industry_rankings(company_data, industry_data)

            # 合并指标
            all_metrics = {**company_metrics, **industry_metrics, **rankings}

            interpretation = self._interpret_industry_comparison(company_metrics, industry_metrics)
            recommendations = self._generate_industry_recommendations(company_metrics, industry_metrics, rankings)
            risk_factors = self._identify_industry_risks(company_metrics, industry_metrics)

            return AnalysisResult(
                analysis_type=AnalysisType.INDUSTRY_COMPARISON,
                metrics=all_metrics,
                interpretation=interpretation,
                recommendations=recommendations,
                risk_factors=risk_factors,
                data_quality=self._assess_data_quality(company_data),
                timestamp=datetime.now()
            )

        except Exception as e:
            self.logger.error(f"行业对比分析失败: {e}")
            raise e

    def _calculate_industry_averages(self, industry_data: List[FinancialData]) -> Dict[str, float]:
        """计算行业平均指标"""
        if not industry_data:
            return {}

        try:
            metrics = {
                'industry_avg_roe': 0,
                'industry_avg_roa': 0,
                'industry_avg_gross_margin': 0,
                'industry_avg_operating_margin': 0,
                'industry_avg_debt_to_equity': 0,
                'industry_avg_current_ratio': 0
            }

            valid_count = 0
            for data in industry_data:
                if data.total_equity > 0 and data.revenue > 0:
                    metrics['industry_avg_roe'] += data.net_income / data.total_equity
                    metrics['industry_avg_roa'] += data.net_income / data.total_assets if data.total_assets > 0 else 0
                    metrics['industry_avg_gross_margin'] += data.gross_profit / data.revenue
                    metrics['industry_avg_operating_margin'] += data.operating_income / data.revenue
                    metrics['industry_avg_debt_to_equity'] += data.total_liabilities / data.total_equity
                    metrics['industry_avg_current_ratio'] += data.current_assets / data.current_liabilities if data.current_liabilities > 0 else 0
                    valid_count += 1

            if valid_count > 0:
                for key in metrics:
                    metrics[key] /= valid_count

            return metrics

        except Exception as e:
            self.logger.warning(f"计算行业平均失败: {e}")
            return {}

    def _calculate_industry_rankings(self, company_data: FinancialData, industry_data: List[FinancialData]) -> Dict[str, int]:
        """计算行业排名"""
        try:
            # 计算公司ROE
            company_roe = company_data.net_income / company_data.total_equity if company_data.total_equity > 0 else 0

            # 计算行业ROE列表
            industry_roes = []
            for data in industry_data:
                if data.total_equity > 0:
                    roe = data.net_income / data.total_equity
                    industry_roes.append(roe)

            # 计算排名
            industry_roes.sort(reverse=True)
            roe_rank = len([roe for roe in industry_roes if roe > company_roe]) + 1

            return {
                'roe_rank': roe_rank,
                'total_companies': len(industry_roes),
                'percentile': (len(industry_roes) - roe_rank + 1) / len(industry_roes) * 100 if industry_roes else 0
            }

        except Exception as e:
            self.logger.warning(f"计算行业排名失败: {e}")
            return {'roe_rank': 0, 'total_companies': 0, 'percentile': 0}

    def _interpret_industry_comparison(self, company_metrics: Dict[str, float], industry_metrics: Dict[str, float]) -> Dict[str, str]:
        """解释行业对比"""
        interpretation = {}

        try:
            # ROE对比
            if company_metrics['roe'] > industry_metrics.get('industry_avg_roe', 0) * 1.1:
                interpretation['roe_comparison'] = 'significantly_above_average'
            elif company_metrics['roe'] > industry_metrics.get('industry_avg_roe', 0):
                interpretation['roe_comparison'] = 'above_average'
            elif company_metrics['roe'] > industry_metrics.get('industry_avg_roe', 0) * 0.9:
                interpretation['roe_comparison'] = 'near_average'
            else:
                interpretation['roe_comparison'] = 'below_average'

            # 毛利率对比
            if company_metrics['gross_margin'] > industry_metrics.get('industry_avg_gross_margin', 0) * 1.05:
                interpretation['margin_comparison'] = 'superior'
            elif company_metrics['gross_margin'] > industry_metrics.get('industry_avg_gross_margin', 0) * 0.95:
                interpretation['margin_comparison'] = 'competitive'
            else:
                interpretation['margin_comparison'] = 'inferior'

        except Exception as e:
            self.logger.warning(f"行业对比解释失败: {e}")
            interpretation['error'] = 'comparison_failed'

        return interpretation

    def _generate_industry_recommendations(self, company_metrics: Dict[str, float],
                                         industry_metrics: Dict[str, float], rankings: Dict[str, int]) -> List[str]:
        """生成行业对比建议"""
        recommendations = []

        try:
            percentile = rankings.get('percentile', 0)

            if percentile > 75:
                recommendations.append("公司在行业中表现优秀，建议保持竞争优势")
            elif percentile > 50:
                recommendations.append("公司表现高于行业平均，建议继续提升竞争力")
            elif percentile > 25:
                recommendations.append("公司表现低于行业平均，需要改善经营效率")
            else:
                recommendations.append("公司在行业中表现较差，需要重大改革")

            # 具体指标建议
            if company_metrics['roe'] < industry_metrics.get('industry_avg_roe', 0):
                recommendations.append("ROE低于行业平均，建议提升盈利能力")

            if company_metrics['gross_margin'] < industry_metrics.get('industry_avg_gross_margin', 0):
                recommendations.append("毛利率低于行业平均，建议优化成本结构")

        except Exception as e:
            self.logger.warning(f"生成行业建议失败: {e}")
            recommendations.append("行业对比分析异常")

        return recommendations

    def _identify_industry_risks(self, company_metrics: Dict[str, float], industry_metrics: Dict[str, float]) -> List[str]:
        """识别行业风险"""
        risks = []

        try:
            # 相对表现风险
            if company_metrics['roe'] < industry_metrics.get('industry_avg_roe', 0) * 0.5:
                risks.append("ROE显著低于行业平均，竞争地位堪忧")

            if company_metrics['gross_margin'] < industry_metrics.get('industry_avg_gross_margin', 0) * 0.8:
                risks.append("毛利率明显低于行业平均，成本控制能力不足")

            if company_metrics['debt_to_equity'] > industry_metrics.get('industry_avg_debt_to_equity', 0) * 1.5:
                risks.append("负债水平显著高于行业平均，财务风险较大")

        except Exception as e:
            self.logger.warning(f"识别行业风险失败: {e}")
            risks.append("行业风险评估异常")

        return risks
