#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
估值模型
提供多种股票估值方法
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple


class ValuationModels:
    """估值模型集合"""
    
    def __init__(self):
        """初始化估值模型"""
        self.logger = logging.getLogger(__name__)
        
    def dcf_valuation(self, financial_data: Dict[str, Any], 
                     assumptions: Dict[str, Any]) -> Dict[str, Any]:
        """
        DCF（现金流折现）估值模型
        
        Args:
            financial_data: 财务数据
            assumptions: 估值假设
            
        Returns:
            DCF估值结果
        """
        try:
            # 提取基础数据
            free_cash_flow = financial_data.get('free_cash_flow', 0)
            revenue = financial_data.get('revenue', 0)
            
            # 估值假设
            growth_rate = assumptions.get('growth_rate', 0.05)
            terminal_growth_rate = assumptions.get('terminal_growth_rate', 0.03)
            discount_rate = assumptions.get('discount_rate', 0.10)
            projection_years = assumptions.get('projection_years', 5)
            
            # 预测未来现金流
            projected_fcf = self._project_free_cash_flows(
                free_cash_flow, growth_rate, projection_years
            )
            
            # 计算终值
            terminal_value = self._calculate_terminal_value(
                projected_fcf[-1], terminal_growth_rate, discount_rate
            )
            
            # 折现计算
            pv_fcf = self._discount_cash_flows(projected_fcf, discount_rate)
            pv_terminal_value = terminal_value / ((1 + discount_rate) ** projection_years)
            
            # 企业价值
            enterprise_value = sum(pv_fcf) + pv_terminal_value
            
            # 股权价值（简化计算）
            net_debt = financial_data.get('net_debt', 0)
            equity_value = enterprise_value - net_debt
            
            # 每股价值
            shares_outstanding = financial_data.get('shares_outstanding', 1)
            value_per_share = equity_value / shares_outstanding if shares_outstanding > 0 else 0
            
            result = {
                'model_type': 'DCF',
                'enterprise_value': enterprise_value,
                'equity_value': equity_value,
                'value_per_share': value_per_share,
                'projected_fcf': projected_fcf,
                'terminal_value': terminal_value,
                'pv_terminal_value': pv_terminal_value,
                'assumptions': assumptions,
                'sensitivity_analysis': self._dcf_sensitivity_analysis(
                    financial_data, assumptions
                )
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"DCF估值失败: {e}")
            return {'error': str(e)}
    
    def relative_valuation(self, financial_data: Dict[str, Any], 
                          peer_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        相对估值模型
        
        Args:
            financial_data: 目标公司财务数据
            peer_data: 同行公司数据
            
        Returns:
            相对估值结果
        """
        try:
            # 计算目标公司指标
            target_metrics = self._calculate_valuation_metrics(financial_data)
            
            # 计算同行平均指标
            peer_metrics = self._calculate_peer_averages(peer_data)
            
            # 相对估值计算
            relative_valuations = {}
            
            # P/E估值
            if target_metrics.get('earnings') and peer_metrics.get('avg_pe'):
                pe_valuation = target_metrics['earnings'] * peer_metrics['avg_pe']
                relative_valuations['pe_valuation'] = pe_valuation
            
            # P/B估值
            if target_metrics.get('book_value') and peer_metrics.get('avg_pb'):
                pb_valuation = target_metrics['book_value'] * peer_metrics['avg_pb']
                relative_valuations['pb_valuation'] = pb_valuation
            
            # P/S估值
            if target_metrics.get('revenue') and peer_metrics.get('avg_ps'):
                ps_valuation = target_metrics['revenue'] * peer_metrics['avg_ps']
                relative_valuations['ps_valuation'] = ps_valuation
            
            # EV/EBITDA估值
            if target_metrics.get('ebitda') and peer_metrics.get('avg_ev_ebitda'):
                ev_ebitda_valuation = target_metrics['ebitda'] * peer_metrics['avg_ev_ebitda']
                relative_valuations['ev_ebitda_valuation'] = ev_ebitda_valuation
            
            # 综合估值
            valuations = list(relative_valuations.values())
            if valuations:
                average_valuation = np.mean(valuations)
                median_valuation = np.median(valuations)
            else:
                average_valuation = median_valuation = 0
            
            result = {
                'model_type': 'Relative',
                'target_metrics': target_metrics,
                'peer_metrics': peer_metrics,
                'relative_valuations': relative_valuations,
                'average_valuation': average_valuation,
                'median_valuation': median_valuation,
                'valuation_range': {
                    'min': min(valuations) if valuations else 0,
                    'max': max(valuations) if valuations else 0
                }
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"相对估值失败: {e}")
            return {'error': str(e)}
    
    def asset_valuation(self, financial_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        资产估值模型
        
        Args:
            financial_data: 财务数据
            
        Returns:
            资产估值结果
        """
        try:
            # 账面价值
            book_value = financial_data.get('total_equity', 0)
            
            # 调整后账面价值
            adjustments = {
                'intangible_assets': financial_data.get('intangible_assets', 0),
                'goodwill': financial_data.get('goodwill', 0),
                'deferred_tax_assets': financial_data.get('deferred_tax_assets', 0)
            }
            
            tangible_book_value = book_value - sum(adjustments.values())
            
            # 重置成本法
            total_assets = financial_data.get('total_assets', 0)
            depreciation = financial_data.get('accumulated_depreciation', 0)
            replacement_cost = total_assets + depreciation * 0.5  # 简化计算
            
            # 清算价值
            current_assets = financial_data.get('current_assets', 0)
            fixed_assets = financial_data.get('fixed_assets', 0)
            total_liabilities = financial_data.get('total_liabilities', 0)
            
            liquidation_value = (
                current_assets * 0.8 +  # 流动资产打8折
                fixed_assets * 0.6 -    # 固定资产打6折
                total_liabilities
            )
            
            result = {
                'model_type': 'Asset',
                'book_value': book_value,
                'tangible_book_value': tangible_book_value,
                'replacement_cost': replacement_cost,
                'liquidation_value': liquidation_value,
                'adjustments': adjustments,
                'asset_quality_score': self._assess_asset_quality(financial_data)
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"资产估值失败: {e}")
            return {'error': str(e)}
    
    def dividend_discount_model(self, financial_data: Dict[str, Any], 
                               assumptions: Dict[str, Any]) -> Dict[str, Any]:
        """
        股利折现模型
        
        Args:
            financial_data: 财务数据
            assumptions: 估值假设
            
        Returns:
            股利折现模型结果
        """
        try:
            # 当前股利
            current_dividend = financial_data.get('dividend_per_share', 0)
            
            if current_dividend <= 0:
                return {'error': '公司不支付股利，无法使用股利折现模型'}
            
            # 估值假设
            dividend_growth_rate = assumptions.get('dividend_growth_rate', 0.05)
            required_return = assumptions.get('required_return', 0.10)
            
            # Gordon增长模型
            if required_return > dividend_growth_rate:
                gordon_value = (current_dividend * (1 + dividend_growth_rate)) / (
                    required_return - dividend_growth_rate
                )
            else:
                gordon_value = 0
            
            # 多阶段增长模型
            high_growth_years = assumptions.get('high_growth_years', 5)
            high_growth_rate = assumptions.get('high_growth_rate', 0.10)
            stable_growth_rate = assumptions.get('stable_growth_rate', 0.03)
            
            multistage_value = self._multistage_dividend_model(
                current_dividend, high_growth_rate, stable_growth_rate,
                required_return, high_growth_years
            )
            
            result = {
                'model_type': 'Dividend_Discount',
                'current_dividend': current_dividend,
                'gordon_growth_value': gordon_value,
                'multistage_value': multistage_value,
                'assumptions': assumptions,
                'dividend_sustainability': self._assess_dividend_sustainability(financial_data)
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"股利折现模型失败: {e}")
            return {'error': str(e)}
    
    def comprehensive_valuation(self, financial_data: Dict[str, Any], 
                              peer_data: List[Dict[str, Any]],
                              assumptions: Dict[str, Any]) -> Dict[str, Any]:
        """
        综合估值
        
        Args:
            financial_data: 财务数据
            peer_data: 同行数据
            assumptions: 估值假设
            
        Returns:
            综合估值结果
        """
        try:
            # 各种估值方法
            dcf_result = self.dcf_valuation(financial_data, assumptions)
            relative_result = self.relative_valuation(financial_data, peer_data)
            asset_result = self.asset_valuation(financial_data)
            
            # 收集估值结果
            valuations = []
            weights = []
            
            if not dcf_result.get('error'):
                valuations.append(dcf_result.get('value_per_share', 0))
                weights.append(0.4)  # DCF权重40%
            
            if not relative_result.get('error'):
                valuations.append(relative_result.get('average_valuation', 0))
                weights.append(0.4)  # 相对估值权重40%
            
            if not asset_result.get('error'):
                valuations.append(asset_result.get('tangible_book_value', 0))
                weights.append(0.2)  # 资产估值权重20%
            
            # 加权平均估值
            if valuations and weights:
                # 标准化权重
                total_weight = sum(weights)
                normalized_weights = [w / total_weight for w in weights]
                
                weighted_valuation = sum(v * w for v, w in zip(valuations, normalized_weights))
                
                # 估值区间
                valuation_range = {
                    'low': min(valuations) * 0.9,
                    'mid': weighted_valuation,
                    'high': max(valuations) * 1.1
                }
            else:
                weighted_valuation = 0
                valuation_range = {'low': 0, 'mid': 0, 'high': 0}
            
            # 当前市价比较
            current_price = financial_data.get('current_price', 0)
            if current_price > 0 and weighted_valuation > 0:
                upside_potential = (weighted_valuation - current_price) / current_price * 100
                valuation_assessment = self._assess_valuation_level(upside_potential)
            else:
                upside_potential = 0
                valuation_assessment = 'unknown'
            
            result = {
                'model_type': 'Comprehensive',
                'individual_valuations': {
                    'dcf': dcf_result,
                    'relative': relative_result,
                    'asset': asset_result
                },
                'weighted_valuation': weighted_valuation,
                'valuation_range': valuation_range,
                'current_price': current_price,
                'upside_potential': upside_potential,
                'valuation_assessment': valuation_assessment,
                'confidence_level': self._calculate_confidence_level(valuations),
                'key_assumptions': assumptions
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"综合估值失败: {e}")
            return {'error': str(e)}
    
    def _project_free_cash_flows(self, base_fcf: float, growth_rate: float, 
                                years: int) -> List[float]:
        """预测自由现金流"""
        projected_fcf = []
        current_fcf = base_fcf
        
        for year in range(1, years + 1):
            current_fcf *= (1 + growth_rate)
            projected_fcf.append(current_fcf)
        
        return projected_fcf
    
    def _calculate_terminal_value(self, final_fcf: float, terminal_growth: float, 
                                discount_rate: float) -> float:
        """计算终值"""
        return (final_fcf * (1 + terminal_growth)) / (discount_rate - terminal_growth)
    
    def _discount_cash_flows(self, cash_flows: List[float], discount_rate: float) -> List[float]:
        """折现现金流"""
        discounted_flows = []
        for i, cf in enumerate(cash_flows, 1):
            pv = cf / ((1 + discount_rate) ** i)
            discounted_flows.append(pv)
        return discounted_flows
    
    def _dcf_sensitivity_analysis(self, financial_data: Dict[str, Any], 
                                 assumptions: Dict[str, Any]) -> Dict[str, Any]:
        """DCF敏感性分析"""
        base_discount_rate = assumptions.get('discount_rate', 0.10)
        base_growth_rate = assumptions.get('growth_rate', 0.05)
        
        # 简化敏感性分析
        sensitivity = {
            'discount_rate_sensitivity': {
                'low': base_discount_rate - 0.01,
                'base': base_discount_rate,
                'high': base_discount_rate + 0.01
            },
            'growth_rate_sensitivity': {
                'low': base_growth_rate - 0.01,
                'base': base_growth_rate,
                'high': base_growth_rate + 0.01
            }
        }
        
        return sensitivity
    
    def _calculate_valuation_metrics(self, financial_data: Dict[str, Any]) -> Dict[str, float]:
        """计算估值指标"""
        return {
            'earnings': financial_data.get('net_income', 0),
            'book_value': financial_data.get('total_equity', 0),
            'revenue': financial_data.get('revenue', 0),
            'ebitda': financial_data.get('ebitda', 0),
            'free_cash_flow': financial_data.get('free_cash_flow', 0)
        }
    
    def _calculate_peer_averages(self, peer_data: List[Dict[str, Any]]) -> Dict[str, float]:
        """计算同行平均指标"""
        if not peer_data:
            return {}
        
        pe_ratios = [p.get('pe_ratio', 0) for p in peer_data if p.get('pe_ratio', 0) > 0]
        pb_ratios = [p.get('pb_ratio', 0) for p in peer_data if p.get('pb_ratio', 0) > 0]
        ps_ratios = [p.get('ps_ratio', 0) for p in peer_data if p.get('ps_ratio', 0) > 0]
        ev_ebitda_ratios = [p.get('ev_ebitda', 0) for p in peer_data if p.get('ev_ebitda', 0) > 0]
        
        return {
            'avg_pe': np.mean(pe_ratios) if pe_ratios else 0,
            'avg_pb': np.mean(pb_ratios) if pb_ratios else 0,
            'avg_ps': np.mean(ps_ratios) if ps_ratios else 0,
            'avg_ev_ebitda': np.mean(ev_ebitda_ratios) if ev_ebitda_ratios else 0
        }
    
    def _assess_asset_quality(self, financial_data: Dict[str, Any]) -> float:
        """评估资产质量"""
        # 简化资产质量评分
        total_assets = financial_data.get('total_assets', 1)
        intangible_assets = financial_data.get('intangible_assets', 0)
        
        tangible_ratio = 1 - (intangible_assets / total_assets)
        return min(100, tangible_ratio * 100)
    
    def _multistage_dividend_model(self, current_dividend: float, high_growth_rate: float,
                                  stable_growth_rate: float, required_return: float,
                                  high_growth_years: int) -> float:
        """多阶段股利增长模型"""
        try:
            # 高增长阶段现值
            high_growth_pv = 0
            dividend = current_dividend
            
            for year in range(1, high_growth_years + 1):
                dividend *= (1 + high_growth_rate)
                pv = dividend / ((1 + required_return) ** year)
                high_growth_pv += pv
            
            # 稳定增长阶段现值
            stable_dividend = dividend * (1 + stable_growth_rate)
            stable_value = stable_dividend / (required_return - stable_growth_rate)
            stable_pv = stable_value / ((1 + required_return) ** high_growth_years)
            
            return high_growth_pv + stable_pv
            
        except:
            return 0
    
    def _assess_dividend_sustainability(self, financial_data: Dict[str, Any]) -> str:
        """评估股利可持续性"""
        dividend_payout_ratio = financial_data.get('dividend_payout_ratio', 0)
        
        if dividend_payout_ratio < 0.3:
            return 'high'
        elif dividend_payout_ratio < 0.6:
            return 'moderate'
        else:
            return 'low'
    
    def _assess_valuation_level(self, upside_potential: float) -> str:
        """评估估值水平"""
        if upside_potential > 20:
            return 'undervalued'
        elif upside_potential > -10:
            return 'fairly_valued'
        else:
            return 'overvalued'
    
    def _calculate_confidence_level(self, valuations: List[float]) -> str:
        """计算置信度"""
        if not valuations or len(valuations) < 2:
            return 'low'
        
        # 计算变异系数
        mean_val = np.mean(valuations)
        std_val = np.std(valuations)
        
        if mean_val > 0:
            cv = std_val / mean_val
            if cv < 0.1:
                return 'high'
            elif cv < 0.2:
                return 'moderate'
            else:
                return 'low'
        
        return 'low'
