#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级Agent编排器
实现智能任务分解、链式推理、自检反馈循环的多Agent协同系统
"""

import asyncio
import logging
import json
import uuid
from typing import Dict, List, Any, Optional, Callable, Union
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import networkx as nx


class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    BLOCKED = "blocked"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """任务优先级"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class TaskNode:
    """任务节点"""
    task_id: str
    name: str
    description: str
    agent_type: str
    function_name: str
    parameters: Dict[str, Any]
    dependencies: List[str] = field(default_factory=list)
    outputs: List[str] = field(default_factory=list)
    status: TaskStatus = TaskStatus.PENDING
    priority: TaskPriority = TaskPriority.MEDIUM
    retry_count: int = 0
    max_retries: int = 3
    result: Optional[Any] = None
    error: Optional[str] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    quality_score: float = 0.0

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'task_id': self.task_id,
            'name': self.name,
            'description': self.description,
            'agent_type': self.agent_type,
            'function_name': self.function_name,
            'parameters': self.parameters,
            'dependencies': self.dependencies,
            'outputs': self.outputs,
            'status': self.status.value,
            'priority': self.priority.value,
            'retry_count': self.retry_count,
            'result': self.result,
            'error': self.error,
            'quality_score': self.quality_score
        }


class TaskDecomposer:
    """智能任务分解器"""

    def __init__(self):
        """初始化任务分解器"""
        self.logger = logging.getLogger(__name__)

        # 预定义的任务模板
        self.task_templates = {
            'company_analysis': {
                'subtasks': [
                    {
                        'name': '数据收集',
                        'agent_type': 'DataCollectorAgent',
                        'function': 'collect_company_data',
                        'outputs': ['company_data']
                    },
                    {
                        'name': '数据质量检查',
                        'agent_type': 'QualityCheckerAgent',
                        'function': 'validate_data_quality',
                        'dependencies': ['数据收集'],
                        'outputs': ['validated_data']
                    },
                    {
                        'name': '财务分析',
                        'agent_type': 'FinancialAnalystAgent',
                        'function': 'analyze_company',
                        'dependencies': ['数据质量检查'],
                        'outputs': ['financial_analysis']
                    },
                    {
                        'name': '风险评估',
                        'agent_type': 'RiskAnalystAgent',
                        'function': 'assess_risks',
                        'dependencies': ['财务分析'],
                        'outputs': ['risk_assessment']
                    },
                    {
                        'name': '投资建议',
                        'agent_type': 'InvestmentAdvisorAgent',
                        'function': 'generate_recommendation',
                        'dependencies': ['财务分析', '风险评估'],
                        'outputs': ['investment_recommendation']
                    },
                    {
                        'name': '报告生成',
                        'agent_type': 'ReportGeneratorAgent',
                        'function': 'generate_report',
                        'dependencies': ['财务分析', '风险评估', '投资建议'],
                        'outputs': ['final_report']
                    },
                    {
                        'name': '质量审查',
                        'agent_type': 'QualityReviewerAgent',
                        'function': 'review_report',
                        'dependencies': ['报告生成'],
                        'outputs': ['reviewed_report']
                    }
                ]
            },
            'industry_analysis': {
                'subtasks': [
                    {
                        'name': '行业数据收集',
                        'agent_type': 'IndustryDataCollectorAgent',
                        'function': 'collect_industry_data',
                        'outputs': ['industry_data']
                    },
                    {
                        'name': '竞争格局分析',
                        'agent_type': 'CompetitionAnalystAgent',
                        'function': 'analyze_competition',
                        'dependencies': ['行业数据收集'],
                        'outputs': ['competition_analysis']
                    },
                    {
                        'name': '趋势预测',
                        'agent_type': 'TrendAnalystAgent',
                        'function': 'predict_trends',
                        'dependencies': ['行业数据收集', '竞争格局分析'],
                        'outputs': ['trend_prediction']
                    },
                    {
                        'name': '行业报告',
                        'agent_type': 'IndustryReportGeneratorAgent',
                        'function': 'generate_industry_report',
                        'dependencies': ['竞争格局分析', '趋势预测'],
                        'outputs': ['industry_report']
                    }
                ]
            },
            'macro_analysis': {
                'subtasks': [
                    {
                        'name': '宏观数据收集',
                        'agent_type': 'MacroDataCollectorAgent',
                        'function': 'collect_macro_data',
                        'outputs': ['macro_data']
                    },
                    {
                        'name': '经济指标分析',
                        'agent_type': 'EconomicAnalystAgent',
                        'function': 'analyze_economic_indicators',
                        'dependencies': ['宏观数据收集'],
                        'outputs': ['economic_analysis']
                    },
                    {
                        'name': '政策影响评估',
                        'agent_type': 'PolicyAnalystAgent',
                        'function': 'assess_policy_impact',
                        'dependencies': ['宏观数据收集'],
                        'outputs': ['policy_impact']
                    },
                    {
                        'name': '宏观报告',
                        'agent_type': 'MacroReportGeneratorAgent',
                        'function': 'generate_macro_report',
                        'dependencies': ['经济指标分析', '政策影响评估'],
                        'outputs': ['macro_report']
                    }
                ]
            }
        }

    def decompose_task(self, main_task: str, parameters: Dict[str, Any]) -> List[TaskNode]:
        """
        智能任务分解

        Args:
            main_task: 主任务类型
            parameters: 任务参数

        Returns:
            分解后的任务节点列表
        """
        try:
            if main_task not in self.task_templates:
                raise ValueError(f"不支持的任务类型: {main_task}")

            template = self.task_templates[main_task]
            task_nodes = []
            task_id_mapping = {}

            # 创建任务节点
            for i, subtask in enumerate(template['subtasks']):
                task_id = f"{main_task}_{i+1}_{uuid.uuid4().hex[:8]}"
                task_id_mapping[subtask['name']] = task_id

                # 确定优先级
                priority = TaskPriority.HIGH if i == 0 else TaskPriority.MEDIUM
                if subtask['name'] in ['质量审查', '报告生成']:
                    priority = TaskPriority.HIGH

                task_node = TaskNode(
                    task_id=task_id,
                    name=subtask['name'],
                    description=f"执行{subtask['name']}任务",
                    agent_type=subtask['agent_type'],
                    function_name=subtask['function'],
                    parameters=self._adapt_parameters(subtask, parameters),
                    dependencies=[],
                    outputs=subtask.get('outputs', []),
                    priority=priority
                )

                task_nodes.append(task_node)

            # 设置依赖关系
            for i, subtask in enumerate(template['subtasks']):
                if 'dependencies' in subtask:
                    task_nodes[i].dependencies = [
                        task_id_mapping[dep] for dep in subtask['dependencies']
                        if dep in task_id_mapping
                    ]

            self.logger.info(f"任务分解完成: {main_task} -> {len(task_nodes)}个子任务")
            return task_nodes

        except Exception as e:
            self.logger.error(f"任务分解失败: {e}")
            return []

    def _adapt_parameters(self, subtask: Dict[str, Any], main_parameters: Dict[str, Any]) -> Dict[str, Any]:
        """适配子任务参数"""
        adapted_params = main_parameters.copy()

        # 根据子任务类型调整参数
        if subtask['name'] == '数据收集':
            adapted_params['include_historical'] = True
            adapted_params['data_sources'] = ['primary', 'secondary']
        elif subtask['name'] == '财务分析':
            adapted_params['analysis_depth'] = 'comprehensive'
            adapted_params['include_ratios'] = True
        elif subtask['name'] == '报告生成':
            adapted_params['format'] = 'professional'
            adapted_params['include_charts'] = True

        return adapted_params

    def optimize_task_sequence(self, task_nodes: List[TaskNode]) -> List[TaskNode]:
        """优化任务执行序列"""
        try:
            # 创建依赖图
            graph = nx.DiGraph()

            # 添加节点
            for task in task_nodes:
                graph.add_node(task.task_id, task=task)

            # 添加边（依赖关系）
            for task in task_nodes:
                for dep_id in task.dependencies:
                    graph.add_edge(dep_id, task.task_id)

            # 检查循环依赖
            if not nx.is_directed_acyclic_graph(graph):
                self.logger.error("检测到循环依赖")
                return task_nodes

            # 拓扑排序
            try:
                sorted_ids = list(nx.topological_sort(graph))

                # 重新排序任务节点
                id_to_task = {task.task_id: task for task in task_nodes}
                optimized_tasks = [id_to_task[task_id] for task_id in sorted_ids]

                self.logger.info("任务序列优化完成")
                return optimized_tasks

            except nx.NetworkXError as e:
                self.logger.error(f"拓扑排序失败: {e}")
                return task_nodes

        except Exception as e:
            self.logger.error(f"任务序列优化失败: {e}")
            return task_nodes


class ChainOfReasoningEngine:
    """链式推理引擎"""

    def __init__(self):
        """初始化链式推理引擎"""
        self.logger = logging.getLogger(__name__)
        self.reasoning_chains = {}
        self.context_memory = {}

    def create_reasoning_chain(self, task_nodes: List[TaskNode]) -> str:
        """
        创建推理链

        Args:
            task_nodes: 任务节点列表

        Returns:
            推理链ID
        """
        chain_id = f"chain_{uuid.uuid4().hex[:8]}"

        reasoning_chain = {
            'chain_id': chain_id,
            'tasks': task_nodes,
            'current_step': 0,
            'reasoning_context': {},
            'intermediate_results': {},
            'confidence_scores': {},
            'created_at': datetime.now()
        }

        self.reasoning_chains[chain_id] = reasoning_chain
        self.logger.info(f"创建推理链: {chain_id}, 包含{len(task_nodes)}个步骤")

        return chain_id

    def execute_reasoning_step(self, chain_id: str, step_result: Any,
                             confidence: float = 1.0) -> Dict[str, Any]:
        """
        执行推理步骤

        Args:
            chain_id: 推理链ID
            step_result: 步骤结果
            confidence: 置信度

        Returns:
            推理上下文更新
        """
        try:
            if chain_id not in self.reasoning_chains:
                raise ValueError(f"推理链不存在: {chain_id}")

            chain = self.reasoning_chains[chain_id]
            current_step = chain['current_step']

            if current_step >= len(chain['tasks']):
                raise ValueError("推理链已完成")

            current_task = chain['tasks'][current_step]

            # 更新推理上下文
            chain['intermediate_results'][current_task.task_id] = step_result
            chain['confidence_scores'][current_task.task_id] = confidence

            # 构建推理上下文
            reasoning_context = self._build_reasoning_context(chain, current_step)
            chain['reasoning_context'] = reasoning_context

            # 推理质量评估
            quality_score = self._assess_reasoning_quality(step_result, confidence, reasoning_context)
            current_task.quality_score = quality_score

            # 移动到下一步
            chain['current_step'] += 1

            self.logger.info(f"推理步骤完成: {current_task.name}, 质量评分: {quality_score:.2f}")

            return {
                'chain_id': chain_id,
                'completed_step': current_task.name,
                'quality_score': quality_score,
                'reasoning_context': reasoning_context,
                'next_step': chain['tasks'][current_step + 1].name if current_step + 1 < len(chain['tasks']) else None
            }

        except Exception as e:
            self.logger.error(f"推理步骤执行失败: {e}")
            return {'error': str(e)}

    def _build_reasoning_context(self, chain: Dict[str, Any], current_step: int) -> Dict[str, Any]:
        """构建推理上下文"""
        context = {
            'previous_results': {},
            'accumulated_insights': [],
            'confidence_trend': [],
            'quality_trend': []
        }

        # 收集前面步骤的结果
        for i in range(current_step + 1):
            task = chain['tasks'][i]
            if task.task_id in chain['intermediate_results']:
                result = chain['intermediate_results'][task.task_id]
                confidence = chain['confidence_scores'].get(task.task_id, 0.0)

                context['previous_results'][task.name] = result
                context['confidence_trend'].append(confidence)
                context['quality_trend'].append(task.quality_score)

                # 提取关键见解
                insights = self._extract_insights(result)
                context['accumulated_insights'].extend(insights)

        return context

    def _extract_insights(self, result: Any) -> List[str]:
        """从结果中提取关键见解"""
        insights = []

        if isinstance(result, dict):
            # 提取关键指标
            if 'financial_ratios' in result:
                ratios = result['financial_ratios']
                if 'roe' in ratios and ratios['roe'] > 15:
                    insights.append(f"ROE表现优秀: {ratios['roe']:.1f}%")

            # 提取投资建议
            if 'investment_recommendation' in result:
                rec = result['investment_recommendation']
                if 'recommendation' in rec:
                    insights.append(f"投资建议: {rec['recommendation']}")

            # 提取风险评估
            if 'risk_level' in result:
                insights.append(f"风险等级: {result['risk_level']}")

        return insights

    def _assess_reasoning_quality(self, result: Any, confidence: float,
                                context: Dict[str, Any]) -> float:
        """评估推理质量"""
        quality_factors = []

        # 置信度因子
        quality_factors.append(confidence * 0.3)

        # 结果完整性因子
        completeness = self._assess_completeness(result)
        quality_factors.append(completeness * 0.3)

        # 一致性因子
        consistency = self._assess_consistency(result, context)
        quality_factors.append(consistency * 0.2)

        # 逻辑性因子
        logic_score = self._assess_logic(result, context)
        quality_factors.append(logic_score * 0.2)

        return sum(quality_factors)

    def _assess_completeness(self, result: Any) -> float:
        """评估结果完整性"""
        if not result:
            return 0.0

        if isinstance(result, dict):
            expected_keys = ['success', 'result', 'metadata']
            present_keys = sum(1 for key in expected_keys if key in result)
            return present_keys / len(expected_keys)

        return 0.8  # 默认评分

    def _assess_consistency(self, result: Any, context: Dict[str, Any]) -> float:
        """评估一致性"""
        if not context.get('previous_results'):
            return 1.0  # 第一步默认一致

        # 检查数值一致性
        consistency_score = 1.0

        if isinstance(result, dict) and 'result' in result:
            current_data = result['result']

            # 检查财务数据一致性
            for prev_name, prev_result in context['previous_results'].items():
                if isinstance(prev_result, dict) and 'result' in prev_result:
                    prev_data = prev_result['result']

                    # 比较共同字段
                    common_fields = set(current_data.keys()) & set(prev_data.keys())
                    if common_fields:
                        for field in common_fields:
                            if isinstance(current_data[field], (int, float)) and isinstance(prev_data[field], (int, float)):
                                if abs(current_data[field] - prev_data[field]) / max(abs(prev_data[field]), 1) > 0.1:
                                    consistency_score *= 0.9

        return max(consistency_score, 0.5)

    def _assess_logic(self, result: Any, context: Dict[str, Any]) -> float:
        """评估逻辑性"""
        logic_score = 0.8  # 基础分

        # 检查逻辑关系
        if isinstance(result, dict) and 'result' in result:
            result_data = result['result']

            # 检查投资建议的逻辑性
            if 'investment_recommendation' in result_data and 'financial_ratios' in result_data:
                recommendation = result_data['investment_recommendation'].get('recommendation', '')
                roe = result_data['financial_ratios'].get('roe', 0)

                # 逻辑检查：高ROE应该对应积极建议
                if roe > 15 and recommendation in ['BUY', 'STRONG_BUY']:
                    logic_score += 0.1
                elif roe < 5 and recommendation in ['SELL', 'STRONG_SELL']:
                    logic_score += 0.1

        return min(logic_score, 1.0)

    def get_reasoning_summary(self, chain_id: str) -> Dict[str, Any]:
        """获取推理摘要"""
        if chain_id not in self.reasoning_chains:
            return {'error': '推理链不存在'}

        chain = self.reasoning_chains[chain_id]

        return {
            'chain_id': chain_id,
            'total_steps': len(chain['tasks']),
            'completed_steps': chain['current_step'],
            'average_confidence': sum(chain['confidence_scores'].values()) / max(len(chain['confidence_scores']), 1),
            'average_quality': sum(task.quality_score for task in chain['tasks']) / len(chain['tasks']),
            'accumulated_insights': chain['reasoning_context'].get('accumulated_insights', []),
            'completion_rate': chain['current_step'] / len(chain['tasks']) * 100
        }


class SelfCheckFeedbackLoop:
    """自检反馈循环系统"""

    def __init__(self, quality_threshold: float = 0.7):
        """
        初始化自检反馈循环

        Args:
            quality_threshold: 质量阈值
        """
        self.logger = logging.getLogger(__name__)
        self.quality_threshold = quality_threshold
        self.feedback_history = {}
        self.improvement_suggestions = {}

    def perform_self_check(self, task_result: Any, task_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行自检

        Args:
            task_result: 任务结果
            task_context: 任务上下文

        Returns:
            自检结果
        """
        try:
            check_id = f"check_{uuid.uuid4().hex[:8]}"

            # 多维度质量检查
            quality_checks = {
                'completeness': self._check_completeness(task_result),
                'accuracy': self._check_accuracy(task_result, task_context),
                'consistency': self._check_consistency(task_result, task_context),
                'relevance': self._check_relevance(task_result, task_context),
                'clarity': self._check_clarity(task_result)
            }

            # 计算总体质量分数
            overall_quality = sum(quality_checks.values()) / len(quality_checks)

            # 判断是否需要改进
            needs_improvement = overall_quality < self.quality_threshold

            # 生成改进建议
            improvement_suggestions = []
            if needs_improvement:
                improvement_suggestions = self._generate_improvement_suggestions(quality_checks, task_result)

            check_result = {
                'check_id': check_id,
                'overall_quality': overall_quality,
                'quality_checks': quality_checks,
                'needs_improvement': needs_improvement,
                'improvement_suggestions': improvement_suggestions,
                'timestamp': datetime.now().isoformat()
            }

            # 记录反馈历史
            self.feedback_history[check_id] = check_result

            self.logger.info(f"自检完成: 质量分数 {overall_quality:.2f}, 需要改进: {needs_improvement}")

            return check_result

        except Exception as e:
            self.logger.error(f"自检失败: {e}")
            return {
                'check_id': 'error',
                'overall_quality': 0.0,
                'error': str(e)
            }

    def _check_completeness(self, result: Any) -> float:
        """检查完整性"""
        if not result:
            return 0.0

        if isinstance(result, dict):
            required_fields = ['success', 'result']
            optional_fields = ['metadata', 'quality_metrics', 'recommendations']

            present_required = sum(1 for field in required_fields if field in result)
            present_optional = sum(1 for field in optional_fields if field in result)

            completeness = (present_required / len(required_fields)) * 0.8 + \
                          (present_optional / len(optional_fields)) * 0.2

            return min(completeness, 1.0)

        return 0.6  # 基础分

    def _check_accuracy(self, result: Any, context: Dict[str, Any]) -> float:
        """检查准确性"""
        accuracy_score = 0.8  # 基础分

        if isinstance(result, dict) and 'result' in result:
            result_data = result['result']

            # 检查数值合理性
            if 'financial_ratios' in result_data:
                ratios = result_data['financial_ratios']

                # ROE合理性检查
                if 'roe' in ratios:
                    roe = ratios['roe']
                    if 0 <= roe <= 50:  # 合理范围
                        accuracy_score += 0.1
                    elif roe < 0 or roe > 100:  # 异常值
                        accuracy_score -= 0.2

                # ROA合理性检查
                if 'roa' in ratios:
                    roa = ratios['roa']
                    if 0 <= roa <= 30:
                        accuracy_score += 0.1
                    elif roa < 0 or roa > 50:
                        accuracy_score -= 0.2

        return max(min(accuracy_score, 1.0), 0.0)

    def _check_consistency(self, result: Any, context: Dict[str, Any]) -> float:
        """检查一致性"""
        if not context.get('previous_results'):
            return 1.0

        consistency_score = 0.8

        # 检查与历史数据的一致性
        if isinstance(result, dict) and 'result' in result:
            current_data = result['result']

            # 检查关键指标的一致性
            for prev_result in context.get('previous_results', {}).values():
                if isinstance(prev_result, dict) and 'result' in prev_result:
                    prev_data = prev_result['result']

                    # 比较财务比率
                    if 'financial_ratios' in current_data and 'financial_ratios' in prev_data:
                        current_ratios = current_data['financial_ratios']
                        prev_ratios = prev_data['financial_ratios']

                        common_ratios = set(current_ratios.keys()) & set(prev_ratios.keys())
                        for ratio in common_ratios:
                            if isinstance(current_ratios[ratio], (int, float)) and isinstance(prev_ratios[ratio], (int, float)):
                                diff_ratio = abs(current_ratios[ratio] - prev_ratios[ratio]) / max(abs(prev_ratios[ratio]), 1)
                                if diff_ratio > 0.5:  # 变化超过50%
                                    consistency_score -= 0.1

        return max(consistency_score, 0.3)

    def _check_relevance(self, result: Any, context: Dict[str, Any]) -> float:
        """检查相关性"""
        relevance_score = 0.8

        # 检查结果是否与任务目标相关
        task_type = context.get('task_type', '')

        if isinstance(result, dict) and 'result' in result:
            result_data = result['result']

            if task_type == 'financial_analysis':
                # 财务分析应包含财务比率
                if 'financial_ratios' in result_data:
                    relevance_score += 0.1
                # 应包含投资建议
                if 'investment_recommendation' in result_data:
                    relevance_score += 0.1

            elif task_type == 'risk_assessment':
                # 风险评估应包含风险等级
                if 'risk_level' in result_data:
                    relevance_score += 0.1
                # 应包含风险因素
                if 'risk_factors' in result_data:
                    relevance_score += 0.1

        return min(relevance_score, 1.0)

    def _check_clarity(self, result: Any) -> float:
        """检查清晰度"""
        clarity_score = 0.7

        if isinstance(result, dict):
            # 检查结构清晰度
            if 'success' in result and isinstance(result['success'], bool):
                clarity_score += 0.1

            if 'result' in result and result['result']:
                clarity_score += 0.1

            # 检查错误信息清晰度
            if not result.get('success', True) and 'error' in result:
                if isinstance(result['error'], str) and len(result['error']) > 10:
                    clarity_score += 0.1

        return min(clarity_score, 1.0)

    def _generate_improvement_suggestions(self, quality_checks: Dict[str, float],
                                        result: Any) -> List[str]:
        """生成改进建议"""
        suggestions = []

        # 基于质量检查结果生成建议
        for check_type, score in quality_checks.items():
            if score < self.quality_threshold:
                if check_type == 'completeness':
                    suggestions.append("增加缺失的必要字段和可选字段")
                elif check_type == 'accuracy':
                    suggestions.append("检查数值计算的准确性，确保在合理范围内")
                elif check_type == 'consistency':
                    suggestions.append("确保结果与历史数据保持一致性")
                elif check_type == 'relevance':
                    suggestions.append("增加与任务目标更相关的内容")
                elif check_type == 'clarity':
                    suggestions.append("改善结果结构和错误信息的清晰度")

        # 基于结果内容生成具体建议
        if isinstance(result, dict) and 'result' in result:
            result_data = result['result']

            if 'financial_ratios' not in result_data:
                suggestions.append("添加关键财务比率分析")

            if 'investment_recommendation' not in result_data:
                suggestions.append("提供明确的投资建议")

            if 'risk_assessment' not in result_data:
                suggestions.append("增加风险评估内容")

        return suggestions

    def apply_feedback(self, task_id: str, feedback: Dict[str, Any]) -> Dict[str, Any]:
        """
        应用反馈进行改进

        Args:
            task_id: 任务ID
            feedback: 反馈信息

        Returns:
            改进结果
        """
        try:
            improvement_id = f"improve_{uuid.uuid4().hex[:8]}"

            # 分析反馈
            suggestions = feedback.get('improvement_suggestions', [])
            quality_issues = feedback.get('quality_checks', {})

            # 生成改进计划
            improvement_plan = {
                'improvement_id': improvement_id,
                'task_id': task_id,
                'target_issues': [issue for issue, score in quality_issues.items() if score < self.quality_threshold],
                'action_items': suggestions,
                'priority': 'high' if feedback.get('overall_quality', 1.0) < 0.5 else 'medium',
                'estimated_effort': len(suggestions) * 0.1,  # 估算改进工作量
                'created_at': datetime.now().isoformat()
            }

            # 记录改进建议
            self.improvement_suggestions[improvement_id] = improvement_plan

            self.logger.info(f"反馈应用完成: {improvement_id}, 改进项目: {len(suggestions)}")

            return improvement_plan

        except Exception as e:
            self.logger.error(f"反馈应用失败: {e}")
            return {'error': str(e)}

    def get_feedback_statistics(self) -> Dict[str, Any]:
        """获取反馈统计"""
        if not self.feedback_history:
            return {'total_checks': 0}

        total_checks = len(self.feedback_history)
        quality_scores = [check['overall_quality'] for check in self.feedback_history.values()]

        improvements_needed = sum(1 for check in self.feedback_history.values() if check['needs_improvement'])

        return {
            'total_checks': total_checks,
            'average_quality': sum(quality_scores) / total_checks,
            'improvements_needed': improvements_needed,
            'improvement_rate': improvements_needed / total_checks * 100,
            'quality_trend': quality_scores[-10:],  # 最近10次的质量趋势
            'common_issues': self._analyze_common_issues()
        }

    def _analyze_common_issues(self) -> Dict[str, int]:
        """分析常见问题"""
        issue_counts = {}

        for check in self.feedback_history.values():
            for suggestion in check.get('improvement_suggestions', []):
                issue_counts[suggestion] = issue_counts.get(suggestion, 0) + 1

        # 返回前5个最常见的问题
        sorted_issues = sorted(issue_counts.items(), key=lambda x: x[1], reverse=True)
        return dict(sorted_issues[:5])


class AdvancedAgentOrchestrator:
    """高级Agent编排器主类"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化高级Agent编排器

        Args:
            config: 配置参数
        """
        self.logger = logging.getLogger(__name__)
        self.config = config or {}

        # 初始化核心组件
        self.task_decomposer = TaskDecomposer()
        self.reasoning_engine = ChainOfReasoningEngine()
        self.feedback_loop = SelfCheckFeedbackLoop(
            quality_threshold=self.config.get('quality_threshold', 0.7)
        )

        # Agent注册表
        self.agent_registry = {}
        self.active_workflows = {}

        # 执行统计
        self.execution_stats = {
            'total_workflows': 0,
            'successful_workflows': 0,
            'total_tasks': 0,
            'successful_tasks': 0,
            'average_quality': 0.0,
            'improvement_cycles': 0
        }

    def register_agent(self, agent_type: str, agent_instance: Any) -> None:
        """
        注册Agent

        Args:
            agent_type: Agent类型
            agent_instance: Agent实例
        """
        self.agent_registry[agent_type] = agent_instance
        self.logger.info(f"Agent已注册: {agent_type}")

    async def execute_workflow(self, workflow_type: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """
        执行工作流

        Args:
            workflow_type: 工作流类型
            parameters: 执行参数

        Returns:
            执行结果
        """
        try:
            workflow_id = f"workflow_{uuid.uuid4().hex[:8]}"
            self.execution_stats['total_workflows'] += 1

            self.logger.info(f"开始执行工作流: {workflow_id} ({workflow_type})")

            # 1. 任务分解
            task_nodes = self.task_decomposer.decompose_task(workflow_type, parameters)
            if not task_nodes:
                raise ValueError("任务分解失败")

            # 2. 优化任务序列
            optimized_tasks = self.task_decomposer.optimize_task_sequence(task_nodes)

            # 3. 创建推理链
            chain_id = self.reasoning_engine.create_reasoning_chain(optimized_tasks)

            # 4. 执行任务链
            workflow_result = await self._execute_task_chain(workflow_id, chain_id, optimized_tasks)

            # 5. 工作流质量评估
            workflow_quality = self._assess_workflow_quality(workflow_result)

            # 6. 更新统计
            if workflow_result.get('success', False):
                self.execution_stats['successful_workflows'] += 1

            self._update_execution_stats(workflow_quality)

            # 7. 构建最终结果
            final_result = {
                'workflow_id': workflow_id,
                'workflow_type': workflow_type,
                'success': workflow_result.get('success', False),
                'result': workflow_result.get('final_result'),
                'quality_score': workflow_quality,
                'execution_summary': {
                    'total_tasks': len(optimized_tasks),
                    'successful_tasks': workflow_result.get('successful_tasks', 0),
                    'reasoning_chain': self.reasoning_engine.get_reasoning_summary(chain_id),
                    'improvement_cycles': workflow_result.get('improvement_cycles', 0)
                },
                'metadata': {
                    'execution_time': workflow_result.get('execution_time', 0),
                    'chain_id': chain_id,
                    'task_count': len(optimized_tasks)
                }
            }

            self.active_workflows[workflow_id] = final_result

            self.logger.info(f"工作流执行完成: {workflow_id}, 质量评分: {workflow_quality:.2f}")

            return final_result

        except Exception as e:
            self.logger.error(f"工作流执行失败: {e}")
            return {
                'workflow_id': workflow_id if 'workflow_id' in locals() else 'unknown',
                'success': False,
                'error': str(e),
                'quality_score': 0.0
            }

    async def _execute_task_chain(self, workflow_id: str, chain_id: str,
                                task_nodes: List[TaskNode]) -> Dict[str, Any]:
        """执行任务链"""
        try:
            start_time = datetime.now()
            successful_tasks = 0
            improvement_cycles = 0
            task_results = {}

            for i, task in enumerate(task_nodes):
                self.logger.info(f"执行任务 {i+1}/{len(task_nodes)}: {task.name}")

                # 检查依赖
                if not await self._check_dependencies(task, task_results):
                    task.status = TaskStatus.BLOCKED
                    self.logger.warning(f"任务依赖未满足: {task.name}")
                    continue

                # 执行任务
                task.status = TaskStatus.RUNNING
                task.start_time = datetime.now()

                try:
                    # 获取Agent
                    agent = self.agent_registry.get(task.agent_type)
                    if not agent:
                        raise ValueError(f"Agent未注册: {task.agent_type}")

                    # 准备参数
                    task_params = self._prepare_task_parameters(task, task_results)

                    # 执行Agent任务
                    if hasattr(agent, task.function_name):
                        task_function = getattr(agent, task.function_name)
                        if asyncio.iscoroutinefunction(task_function):
                            result = await task_function(**task_params)
                        else:
                            result = task_function(**task_params)
                    else:
                        # 使用execute_task方法
                        result = await agent.execute_task(task.function_name, **task_params)

                    task.result = result
                    task.status = TaskStatus.COMPLETED
                    task.end_time = datetime.now()

                    # 自检和反馈循环
                    check_result = self.feedback_loop.perform_self_check(
                        result, {'task_type': task.function_name, 'previous_results': task_results}
                    )

                    # 如果质量不达标，尝试改进
                    if check_result.get('needs_improvement', False) and task.retry_count < task.max_retries:
                        self.logger.info(f"任务质量不达标，尝试改进: {task.name}")

                        # 应用反馈改进
                        improvement_plan = self.feedback_loop.apply_feedback(task.task_id, check_result)

                        # 重新执行任务
                        task.retry_count += 1
                        improvement_cycles += 1

                        # 这里可以根据改进建议调整参数重新执行
                        # 为简化，我们直接标记为需要人工干预
                        self.logger.warning(f"任务需要改进: {task.name}, 建议: {improvement_plan.get('action_items', [])}")

                    # 更新推理链
                    confidence = check_result.get('overall_quality', 0.8)
                    self.reasoning_engine.execute_reasoning_step(chain_id, result, confidence)

                    task_results[task.task_id] = result
                    successful_tasks += 1
                    self.execution_stats['total_tasks'] += 1
                    self.execution_stats['successful_tasks'] += 1

                except Exception as task_error:
                    task.status = TaskStatus.FAILED
                    task.error = str(task_error)
                    task.end_time = datetime.now()

                    self.logger.error(f"任务执行失败: {task.name} - {task_error}")

                    # 如果是关键任务失败，可能需要终止整个工作流
                    if task.priority == TaskPriority.CRITICAL:
                        break

            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()

            # 确定最终结果
            final_result = None
            if task_results:
                # 获取最后一个成功任务的结果作为最终结果
                last_successful_task = None
                for task in reversed(task_nodes):
                    if task.status == TaskStatus.COMPLETED:
                        last_successful_task = task
                        break

                if last_successful_task:
                    final_result = task_results.get(last_successful_task.task_id)

            return {
                'success': successful_tasks > 0,
                'successful_tasks': successful_tasks,
                'final_result': final_result,
                'task_results': task_results,
                'execution_time': execution_time,
                'improvement_cycles': improvement_cycles
            }

        except Exception as e:
            self.logger.error(f"任务链执行失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'successful_tasks': 0,
                'execution_time': 0,
                'improvement_cycles': 0
            }

    async def _check_dependencies(self, task: TaskNode, task_results: Dict[str, Any]) -> bool:
        """检查任务依赖"""
        for dep_id in task.dependencies:
            if dep_id not in task_results:
                return False

            # 检查依赖任务的结果是否成功
            dep_result = task_results[dep_id]
            if isinstance(dep_result, dict) and not dep_result.get('success', True):
                return False

        return True

    def _prepare_task_parameters(self, task: TaskNode, task_results: Dict[str, Any]) -> Dict[str, Any]:
        """准备任务参数"""
        params = task.parameters.copy()

        # 添加依赖任务的结果
        for dep_id in task.dependencies:
            if dep_id in task_results:
                dep_result = task_results[dep_id]
                # 将依赖结果添加到参数中
                if isinstance(dep_result, dict) and 'result' in dep_result:
                    params[f'dependency_{dep_id}'] = dep_result['result']

        return params

    def _assess_workflow_quality(self, workflow_result: Dict[str, Any]) -> float:
        """评估工作流质量"""
        if not workflow_result.get('success', False):
            return 0.0

        quality_factors = []

        # 成功率因子
        total_tasks = workflow_result.get('successful_tasks', 0) + 1  # 避免除零
        success_rate = workflow_result.get('successful_tasks', 0) / total_tasks
        quality_factors.append(success_rate * 0.4)

        # 执行效率因子
        execution_time = workflow_result.get('execution_time', float('inf'))
        efficiency_score = max(0, 1 - (execution_time - 10) / 60)  # 10秒基准，60秒满分
        quality_factors.append(efficiency_score * 0.2)

        # 改进循环因子（越少越好）
        improvement_cycles = workflow_result.get('improvement_cycles', 0)
        improvement_score = max(0, 1 - improvement_cycles * 0.1)
        quality_factors.append(improvement_score * 0.2)

        # 结果质量因子
        if workflow_result.get('final_result'):
            result_quality = 0.8  # 基础分
            quality_factors.append(result_quality * 0.2)
        else:
            quality_factors.append(0.0)

        return sum(quality_factors)

    def _update_execution_stats(self, quality_score: float) -> None:
        """更新执行统计"""
        total_workflows = self.execution_stats['total_workflows']
        current_avg = self.execution_stats['average_quality']
        new_avg = (current_avg * (total_workflows - 1) + quality_score) / total_workflows
        self.execution_stats['average_quality'] = new_avg

    def get_orchestration_report(self) -> Dict[str, Any]:
        """获取编排报告"""
        total_workflows = self.execution_stats['total_workflows']
        success_rate = (self.execution_stats['successful_workflows'] / max(total_workflows, 1)) * 100

        task_success_rate = (self.execution_stats['successful_tasks'] / max(self.execution_stats['total_tasks'], 1)) * 100

        return {
            'execution_statistics': self.execution_stats,
            'workflow_success_rate': success_rate,
            'task_success_rate': task_success_rate,
            'active_workflows': len(self.active_workflows),
            'registered_agents': list(self.agent_registry.keys()),
            'feedback_statistics': self.feedback_loop.get_feedback_statistics(),
            'performance_recommendations': self._get_performance_recommendations()
        }

    def _get_performance_recommendations(self) -> List[str]:
        """获取性能改进建议"""
        recommendations = []

        success_rate = (self.execution_stats['successful_workflows'] / max(self.execution_stats['total_workflows'], 1)) * 100

        if success_rate < 80:
            recommendations.append("工作流成功率较低，建议检查任务依赖和Agent稳定性")

        if self.execution_stats['average_quality'] < 0.7:
            recommendations.append("平均质量评分较低，建议优化质量检查标准")

        feedback_stats = self.feedback_loop.get_feedback_statistics()
        if feedback_stats.get('improvement_rate', 0) > 30:
            recommendations.append("改进率较高，建议优化初始任务执行质量")

        recommendations.extend([
            "定期更新Agent能力和任务模板",
            "建立更细粒度的质量评估标准",
            "实施预测性任务调度",
            "增加异常处理和恢复机制"
        ])

        return recommendations