#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Agent协作框架
实现Agent间智能协调和数据共享（A2A）
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable
from enum import Enum
import json
from pathlib import Path


class MessageType(Enum):
    """消息类型"""
    DATA_REQUEST = "data_request"
    DATA_RESPONSE = "data_response"
    TASK_ASSIGNMENT = "task_assignment"
    TASK_COMPLETION = "task_completion"
    COLLABORATION_REQUEST = "collaboration_request"
    STATUS_UPDATE = "status_update"
    ERROR_NOTIFICATION = "error_notification"


class CollaborationPriority(Enum):
    """协作优先级"""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    URGENT = 4


class AgentMessage:
    """Agent消息类"""
    
    def __init__(self, sender_id: str, receiver_id: str, message_type: MessageType,
                 content: Dict[str, Any], priority: CollaborationPriority = CollaborationPriority.MEDIUM):
        self.message_id = f"{sender_id}_{receiver_id}_{datetime.now().timestamp()}"
        self.sender_id = sender_id
        self.receiver_id = receiver_id
        self.message_type = message_type
        self.content = content
        self.priority = priority
        self.timestamp = datetime.now()
        self.status = "pending"
        self.response = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'message_id': self.message_id,
            'sender_id': self.sender_id,
            'receiver_id': self.receiver_id,
            'message_type': self.message_type.value,
            'content': self.content,
            'priority': self.priority.value,
            'timestamp': self.timestamp.isoformat(),
            'status': self.status
        }


class AgentCollaborationFramework:
    """Agent协作框架"""
    
    def __init__(self):
        """初始化协作框架"""
        self.logger = logging.getLogger(__name__)
        
        # Agent注册表
        self.registered_agents = {}
        
        # 消息队列
        self.message_queue = asyncio.Queue()
        self.message_history = []
        
        # 协作会话
        self.active_sessions = {}
        
        # 数据共享存储
        self.shared_data_store = {}
        
        # 协作规则
        self.collaboration_rules = self._initialize_collaboration_rules()
        
        # 性能监控
        self.collaboration_metrics = {
            'messages_sent': 0,
            'messages_processed': 0,
            'successful_collaborations': 0,
            'failed_collaborations': 0,
            'average_response_time': 0
        }
        
        # 启动消息处理器
        self.message_processor_task = None
    
    def _initialize_collaboration_rules(self) -> Dict[str, Any]:
        """初始化协作规则"""
        return {
            'data_sharing_rules': {
                'financial_data': ['DataCollector', 'FinancialAnalyst'],
                'analysis_results': ['FinancialAnalyst', 'ReportGenerator'],
                'market_data': ['DataCollector', 'FinancialAnalyst', 'ReportGenerator']
            },
            'task_delegation_rules': {
                'data_collection': 'DataCollector',
                'financial_analysis': 'FinancialAnalyst',
                'report_generation': 'ReportGenerator'
            },
            'priority_rules': {
                'urgent_analysis': CollaborationPriority.URGENT,
                'regular_report': CollaborationPriority.MEDIUM,
                'background_update': CollaborationPriority.LOW
            }
        }
    
    async def start_collaboration_system(self):
        """启动协作系统"""
        try:
            self.logger.info("启动Agent协作系统...")
            
            # 启动消息处理器
            self.message_processor_task = asyncio.create_task(self._process_messages())
            
            self.logger.info("Agent协作系统启动完成")
            
        except Exception as e:
            self.logger.error(f"启动协作系统失败: {e}")
    
    async def stop_collaboration_system(self):
        """停止协作系统"""
        try:
            self.logger.info("停止Agent协作系统...")
            
            if self.message_processor_task:
                self.message_processor_task.cancel()
                try:
                    await self.message_processor_task
                except asyncio.CancelledError:
                    pass
            
            self.logger.info("Agent协作系统已停止")
            
        except Exception as e:
            self.logger.error(f"停止协作系统失败: {e}")
    
    def register_agent(self, agent_id: str, agent_instance: Any, capabilities: List[str]):
        """注册Agent"""
        try:
            self.registered_agents[agent_id] = {
                'instance': agent_instance,
                'capabilities': capabilities,
                'status': 'active',
                'last_activity': datetime.now(),
                'message_handlers': {}
            }
            
            self.logger.info(f"Agent {agent_id} 注册成功，能力: {capabilities}")
            
        except Exception as e:
            self.logger.error(f"注册Agent {agent_id} 失败: {e}")
    
    def unregister_agent(self, agent_id: str):
        """注销Agent"""
        try:
            if agent_id in self.registered_agents:
                del self.registered_agents[agent_id]
                self.logger.info(f"Agent {agent_id} 注销成功")
            
        except Exception as e:
            self.logger.error(f"注销Agent {agent_id} 失败: {e}")
    
    async def send_message(self, sender_id: str, receiver_id: str, message_type: MessageType,
                          content: Dict[str, Any], priority: CollaborationPriority = CollaborationPriority.MEDIUM) -> str:
        """发送消息"""
        try:
            # 创建消息
            message = AgentMessage(sender_id, receiver_id, message_type, content, priority)
            
            # 验证接收者
            if receiver_id not in self.registered_agents:
                raise ValueError(f"接收者 {receiver_id} 未注册")
            
            # 添加到消息队列
            await self.message_queue.put(message)
            
            # 更新统计
            self.collaboration_metrics['messages_sent'] += 1
            
            self.logger.info(f"消息已发送: {sender_id} -> {receiver_id}, 类型: {message_type.value}")
            
            return message.message_id
            
        except Exception as e:
            self.logger.error(f"发送消息失败: {e}")
            return ""
    
    async def _process_messages(self):
        """处理消息队列"""
        while True:
            try:
                # 获取消息（按优先级排序）
                message = await self.message_queue.get()
                
                # 处理消息
                await self._handle_message(message)
                
                # 更新统计
                self.collaboration_metrics['messages_processed'] += 1
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"处理消息失败: {e}")
    
    async def _handle_message(self, message: AgentMessage):
        """处理单个消息"""
        try:
            start_time = datetime.now()
            
            # 获取接收者Agent
            receiver_info = self.registered_agents.get(message.receiver_id)
            if not receiver_info:
                self.logger.error(f"接收者 {message.receiver_id} 不存在")
                return
            
            # 根据消息类型处理
            if message.message_type == MessageType.DATA_REQUEST:
                await self._handle_data_request(message)
            elif message.message_type == MessageType.TASK_ASSIGNMENT:
                await self._handle_task_assignment(message)
            elif message.message_type == MessageType.COLLABORATION_REQUEST:
                await self._handle_collaboration_request(message)
            else:
                await self._handle_generic_message(message)
            
            # 记录消息历史
            self.message_history.append(message.to_dict())
            
            # 更新响应时间统计
            response_time = (datetime.now() - start_time).total_seconds()
            self._update_response_time_metrics(response_time)
            
        except Exception as e:
            self.logger.error(f"处理消息失败: {e}")
            message.status = "failed"
    
    async def _handle_data_request(self, message: AgentMessage):
        """处理数据请求"""
        try:
            request_content = message.content
            data_type = request_content.get('data_type')
            parameters = request_content.get('parameters', {})
            
            # 检查数据共享权限
            if not self._check_data_sharing_permission(message.sender_id, message.receiver_id, data_type):
                message.status = "permission_denied"
                return
            
            # 获取接收者Agent实例
            receiver_agent = self.registered_agents[message.receiver_id]['instance']
            
            # 调用Agent的数据提供方法
            if hasattr(receiver_agent, 'provide_data'):
                response_data = await receiver_agent.provide_data(data_type, parameters)
                
                # 发送响应
                await self.send_message(
                    message.receiver_id, 
                    message.sender_id,
                    MessageType.DATA_RESPONSE,
                    {
                        'request_id': message.message_id,
                        'data': response_data,
                        'status': 'success'
                    }
                )
                
                message.status = "completed"
            else:
                message.status = "not_supported"
                
        except Exception as e:
            self.logger.error(f"处理数据请求失败: {e}")
            message.status = "failed"
    
    async def _handle_task_assignment(self, message: AgentMessage):
        """处理任务分配"""
        try:
            task_content = message.content
            task_type = task_content.get('task_type')
            task_parameters = task_content.get('parameters', {})
            
            # 获取接收者Agent实例
            receiver_agent = self.registered_agents[message.receiver_id]['instance']
            
            # 执行任务
            if hasattr(receiver_agent, 'execute_task'):
                task_result = await receiver_agent.execute_task(task_type, **task_parameters)
                
                # 发送完成通知
                await self.send_message(
                    message.receiver_id,
                    message.sender_id,
                    MessageType.TASK_COMPLETION,
                    {
                        'task_id': message.message_id,
                        'result': task_result,
                        'status': 'completed'
                    }
                )
                
                message.status = "completed"
                self.collaboration_metrics['successful_collaborations'] += 1
            else:
                message.status = "not_supported"
                
        except Exception as e:
            self.logger.error(f"处理任务分配失败: {e}")
            message.status = "failed"
            self.collaboration_metrics['failed_collaborations'] += 1
    
    async def _handle_collaboration_request(self, message: AgentMessage):
        """处理协作请求"""
        try:
            collaboration_content = message.content
            collaboration_type = collaboration_content.get('collaboration_type')
            
            # 创建协作会话
            session_id = f"collab_{message.sender_id}_{message.receiver_id}_{datetime.now().timestamp()}"
            
            self.active_sessions[session_id] = {
                'participants': [message.sender_id, message.receiver_id],
                'collaboration_type': collaboration_type,
                'start_time': datetime.now(),
                'status': 'active',
                'shared_context': collaboration_content.get('context', {})
            }
            
            # 通知参与者
            await self.send_message(
                message.receiver_id,
                message.sender_id,
                MessageType.STATUS_UPDATE,
                {
                    'session_id': session_id,
                    'status': 'collaboration_started',
                    'message': f'协作会话 {session_id} 已启动'
                }
            )
            
            message.status = "completed"
            
        except Exception as e:
            self.logger.error(f"处理协作请求失败: {e}")
            message.status = "failed"
    
    async def _handle_generic_message(self, message: AgentMessage):
        """处理通用消息"""
        try:
            # 获取接收者Agent实例
            receiver_agent = self.registered_agents[message.receiver_id]['instance']
            
            # 如果Agent有自定义消息处理器
            if hasattr(receiver_agent, 'handle_message'):
                await receiver_agent.handle_message(message)
            
            message.status = "completed"
            
        except Exception as e:
            self.logger.error(f"处理通用消息失败: {e}")
            message.status = "failed"
    
    def _check_data_sharing_permission(self, sender_id: str, receiver_id: str, data_type: str) -> bool:
        """检查数据共享权限"""
        try:
            sharing_rules = self.collaboration_rules.get('data_sharing_rules', {})
            allowed_agents = sharing_rules.get(data_type, [])
            
            return sender_id in allowed_agents and receiver_id in allowed_agents
            
        except Exception as e:
            self.logger.error(f"检查数据共享权限失败: {e}")
            return False
    
    def _update_response_time_metrics(self, response_time: float):
        """更新响应时间统计"""
        try:
            current_avg = self.collaboration_metrics['average_response_time']
            processed_count = self.collaboration_metrics['messages_processed']
            
            if processed_count == 1:
                self.collaboration_metrics['average_response_time'] = response_time
            else:
                # 计算移动平均
                new_avg = (current_avg * (processed_count - 1) + response_time) / processed_count
                self.collaboration_metrics['average_response_time'] = new_avg
                
        except Exception as e:
            self.logger.error(f"更新响应时间统计失败: {e}")
    
    def get_collaboration_metrics(self) -> Dict[str, Any]:
        """获取协作指标"""
        return {
            'registered_agents': len(self.registered_agents),
            'active_sessions': len(self.active_sessions),
            'message_queue_size': self.message_queue.qsize(),
            'metrics': self.collaboration_metrics.copy(),
            'agent_status': {
                agent_id: info['status'] 
                for agent_id, info in self.registered_agents.items()
            }
        }
    
    async def create_collaborative_workflow(self, workflow_name: str, 
                                          participants: List[str], 
                                          workflow_steps: List[Dict[str, Any]]) -> str:
        """创建协作工作流"""
        try:
            workflow_id = f"workflow_{workflow_name}_{datetime.now().timestamp()}"
            
            # 验证参与者
            for participant in participants:
                if participant not in self.registered_agents:
                    raise ValueError(f"参与者 {participant} 未注册")
            
            # 创建工作流会话
            self.active_sessions[workflow_id] = {
                'type': 'workflow',
                'name': workflow_name,
                'participants': participants,
                'steps': workflow_steps,
                'current_step': 0,
                'start_time': datetime.now(),
                'status': 'active',
                'results': {}
            }
            
            # 启动工作流
            await self._execute_workflow_step(workflow_id, 0)
            
            self.logger.info(f"协作工作流 {workflow_name} 已创建: {workflow_id}")
            return workflow_id
            
        except Exception as e:
            self.logger.error(f"创建协作工作流失败: {e}")
            return ""
    
    async def _execute_workflow_step(self, workflow_id: str, step_index: int):
        """执行工作流步骤"""
        try:
            workflow = self.active_sessions.get(workflow_id)
            if not workflow or step_index >= len(workflow['steps']):
                return
            
            step = workflow['steps'][step_index]
            step_type = step.get('type')
            target_agent = step.get('agent')
            parameters = step.get('parameters', {})
            
            if step_type == 'task':
                # 发送任务分配消息
                await self.send_message(
                    'workflow_manager',
                    target_agent,
                    MessageType.TASK_ASSIGNMENT,
                    {
                        'workflow_id': workflow_id,
                        'step_index': step_index,
                        'task_type': step.get('task_type'),
                        'parameters': parameters
                    },
                    CollaborationPriority.HIGH
                )
            
        except Exception as e:
            self.logger.error(f"执行工作流步骤失败: {e}")
