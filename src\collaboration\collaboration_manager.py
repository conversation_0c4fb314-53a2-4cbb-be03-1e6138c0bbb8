#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
协作管理器
管理Agent间的协作任务和工作流
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from .agent_collaboration_framework import AgentCollaborationFramework, MessageType, CollaborationPriority


class CollaborationManager:
    """协作管理器"""
    
    def __init__(self, collaboration_framework: AgentCollaborationFramework):
        """
        初始化协作管理器
        
        Args:
            collaboration_framework: 协作框架实例
        """
        self.logger = logging.getLogger(__name__)
        self.framework = collaboration_framework
        
        # 预定义工作流模板
        self.workflow_templates = self._initialize_workflow_templates()
        
        # 协作策略
        self.collaboration_strategies = self._initialize_collaboration_strategies()
    
    def _initialize_workflow_templates(self) -> Dict[str, Any]:
        """初始化工作流模板"""
        return {
            'company_analysis_workflow': {
                'name': '公司分析工作流',
                'description': '完整的公司财务分析流程',
                'steps': [
                    {
                        'type': 'task',
                        'agent': 'DataCollector',
                        'task_type': 'collect_company_data',
                        'parameters': {'symbol': '${company_symbol}'},
                        'timeout': 300
                    },
                    {
                        'type': 'task',
                        'agent': 'FinancialAnalyst',
                        'task_type': 'analyze_company',
                        'parameters': {
                            'symbol': '${company_symbol}',
                            'company_data': '${step_0_result}'
                        },
                        'timeout': 600
                    },
                    {
                        'type': 'task',
                        'agent': 'ReportGenerator',
                        'task_type': 'generate_company_report',
                        'parameters': {
                            'symbol': '${company_symbol}',
                            'company_data': '${step_0_result}',
                            'analysis_result': '${step_1_result}'
                        },
                        'timeout': 300
                    }
                ]
            },
            'industry_analysis_workflow': {
                'name': '行业分析工作流',
                'description': '完整的行业分析流程',
                'steps': [
                    {
                        'type': 'task',
                        'agent': 'DataCollector',
                        'task_type': 'collect_industry_data',
                        'parameters': {'industry_name': '${industry_name}'},
                        'timeout': 300
                    },
                    {
                        'type': 'task',
                        'agent': 'FinancialAnalyst',
                        'task_type': 'analyze_industry',
                        'parameters': {
                            'industry_name': '${industry_name}',
                            'industry_data': '${step_0_result}'
                        },
                        'timeout': 600
                    },
                    {
                        'type': 'task',
                        'agent': 'ReportGenerator',
                        'task_type': 'generate_industry_report',
                        'parameters': {
                            'industry_name': '${industry_name}',
                            'industry_data': '${step_0_result}',
                            'analysis_result': '${step_1_result}'
                        },
                        'timeout': 300
                    }
                ]
            },
            'macro_analysis_workflow': {
                'name': '宏观分析工作流',
                'description': '完整的宏观经济分析流程',
                'steps': [
                    {
                        'type': 'task',
                        'agent': 'DataCollector',
                        'task_type': 'collect_macro_data',
                        'parameters': {'indicators': '${indicators}'},
                        'timeout': 300
                    },
                    {
                        'type': 'task',
                        'agent': 'FinancialAnalyst',
                        'task_type': 'analyze_macro',
                        'parameters': {
                            'indicators': '${indicators}',
                            'macro_data': '${step_0_result}'
                        },
                        'timeout': 600
                    },
                    {
                        'type': 'task',
                        'agent': 'ReportGenerator',
                        'task_type': 'generate_macro_report',
                        'parameters': {
                            'indicators': '${indicators}',
                            'macro_data': '${step_0_result}',
                            'analysis_result': '${step_1_result}'
                        },
                        'timeout': 300
                    }
                ]
            }
        }
    
    def _initialize_collaboration_strategies(self) -> Dict[str, Any]:
        """初始化协作策略"""
        return {
            'parallel_processing': {
                'description': '并行处理策略，同时执行多个独立任务',
                'applicable_scenarios': ['多公司分析', '多行业对比']
            },
            'pipeline_processing': {
                'description': '流水线处理策略，按顺序执行依赖任务',
                'applicable_scenarios': ['单一分析流程', '报告生成']
            },
            'adaptive_processing': {
                'description': '自适应处理策略，根据负载动态调整',
                'applicable_scenarios': ['高并发场景', '资源受限环境']
            }
        }
    
    async def execute_company_analysis_collaboration(self, company_symbol: str) -> Dict[str, Any]:
        """执行公司分析协作"""
        try:
            self.logger.info(f"开始公司 {company_symbol} 协作分析...")
            
            # 使用工作流模板
            workflow_template = self.workflow_templates['company_analysis_workflow']
            
            # 替换参数
            workflow_steps = self._substitute_workflow_parameters(
                workflow_template['steps'],
                {'company_symbol': company_symbol}
            )
            
            # 创建协作工作流
            workflow_id = await self.framework.create_collaborative_workflow(
                f"company_analysis_{company_symbol}",
                ['DataCollector', 'FinancialAnalyst', 'ReportGenerator'],
                workflow_steps
            )
            
            # 等待工作流完成
            result = await self._wait_for_workflow_completion(workflow_id)
            
            self.logger.info(f"公司 {company_symbol} 协作分析完成")
            return result
            
        except Exception as e:
            self.logger.error(f"公司分析协作失败: {e}")
            return {'error': str(e)}
    
    async def execute_industry_analysis_collaboration(self, industry_name: str) -> Dict[str, Any]:
        """执行行业分析协作"""
        try:
            self.logger.info(f"开始行业 {industry_name} 协作分析...")
            
            # 使用工作流模板
            workflow_template = self.workflow_templates['industry_analysis_workflow']
            
            # 替换参数
            workflow_steps = self._substitute_workflow_parameters(
                workflow_template['steps'],
                {'industry_name': industry_name}
            )
            
            # 创建协作工作流
            workflow_id = await self.framework.create_collaborative_workflow(
                f"industry_analysis_{industry_name}",
                ['DataCollector', 'FinancialAnalyst', 'ReportGenerator'],
                workflow_steps
            )
            
            # 等待工作流完成
            result = await self._wait_for_workflow_completion(workflow_id)
            
            self.logger.info(f"行业 {industry_name} 协作分析完成")
            return result
            
        except Exception as e:
            self.logger.error(f"行业分析协作失败: {e}")
            return {'error': str(e)}
    
    async def execute_macro_analysis_collaboration(self, indicators: List[str]) -> Dict[str, Any]:
        """执行宏观分析协作"""
        try:
            self.logger.info(f"开始宏观指标 {indicators} 协作分析...")
            
            # 使用工作流模板
            workflow_template = self.workflow_templates['macro_analysis_workflow']
            
            # 替换参数
            workflow_steps = self._substitute_workflow_parameters(
                workflow_template['steps'],
                {'indicators': indicators}
            )
            
            # 创建协作工作流
            workflow_id = await self.framework.create_collaborative_workflow(
                f"macro_analysis_{'_'.join(indicators)}",
                ['DataCollector', 'FinancialAnalyst', 'ReportGenerator'],
                workflow_steps
            )
            
            # 等待工作流完成
            result = await self._wait_for_workflow_completion(workflow_id)
            
            self.logger.info(f"宏观指标 {indicators} 协作分析完成")
            return result
            
        except Exception as e:
            self.logger.error(f"宏观分析协作失败: {e}")
            return {'error': str(e)}
    
    async def execute_parallel_analysis(self, analysis_requests: List[Dict[str, Any]]) -> Dict[str, Any]:
        """执行并行分析"""
        try:
            self.logger.info(f"开始并行分析 {len(analysis_requests)} 个请求...")
            
            # 创建并行任务
            tasks = []
            for request in analysis_requests:
                analysis_type = request.get('type')
                
                if analysis_type == 'company':
                    task = self.execute_company_analysis_collaboration(request.get('symbol'))
                elif analysis_type == 'industry':
                    task = self.execute_industry_analysis_collaboration(request.get('industry_name'))
                elif analysis_type == 'macro':
                    task = self.execute_macro_analysis_collaboration(request.get('indicators'))
                else:
                    continue
                
                tasks.append(task)
            
            # 并行执行
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 整理结果
            parallel_results = {
                'total_requests': len(analysis_requests),
                'successful_results': [],
                'failed_results': [],
                'execution_summary': {}
            }
            
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    parallel_results['failed_results'].append({
                        'request_index': i,
                        'error': str(result)
                    })
                else:
                    parallel_results['successful_results'].append({
                        'request_index': i,
                        'result': result
                    })
            
            # 执行摘要
            parallel_results['execution_summary'] = {
                'success_rate': len(parallel_results['successful_results']) / len(analysis_requests),
                'total_time': 'calculated_in_real_implementation',
                'average_time_per_task': 'calculated_in_real_implementation'
            }
            
            self.logger.info(f"并行分析完成，成功率: {parallel_results['execution_summary']['success_rate']:.2%}")
            return parallel_results
            
        except Exception as e:
            self.logger.error(f"并行分析失败: {e}")
            return {'error': str(e)}
    
    async def coordinate_data_sharing(self, data_provider: str, data_consumer: str, 
                                    data_type: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """协调数据共享"""
        try:
            self.logger.info(f"协调数据共享: {data_provider} -> {data_consumer}, 类型: {data_type}")
            
            # 发送数据请求
            message_id = await self.framework.send_message(
                data_consumer,
                data_provider,
                MessageType.DATA_REQUEST,
                {
                    'data_type': data_type,
                    'parameters': parameters,
                    'requester': data_consumer
                },
                CollaborationPriority.HIGH
            )
            
            # 等待响应
            response = await self._wait_for_message_response(message_id, timeout=60)
            
            if response and response.get('status') == 'success':
                self.logger.info(f"数据共享成功: {data_type}")
                return {
                    'success': True,
                    'data': response.get('data'),
                    'message': '数据共享成功'
                }
            else:
                self.logger.warning(f"数据共享失败: {data_type}")
                return {
                    'success': False,
                    'error': response.get('error', '未知错误'),
                    'message': '数据共享失败'
                }
            
        except Exception as e:
            self.logger.error(f"协调数据共享失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _substitute_workflow_parameters(self, workflow_steps: List[Dict[str, Any]], 
                                      parameters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """替换工作流参数"""
        try:
            import json
            
            # 将工作流步骤转换为JSON字符串进行参数替换
            workflow_json = json.dumps(workflow_steps)
            
            # 替换参数
            for key, value in parameters.items():
                placeholder = f"${{{key}}}"
                workflow_json = workflow_json.replace(placeholder, json.dumps(value))
            
            # 转换回字典
            return json.loads(workflow_json)
            
        except Exception as e:
            self.logger.error(f"替换工作流参数失败: {e}")
            return workflow_steps
    
    async def _wait_for_workflow_completion(self, workflow_id: str, timeout: int = 1800) -> Dict[str, Any]:
        """等待工作流完成"""
        try:
            start_time = datetime.now()
            
            while (datetime.now() - start_time).total_seconds() < timeout:
                # 检查工作流状态
                workflow = self.framework.active_sessions.get(workflow_id)
                
                if not workflow:
                    return {'error': '工作流不存在'}
                
                if workflow.get('status') == 'completed':
                    return workflow.get('results', {})
                elif workflow.get('status') == 'failed':
                    return {'error': '工作流执行失败'}
                
                # 等待一段时间再检查
                await asyncio.sleep(5)
            
            return {'error': '工作流执行超时'}
            
        except Exception as e:
            self.logger.error(f"等待工作流完成失败: {e}")
            return {'error': str(e)}
    
    async def _wait_for_message_response(self, message_id: str, timeout: int = 60) -> Optional[Dict[str, Any]]:
        """等待消息响应"""
        try:
            start_time = datetime.now()
            
            while (datetime.now() - start_time).total_seconds() < timeout:
                # 检查消息历史中的响应
                for message_record in self.framework.message_history:
                    if (message_record.get('content', {}).get('request_id') == message_id and
                        message_record.get('message_type') == MessageType.DATA_RESPONSE.value):
                        return message_record.get('content')
                
                # 等待一段时间再检查
                await asyncio.sleep(1)
            
            return None
            
        except Exception as e:
            self.logger.error(f"等待消息响应失败: {e}")
            return None
    
    def get_collaboration_status(self) -> Dict[str, Any]:
        """获取协作状态"""
        try:
            framework_metrics = self.framework.get_collaboration_metrics()
            
            return {
                'framework_metrics': framework_metrics,
                'available_workflows': list(self.workflow_templates.keys()),
                'collaboration_strategies': list(self.collaboration_strategies.keys()),
                'active_workflows': len([
                    session for session in self.framework.active_sessions.values()
                    if session.get('type') == 'workflow'
                ])
            }
            
        except Exception as e:
            self.logger.error(f"获取协作状态失败: {e}")
            return {'error': str(e)}
