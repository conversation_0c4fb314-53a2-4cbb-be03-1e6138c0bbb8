#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
竞赛配置优化器
针对商汤科技(00020.HK)、智能风控&大数据征信服务行业、生成式AI基建投资趋势，优化系统配置和分析模板
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
from enum import Enum

class ReportType(Enum):
    """报告类型"""
    COMPANY = "company"
    INDUSTRY = "industry"
    MACRO = "macro"

class AnalysisTemplate(Enum):
    """分析模板"""
    SENSETIME_COMPANY = "sensetime_company"
    FINTECH_INDUSTRY = "fintech_industry"
    AI_INFRASTRUCTURE_MACRO = "ai_infrastructure_macro"

@dataclass
class CompetitionTarget:
    """竞赛目标配置"""
    # 公司分析目标
    company_code: str = "00020.HK"
    company_name: str = "商汤科技集团有限公司"
    company_name_en: str = "SenseTime Group Inc."
    
    # 行业分析目标
    industry_name: str = "智能风控&大数据征信服务"
    industry_category: str = "金融科技"
    industry_scope: str = "人工智能在金融领域的应用"
    
    # 宏观分析目标
    macro_theme: str = "生成式AI基建与算力投资趋势"
    macro_timeframe: str = "2023-2026"
    macro_focus: str = "基础设施投资和技术发展"
    
    # 输出要求
    output_format: str = "docx"
    report_language: str = "zh-CN"
    include_charts: bool = True
    chart_embedded: bool = True

@dataclass
class AnalysisConfig:
    """分析配置"""
    template_type: AnalysisTemplate
    data_sources: List[str]
    analysis_depth: str  # basic, standard, comprehensive
    time_horizon: str  # short, medium, long
    risk_assessment: bool
    valuation_analysis: bool
    peer_comparison: bool
    scenario_analysis: bool
    sensitivity_analysis: bool

class CompetitionConfigOptimizer:
    """竞赛配置优化器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化竞赛配置优化器
        
        Args:
            config: 基础配置参数
        """
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # 竞赛目标
        self.competition_target = CompetitionTarget()
        
        # 分析配置模板
        self.analysis_templates = self._init_analysis_templates()
        
        # 数据源配置
        self.data_source_configs = self._init_data_source_configs()
        
        # 报告模板配置
        self.report_templates = self._init_report_templates()

    def _init_analysis_templates(self) -> Dict[AnalysisTemplate, AnalysisConfig]:
        """初始化分析配置模板"""
        return {
            AnalysisTemplate.SENSETIME_COMPANY: AnalysisConfig(
                template_type=AnalysisTemplate.SENSETIME_COMPANY,
                data_sources=[
                    "hkex_com_hk",  # 香港交易所
                    "sensetime_official",  # 商汤科技官方
                    "sina_finance",  # 新浪财经
                    "eastmoney_com",  # 东方财富
                    "afac_calculated"  # AFAC计算数据
                ],
                analysis_depth="comprehensive",
                time_horizon="medium",  # 12-18个月
                risk_assessment=True,
                valuation_analysis=True,
                peer_comparison=True,
                scenario_analysis=True,
                sensitivity_analysis=True
            ),
            
            AnalysisTemplate.FINTECH_INDUSTRY: AnalysisConfig(
                template_type=AnalysisTemplate.FINTECH_INDUSTRY,
                data_sources=[
                    "stats_gov_cn",  # 国家统计局
                    "pbc_gov_cn",  # 央行
                    "sina_finance",  # 新浪财经
                    "eastmoney_com",  # 东方财富
                    "afac_calculated"  # AFAC计算数据
                ],
                analysis_depth="comprehensive",
                time_horizon="long",  # 3-5年
                risk_assessment=True,
                valuation_analysis=False,  # 行业分析不需要估值
                peer_comparison=True,
                scenario_analysis=True,
                sensitivity_analysis=True
            ),
            
            AnalysisTemplate.AI_INFRASTRUCTURE_MACRO: AnalysisConfig(
                template_type=AnalysisTemplate.AI_INFRASTRUCTURE_MACRO,
                data_sources=[
                    "stats_gov_cn",  # 国家统计局
                    "pbc_gov_cn",  # 央行
                    "sina_finance",  # 新浪财经
                    "afac_calculated"  # AFAC计算数据
                ],
                analysis_depth="comprehensive",
                time_horizon="long",  # 2023-2026
                risk_assessment=True,
                valuation_analysis=False,  # 宏观分析不需要估值
                peer_comparison=False,  # 宏观分析不需要同业对比
                scenario_analysis=True,
                sensitivity_analysis=True
            )
        }

    def _init_data_source_configs(self) -> Dict[str, Dict[str, Any]]:
        """初始化数据源配置"""
        return {
            "sensetime_specific": {
                "company_code": "00020.HK",
                "market": "HKEX",
                "currency": "HKD",
                "fiscal_year_end": "12-31",
                "listing_date": "2021-12-30",
                "industry_classification": "AI Software",
                "key_metrics": [
                    "revenue", "net_income", "gross_margin", "rd_expenses",
                    "market_cap", "pe_ratio", "pb_ratio", "roe", "roa"
                ]
            },
            
            "fintech_industry": {
                "industry_code": "AI_FINTECH",
                "market_size_metric": "total_addressable_market",
                "growth_drivers": [
                    "digital_transformation", "regulatory_compliance",
                    "risk_management_demand", "ai_technology_advancement"
                ],
                "key_players": [
                    "商汤科技", "旷视科技", "云从科技", "依图科技",
                    "蚂蚁集团", "腾讯金融", "京东数科"
                ],
                "regulatory_framework": [
                    "数据安全法", "个人信息保护法", "征信业管理条例"
                ]
            },
            
            "ai_infrastructure": {
                "scope": "global_and_china",
                "timeframe": "2023-2026",
                "key_segments": [
                    "computing_power", "cloud_services", "gpu_hardware",
                    "data_centers", "ai_chips", "software_platforms"
                ],
                "investment_categories": [
                    "venture_capital", "government_funding", "corporate_investment",
                    "infrastructure_spending"
                ],
                "geographic_focus": ["china", "usa", "europe", "global"]
            }
        }

    def _init_report_templates(self) -> Dict[ReportType, Dict[str, Any]]:
        """初始化报告模板配置"""
        return {
            ReportType.COMPANY: {
                "filename": "Company_Research_Report.docx",
                "title": "商汤科技(00020.HK)投资研究报告",
                "sections": [
                    {
                        "title": "投资要点",
                        "content_type": "executive_summary",
                        "required_elements": ["投资建议", "目标价", "核心逻辑", "主要风险"]
                    },
                    {
                        "title": "公司概况",
                        "content_type": "company_overview",
                        "required_elements": ["基本信息", "主营业务", "股权结构", "发展历程"]
                    },
                    {
                        "title": "财务分析",
                        "content_type": "financial_analysis",
                        "required_elements": ["财务概况", "盈利能力", "偿债能力", "运营效率", "杜邦分析"],
                        "required_charts": ["营收利润趋势图", "财务比率雷达图", "资产负债结构图"]
                    },
                    {
                        "title": "行业地位与竞争优势",
                        "content_type": "competitive_analysis",
                        "required_elements": ["市场地位", "竞争优势", "护城河分析"],
                        "required_charts": ["行业对比图", "市场份额图"]
                    },
                    {
                        "title": "估值分析",
                        "content_type": "valuation_analysis",
                        "required_elements": ["估值方法", "DCF模型", "相对估值", "敏感性分析"],
                        "required_charts": ["估值对比图", "敏感性分析图"]
                    },
                    {
                        "title": "风险提示",
                        "content_type": "risk_assessment",
                        "required_elements": ["主要风险因素", "风险缓释措施"]
                    }
                ]
            },
            
            ReportType.INDUSTRY: {
                "filename": "Industry_Research_Report.docx",
                "title": "智能风控&大数据征信服务行业研究报告",
                "sections": [
                    {
                        "title": "行业概况",
                        "content_type": "industry_overview",
                        "required_elements": ["行业定义", "发展历程", "产业链分析", "商业模式"]
                    },
                    {
                        "title": "市场规模与增长",
                        "content_type": "market_analysis",
                        "required_elements": ["市场规模", "增长趋势", "驱动因素", "发展前景"],
                        "required_charts": ["市场规模趋势图", "细分市场结构图"]
                    },
                    {
                        "title": "竞争格局",
                        "content_type": "competitive_landscape",
                        "required_elements": ["市场集中度", "主要参与者", "竞争态势", "进入壁垒"],
                        "required_charts": ["竞争格局图", "市场份额分布图"]
                    },
                    {
                        "title": "技术发展趋势",
                        "content_type": "technology_trends",
                        "required_elements": ["核心技术", "技术演进", "创新方向", "技术壁垒"]
                    },
                    {
                        "title": "政策环境",
                        "content_type": "regulatory_environment",
                        "required_elements": ["监管政策", "政策影响", "合规要求", "政策趋势"]
                    },
                    {
                        "title": "投资机会与风险",
                        "content_type": "investment_outlook",
                        "required_elements": ["投资机会", "投资逻辑", "主要风险", "投资建议"]
                    }
                ]
            },
            
            ReportType.MACRO: {
                "filename": "Macro_Research_Report.docx",
                "title": "生成式AI基建与算力投资趋势研究报告(2023-2026)",
                "sections": [
                    {
                        "title": "研究背景与意义",
                        "content_type": "research_background",
                        "required_elements": ["研究背景", "研究意义", "研究方法", "报告结构"]
                    },
                    {
                        "title": "全球AI基础设施投资现状",
                        "content_type": "global_status",
                        "required_elements": ["投资规模", "地区分布", "投资结构", "主要参与者"],
                        "required_charts": ["全球投资规模图", "地区分布图", "投资结构图"]
                    },
                    {
                        "title": "中国AI基础设施发展分析",
                        "content_type": "china_analysis",
                        "required_elements": ["发展现状", "政策支持", "投资规模", "技术水平"],
                        "required_charts": ["中国投资趋势图", "政策时间轴", "技术对比图"]
                    },
                    {
                        "title": "算力投资趋势分析",
                        "content_type": "computing_power_analysis",
                        "required_elements": ["算力需求", "投资趋势", "技术发展", "成本分析"],
                        "required_charts": ["算力投资趋势图", "成本效益分析图"]
                    },
                    {
                        "title": "未来发展趋势预测",
                        "content_type": "future_trends",
                        "required_elements": ["发展趋势", "情景分析", "关键变量", "预测模型"],
                        "required_charts": ["趋势预测图", "情景分析图"]
                    },
                    {
                        "title": "投资建议与风险提示",
                        "content_type": "investment_recommendation",
                        "required_elements": ["投资机会", "投资策略", "风险因素", "政策建议"]
                    }
                ]
            }
        }

    def get_optimized_config(self, report_type: ReportType) -> Dict[str, Any]:
        """
        获取优化的配置
        
        Args:
            report_type: 报告类型
            
        Returns:
            优化的配置字典
        """
        try:
            # 基础配置
            base_config = {
                "competition_target": asdict(self.competition_target),
                "report_type": report_type.value,
                "generation_time": datetime.now().isoformat(),
                "compliance_requirements": self._get_compliance_requirements()
            }
            
            # 根据报告类型添加特定配置
            if report_type == ReportType.COMPANY:
                analysis_config = self.analysis_templates[AnalysisTemplate.SENSETIME_COMPANY]
                data_config = self.data_source_configs["sensetime_specific"]
                template_config = self.report_templates[ReportType.COMPANY]
                
            elif report_type == ReportType.INDUSTRY:
                analysis_config = self.analysis_templates[AnalysisTemplate.FINTECH_INDUSTRY]
                data_config = self.data_source_configs["fintech_industry"]
                template_config = self.report_templates[ReportType.INDUSTRY]
                
            elif report_type == ReportType.MACRO:
                analysis_config = self.analysis_templates[AnalysisTemplate.AI_INFRASTRUCTURE_MACRO]
                data_config = self.data_source_configs["ai_infrastructure"]
                template_config = self.report_templates[ReportType.MACRO]
            
            else:
                raise ValueError(f"不支持的报告类型: {report_type}")
            
            # 合并配置
            optimized_config = {
                **base_config,
                "analysis_config": asdict(analysis_config),
                "data_config": data_config,
                "template_config": template_config,
                "quality_requirements": self._get_quality_requirements(report_type),
                "chart_requirements": self._get_chart_requirements(report_type)
            }
            
            return optimized_config
            
        except Exception as e:
            self.logger.error(f"获取优化配置失败: {e}")
            raise e

    def _get_compliance_requirements(self) -> Dict[str, Any]:
        """获取合规要求"""
        return {
            "regulation_standard": "中国证券业协会《发布证券研究报告暂行规定》",
            "disclosure_requirements": [
                "分析师信息披露",
                "利益冲突声明",
                "风险提示声明",
                "免责声明"
            ],
            "content_requirements": [
                "客观公正原则",
                "充分披露原则",
                "审慎性原则",
                "独立性原则"
            ],
            "format_requirements": [
                "标准化格式",
                "专业术语规范",
                "数据来源标注",
                "图表规范化"
            ]
        }

    def _get_quality_requirements(self, report_type: ReportType) -> Dict[str, Any]:
        """获取质量要求"""
        base_requirements = {
            "data_quality": {
                "min_source_reliability": "high",
                "data_freshness_days": 30,
                "cross_validation_required": True,
                "source_citation_required": True
            },
            "analysis_quality": {
                "logical_consistency": True,
                "quantitative_analysis": True,
                "risk_assessment": True,
                "scenario_analysis": True
            },
            "presentation_quality": {
                "professional_language": True,
                "clear_structure": True,
                "visual_consistency": True,
                "actionable_insights": True
            }
        }
        
        # 根据报告类型调整要求
        if report_type == ReportType.COMPANY:
            base_requirements["analysis_quality"]["valuation_analysis"] = True
            base_requirements["analysis_quality"]["peer_comparison"] = True
            
        elif report_type == ReportType.INDUSTRY:
            base_requirements["analysis_quality"]["market_sizing"] = True
            base_requirements["analysis_quality"]["competitive_analysis"] = True
            
        elif report_type == ReportType.MACRO:
            base_requirements["analysis_quality"]["trend_analysis"] = True
            base_requirements["analysis_quality"]["policy_analysis"] = True
        
        return base_requirements

    def _get_chart_requirements(self, report_type: ReportType) -> Dict[str, Any]:
        """获取图表要求"""
        base_requirements = {
            "chart_quality": {
                "high_resolution": True,
                "professional_style": True,
                "chinese_fonts": True,
                "embedded_in_document": True
            },
            "chart_types": [],
            "data_visualization": {
                "clear_legends": True,
                "proper_scaling": True,
                "color_consistency": True,
                "source_attribution": True
            }
        }
        
        # 根据报告类型设置图表类型
        if report_type == ReportType.COMPANY:
            base_requirements["chart_types"] = [
                "stock_price_trend", "financial_ratios", "revenue_profit_trend",
                "industry_comparison", "valuation_analysis"
            ]
            
        elif report_type == ReportType.INDUSTRY:
            base_requirements["chart_types"] = [
                "market_size_trend", "competitive_landscape", "industry_comparison",
                "technology_trends", "regulatory_timeline"
            ]
            
        elif report_type == ReportType.MACRO:
            base_requirements["chart_types"] = [
                "macro_indicators", "investment_trends", "regional_comparison",
                "scenario_analysis", "trend_forecasting"
            ]
        
        return base_requirements

    def validate_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证配置完整性和正确性
        
        Args:
            config: 配置字典
            
        Returns:
            验证结果
        """
        try:
            validation_result = {
                "is_valid": True,
                "errors": [],
                "warnings": [],
                "suggestions": []
            }
            
            # 检查必需字段
            required_fields = [
                "competition_target", "report_type", "analysis_config",
                "data_config", "template_config"
            ]
            
            for field in required_fields:
                if field not in config:
                    validation_result["errors"].append(f"缺少必需字段: {field}")
                    validation_result["is_valid"] = False
            
            # 检查数据源配置
            if "analysis_config" in config:
                data_sources = config["analysis_config"].get("data_sources", [])
                if not data_sources:
                    validation_result["warnings"].append("未配置数据源")
                elif len(data_sources) < 2:
                    validation_result["warnings"].append("数据源数量较少，建议增加更多数据源以提高可靠性")
            
            # 检查图表配置
            if "chart_requirements" in config:
                chart_types = config["chart_requirements"].get("chart_types", [])
                if not chart_types:
                    validation_result["warnings"].append("未配置图表类型")
            
            # 提供优化建议
            if validation_result["is_valid"]:
                validation_result["suggestions"].extend([
                    "建议定期更新数据源配置以确保数据时效性",
                    "建议根据市场变化调整分析重点",
                    "建议增加更多交叉验证机制"
                ])
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return {
                "is_valid": False,
                "errors": [f"验证过程出错: {str(e)}"],
                "warnings": [],
                "suggestions": []
            }

    def export_config(self, config: Dict[str, Any], output_path: str) -> bool:
        """
        导出配置到文件
        
        Args:
            config: 配置字典
            output_path: 输出路径
            
        Returns:
            是否导出成功
        """
        try:
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"配置已导出到: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出配置失败: {e}")
            return False
