#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据融合验证器
实现多源数据的智能融合和验证，确保数据质量和一致性
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import statistics
from src.data_integration.data_source_tracker import DataSourceTracker, DataReliabilityLevel

class FusionStrategy(Enum):
    """数据融合策略"""
    WEIGHTED_AVERAGE = "weighted_average"  # 加权平均
    RELIABILITY_PRIORITY = "reliability_priority"  # 可靠性优先
    CONSENSUS_BASED = "consensus_based"  # 共识基础
    TEMPORAL_PRIORITY = "temporal_priority"  # 时间优先
    EXPERT_JUDGMENT = "expert_judgment"  # 专家判断

class ValidationLevel(Enum):
    """验证级别"""
    STRICT = "strict"  # 严格验证
    MODERATE = "moderate"  # 中等验证
    LENIENT = "lenient"  # 宽松验证

@dataclass
class FusionResult:
    """融合结果"""
    fused_value: Any
    confidence_score: float
    contributing_sources: List[str]
    fusion_strategy: FusionStrategy
    validation_status: str
    inconsistencies: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    timestamp: datetime

@dataclass
class ValidationRule:
    """验证规则"""
    rule_id: str
    data_type: str
    min_sources: int
    max_deviation: float
    required_reliability: DataReliabilityLevel
    temporal_tolerance: timedelta
    custom_validator: Optional[callable] = None

class DataFusionValidator:
    """数据融合验证器"""
    
    def __init__(self, config: Dict[str, Any], data_tracker: DataSourceTracker):
        """
        初始化数据融合验证器
        
        Args:
            config: 配置参数
            data_tracker: 数据来源追踪器
        """
        self.logger = logging.getLogger(__name__)
        self.config = config
        self.data_tracker = data_tracker
        
        # 可靠性权重映射
        self.reliability_weights = {
            DataReliabilityLevel.VERY_HIGH: 1.0,
            DataReliabilityLevel.HIGH: 0.8,
            DataReliabilityLevel.MEDIUM: 0.6,
            DataReliabilityLevel.LOW: 0.3,
            DataReliabilityLevel.UNKNOWN: 0.1
        }
        
        # 验证规则
        self.validation_rules = self._init_validation_rules()
        
        # 统计信息
        self.fusion_stats = {
            'total_fusions': 0,
            'successful_fusions': 0,
            'failed_validations': 0,
            'strategy_usage': {strategy.value: 0 for strategy in FusionStrategy},
            'average_confidence': 0.0
        }

    def _init_validation_rules(self) -> Dict[str, ValidationRule]:
        """初始化验证规则"""
        return {
            'stock_price': ValidationRule(
                rule_id='stock_price_validation',
                data_type='stock_price',
                min_sources=2,
                max_deviation=0.05,  # 5%最大偏差
                required_reliability=DataReliabilityLevel.HIGH,
                temporal_tolerance=timedelta(minutes=30)
            ),
            'financial_ratio': ValidationRule(
                rule_id='financial_ratio_validation',
                data_type='financial_ratio',
                min_sources=2,
                max_deviation=0.10,  # 10%最大偏差
                required_reliability=DataReliabilityLevel.HIGH,
                temporal_tolerance=timedelta(days=1)
            ),
            'market_cap': ValidationRule(
                rule_id='market_cap_validation',
                data_type='market_cap',
                min_sources=2,
                max_deviation=0.03,  # 3%最大偏差
                required_reliability=DataReliabilityLevel.VERY_HIGH,
                temporal_tolerance=timedelta(hours=1)
            ),
            'macro_indicator': ValidationRule(
                rule_id='macro_indicator_validation',
                data_type='macro_indicator',
                min_sources=1,  # 宏观指标通常来源单一但权威
                max_deviation=0.15,  # 15%最大偏差
                required_reliability=DataReliabilityLevel.VERY_HIGH,
                temporal_tolerance=timedelta(days=7)
            ),
            'industry_data': ValidationRule(
                rule_id='industry_data_validation',
                data_type='industry_data',
                min_sources=2,
                max_deviation=0.20,  # 20%最大偏差
                required_reliability=DataReliabilityLevel.MEDIUM,
                temporal_tolerance=timedelta(days=30)
            )
        }

    def fuse_data(self, data_type: str, data_records: List[Dict[str, Any]], 
                  strategy: FusionStrategy = FusionStrategy.RELIABILITY_PRIORITY,
                  validation_level: ValidationLevel = ValidationLevel.MODERATE) -> FusionResult:
        """
        融合多源数据
        
        Args:
            data_type: 数据类型
            data_records: 数据记录列表
            strategy: 融合策略
            validation_level: 验证级别
            
        Returns:
            融合结果
        """
        try:
            self.fusion_stats['total_fusions'] += 1
            self.fusion_stats['strategy_usage'][strategy.value] += 1
            
            # 数据预处理和验证
            validated_records = self._validate_input_data(data_records, data_type, validation_level)
            
            if not validated_records:
                return self._create_failed_fusion_result(data_type, "No valid data records")
            
            # 根据策略执行融合
            if strategy == FusionStrategy.WEIGHTED_AVERAGE:
                result = self._weighted_average_fusion(validated_records, data_type)
            elif strategy == FusionStrategy.RELIABILITY_PRIORITY:
                result = self._reliability_priority_fusion(validated_records, data_type)
            elif strategy == FusionStrategy.CONSENSUS_BASED:
                result = self._consensus_based_fusion(validated_records, data_type)
            elif strategy == FusionStrategy.TEMPORAL_PRIORITY:
                result = self._temporal_priority_fusion(validated_records, data_type)
            else:
                result = self._reliability_priority_fusion(validated_records, data_type)  # 默认策略
            
            # 后验证
            validation_result = self._post_fusion_validation(result, data_type, validation_level)
            result.validation_status = validation_result['status']
            result.inconsistencies = validation_result['inconsistencies']
            
            if validation_result['status'] == 'passed':
                self.fusion_stats['successful_fusions'] += 1
            else:
                self.fusion_stats['failed_validations'] += 1
            
            # 更新统计信息
            self._update_fusion_statistics(result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"数据融合失败: {e}")
            return self._create_failed_fusion_result(data_type, str(e))

    def _validate_input_data(self, data_records: List[Dict[str, Any]], 
                           data_type: str, validation_level: ValidationLevel) -> List[Dict[str, Any]]:
        """验证输入数据"""
        try:
            validated_records = []
            validation_rule = self.validation_rules.get(data_type)
            
            for record in data_records:
                # 基础字段检查
                if not all(key in record for key in ['value', 'source_id', 'timestamp', 'reliability']):
                    continue
                
                # 数据值有效性检查
                if record['value'] is None or record['value'] == "":
                    continue
                
                # 可靠性检查
                if validation_rule and validation_level == ValidationLevel.STRICT:
                    required_reliability = validation_rule.required_reliability
                    if DataReliabilityLevel(record['reliability']).value < required_reliability.value:
                        continue
                
                # 时间有效性检查
                if validation_rule and validation_level != ValidationLevel.LENIENT:
                    record_time = datetime.fromisoformat(record['timestamp'])
                    time_diff = datetime.now() - record_time
                    if time_diff > validation_rule.temporal_tolerance:
                        continue
                
                validated_records.append(record)
            
            return validated_records
            
        except Exception as e:
            self.logger.error(f"输入数据验证失败: {e}")
            return []

    def _weighted_average_fusion(self, records: List[Dict[str, Any]], data_type: str) -> FusionResult:
        """加权平均融合"""
        try:
            if not records:
                raise ValueError("No records to fuse")
            
            # 提取数值数据
            values = []
            weights = []
            sources = []
            
            for record in records:
                try:
                    value = float(record['value'])
                    reliability = DataReliabilityLevel(record['reliability'])
                    weight = self.reliability_weights[reliability]
                    
                    values.append(value)
                    weights.append(weight)
                    sources.append(record['source_id'])
                except (ValueError, TypeError):
                    continue
            
            if not values:
                raise ValueError("No numeric values found")
            
            # 计算加权平均
            weighted_sum = sum(v * w for v, w in zip(values, weights))
            total_weight = sum(weights)
            fused_value = weighted_sum / total_weight if total_weight > 0 else statistics.mean(values)
            
            # 计算置信度
            confidence = self._calculate_confidence(values, weights, fused_value)
            
            return FusionResult(
                fused_value=fused_value,
                confidence_score=confidence,
                contributing_sources=sources,
                fusion_strategy=FusionStrategy.WEIGHTED_AVERAGE,
                validation_status='pending',
                inconsistencies=[],
                metadata={
                    'original_values': values,
                    'weights': weights,
                    'total_weight': total_weight
                },
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"加权平均融合失败: {e}")
            raise e

    def _reliability_priority_fusion(self, records: List[Dict[str, Any]], data_type: str) -> FusionResult:
        """可靠性优先融合"""
        try:
            if not records:
                raise ValueError("No records to fuse")
            
            # 按可靠性排序
            sorted_records = sorted(records, 
                                  key=lambda x: self.reliability_weights.get(DataReliabilityLevel(x['reliability']), 0),
                                  reverse=True)
            
            # 选择最高可靠性的数据
            best_record = sorted_records[0]
            fused_value = best_record['value']
            
            # 如果有多个相同可靠性的数据，取平均值
            best_reliability = DataReliabilityLevel(best_record['reliability'])
            same_reliability_records = [r for r in sorted_records 
                                      if DataReliabilityLevel(r['reliability']) == best_reliability]
            
            if len(same_reliability_records) > 1:
                try:
                    numeric_values = [float(r['value']) for r in same_reliability_records]
                    fused_value = statistics.mean(numeric_values)
                except (ValueError, TypeError):
                    # 非数值数据，使用第一个值
                    fused_value = best_record['value']
            
            # 计算置信度
            confidence = self.reliability_weights[best_reliability]
            if len(same_reliability_records) > 1:
                confidence *= 1.1  # 多源确认提升置信度
            
            return FusionResult(
                fused_value=fused_value,
                confidence_score=min(1.0, confidence),
                contributing_sources=[r['source_id'] for r in same_reliability_records],
                fusion_strategy=FusionStrategy.RELIABILITY_PRIORITY,
                validation_status='pending',
                inconsistencies=[],
                metadata={
                    'best_reliability': best_reliability.value,
                    'same_reliability_count': len(same_reliability_records)
                },
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"可靠性优先融合失败: {e}")
            raise e

    def _consensus_based_fusion(self, records: List[Dict[str, Any]], data_type: str) -> FusionResult:
        """共识基础融合"""
        try:
            if not records:
                raise ValueError("No records to fuse")
            
            # 尝试数值共识
            try:
                values = [float(r['value']) for r in records]
                
                # 计算中位数作为共识值
                fused_value = statistics.median(values)
                
                # 计算共识度（基于标准差）
                if len(values) > 1:
                    std_dev = statistics.stdev(values)
                    mean_val = statistics.mean(values)
                    consensus_score = 1.0 - min(1.0, std_dev / abs(mean_val) if mean_val != 0 else 1.0)
                else:
                    consensus_score = 1.0
                
            except (ValueError, TypeError):
                # 非数值数据，使用众数
                value_counts = {}
                for record in records:
                    value = str(record['value'])
                    value_counts[value] = value_counts.get(value, 0) + 1
                
                # 找到出现次数最多的值
                most_common_value = max(value_counts, key=value_counts.get)
                fused_value = most_common_value
                
                # 计算共识度
                max_count = value_counts[most_common_value]
                consensus_score = max_count / len(records)
            
            return FusionResult(
                fused_value=fused_value,
                confidence_score=consensus_score,
                contributing_sources=[r['source_id'] for r in records],
                fusion_strategy=FusionStrategy.CONSENSUS_BASED,
                validation_status='pending',
                inconsistencies=[],
                metadata={
                    'consensus_score': consensus_score,
                    'total_sources': len(records)
                },
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"共识基础融合失败: {e}")
            raise e

    def _temporal_priority_fusion(self, records: List[Dict[str, Any]], data_type: str) -> FusionResult:
        """时间优先融合"""
        try:
            if not records:
                raise ValueError("No records to fuse")
            
            # 按时间排序，最新的优先
            sorted_records = sorted(records, 
                                  key=lambda x: datetime.fromisoformat(x['timestamp']),
                                  reverse=True)
            
            latest_record = sorted_records[0]
            fused_value = latest_record['value']
            
            # 计算时间新鲜度分数
            latest_time = datetime.fromisoformat(latest_record['timestamp'])
            time_diff = datetime.now() - latest_time
            freshness_score = max(0.1, 1.0 - (time_diff.total_seconds() / (24 * 3600)))  # 24小时内为满分
            
            return FusionResult(
                fused_value=fused_value,
                confidence_score=freshness_score,
                contributing_sources=[latest_record['source_id']],
                fusion_strategy=FusionStrategy.TEMPORAL_PRIORITY,
                validation_status='pending',
                inconsistencies=[],
                metadata={
                    'latest_timestamp': latest_record['timestamp'],
                    'freshness_score': freshness_score
                },
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"时间优先融合失败: {e}")
            raise e

    def _calculate_confidence(self, values: List[float], weights: List[float], fused_value: float) -> float:
        """计算融合置信度"""
        try:
            if not values or len(values) == 1:
                return 0.8  # 单一数据源的基础置信度
            
            # 基于权重的置信度
            weight_confidence = sum(weights) / len(weights)
            
            # 基于一致性的置信度
            deviations = [abs(v - fused_value) / abs(fused_value) if fused_value != 0 else abs(v) for v in values]
            avg_deviation = statistics.mean(deviations)
            consistency_confidence = max(0.1, 1.0 - avg_deviation)
            
            # 综合置信度
            overall_confidence = (weight_confidence * 0.6 + consistency_confidence * 0.4)
            
            return min(1.0, overall_confidence)
            
        except Exception as e:
            self.logger.warning(f"置信度计算失败: {e}")
            return 0.5

    def _post_fusion_validation(self, result: FusionResult, data_type: str, 
                              validation_level: ValidationLevel) -> Dict[str, Any]:
        """融合后验证"""
        try:
            validation_rule = self.validation_rules.get(data_type)
            inconsistencies = []
            
            # 基础验证
            if result.fused_value is None:
                return {'status': 'failed', 'inconsistencies': [{'type': 'null_value', 'description': '融合结果为空'}]}
            
            # 置信度验证
            min_confidence = 0.7 if validation_level == ValidationLevel.STRICT else 0.5
            if result.confidence_score < min_confidence:
                inconsistencies.append({
                    'type': 'low_confidence',
                    'description': f'置信度{result.confidence_score:.2f}低于阈值{min_confidence}'
                })
            
            # 数据源数量验证
            if validation_rule and len(result.contributing_sources) < validation_rule.min_sources:
                inconsistencies.append({
                    'type': 'insufficient_sources',
                    'description': f'数据源数量{len(result.contributing_sources)}少于要求的{validation_rule.min_sources}'
                })
            
            # 确定验证状态
            if not inconsistencies:
                status = 'passed'
            elif validation_level == ValidationLevel.LENIENT:
                status = 'passed_with_warnings'
            else:
                status = 'failed'
            
            return {
                'status': status,
                'inconsistencies': inconsistencies
            }
            
        except Exception as e:
            self.logger.error(f"融合后验证失败: {e}")
            return {'status': 'error', 'inconsistencies': [{'type': 'validation_error', 'description': str(e)}]}

    def _create_failed_fusion_result(self, data_type: str, error_message: str) -> FusionResult:
        """创建失败的融合结果"""
        return FusionResult(
            fused_value=None,
            confidence_score=0.0,
            contributing_sources=[],
            fusion_strategy=FusionStrategy.RELIABILITY_PRIORITY,
            validation_status='failed',
            inconsistencies=[{'type': 'fusion_error', 'description': error_message}],
            metadata={'error': error_message},
            timestamp=datetime.now()
        )

    def _update_fusion_statistics(self, result: FusionResult):
        """更新融合统计信息"""
        try:
            # 更新平均置信度
            total_confidence = self.fusion_stats['average_confidence'] * (self.fusion_stats['total_fusions'] - 1)
            self.fusion_stats['average_confidence'] = (total_confidence + result.confidence_score) / self.fusion_stats['total_fusions']
            
        except Exception as e:
            self.logger.warning(f"更新统计信息失败: {e}")

    def get_fusion_statistics(self) -> Dict[str, Any]:
        """获取融合统计信息"""
        return self.fusion_stats.copy()
