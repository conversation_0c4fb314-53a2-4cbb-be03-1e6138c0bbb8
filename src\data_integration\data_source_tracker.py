#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据来源追踪器
建立完善的数据来源追踪机制，确保所有数据都有明确的来源引用，实现多源数据的智能融合和验证
"""

import logging
import hashlib
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import sqlite3

class DataSourceType(Enum):
    """数据源类型"""
    GOVERNMENT_OFFICIAL = "government_official"  # 政府官方
    STOCK_EXCHANGE = "stock_exchange"  # 证券交易所
    FINANCIAL_MEDIA = "financial_media"  # 财经媒体
    COMPANY_OFFICIAL = "company_official"  # 公司官方
    RESEARCH_INSTITUTION = "research_institution"  # 研究机构
    THIRD_PARTY_DATA = "third_party_data"  # 第三方数据
    CALCULATED_DERIVED = "calculated_derived"  # 计算衍生

class DataReliabilityLevel(Enum):
    """数据可靠性等级"""
    VERY_HIGH = "very_high"  # 极高（政府、交易所官方）
    HIGH = "high"  # 高（知名媒体、公司公告）
    MEDIUM = "medium"  # 中等（一般媒体、研究报告）
    LOW = "low"  # 低（非官方来源）
    UNKNOWN = "unknown"  # 未知

@dataclass
class DataSource:
    """数据源信息"""
    source_id: str
    name: str
    source_type: DataSourceType
    reliability_level: DataReliabilityLevel
    base_url: str
    description: str
    last_updated: datetime
    access_method: str  # API, Web Scraping, Manual
    rate_limit: Optional[int] = None
    api_key_required: bool = False
    is_free: bool = True
    contact_info: Optional[str] = None

@dataclass
class DataRecord:
    """数据记录"""
    record_id: str
    data_type: str
    data_value: Any
    source_id: str
    collection_time: datetime
    data_timestamp: datetime
    confidence_score: float
    verification_status: str
    metadata: Dict[str, Any]
    hash_value: str

@dataclass
class DataLineage:
    """数据血缘关系"""
    target_record_id: str
    source_record_ids: List[str]
    transformation_type: str
    transformation_description: str
    created_time: datetime

class DataSourceTracker:
    """数据来源追踪器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化数据来源追踪器
        
        Args:
            config: 配置参数
        """
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # 数据库路径
        self.db_path = Path(config.get('db_path', './storage/data_tracking.db'))
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库
        self._init_database()
        
        # 预定义数据源
        self.predefined_sources = self._init_predefined_sources()
        
        # 注册预定义数据源
        self._register_predefined_sources()
        
        # 统计信息
        self.stats = {
            'total_sources': 0,
            'total_records': 0,
            'reliability_distribution': {},
            'verification_stats': {},
            'last_updated': datetime.now()
        }

    def _init_database(self):
        """初始化数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 数据源表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS data_sources (
                        source_id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        source_type TEXT NOT NULL,
                        reliability_level TEXT NOT NULL,
                        base_url TEXT,
                        description TEXT,
                        last_updated TEXT,
                        access_method TEXT,
                        rate_limit INTEGER,
                        api_key_required BOOLEAN,
                        is_free BOOLEAN,
                        contact_info TEXT,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 数据记录表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS data_records (
                        record_id TEXT PRIMARY KEY,
                        data_type TEXT NOT NULL,
                        data_value TEXT NOT NULL,
                        source_id TEXT NOT NULL,
                        collection_time TEXT NOT NULL,
                        data_timestamp TEXT NOT NULL,
                        confidence_score REAL NOT NULL,
                        verification_status TEXT NOT NULL,
                        metadata TEXT,
                        hash_value TEXT NOT NULL,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (source_id) REFERENCES data_sources (source_id)
                    )
                ''')
                
                # 数据血缘表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS data_lineage (
                        lineage_id INTEGER PRIMARY KEY AUTOINCREMENT,
                        target_record_id TEXT NOT NULL,
                        source_record_ids TEXT NOT NULL,
                        transformation_type TEXT NOT NULL,
                        transformation_description TEXT,
                        created_time TEXT NOT NULL,
                        FOREIGN KEY (target_record_id) REFERENCES data_records (record_id)
                    )
                ''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_records_source ON data_records(source_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_records_type ON data_records(data_type)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_records_timestamp ON data_records(data_timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_lineage_target ON data_lineage(target_record_id)')
                
                conn.commit()
                self.logger.info("数据追踪数据库初始化完成")
                
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")

    def _init_predefined_sources(self) -> List[DataSource]:
        """初始化预定义数据源"""
        return [
            # 政府官方数据源
            DataSource(
                source_id="stats_gov_cn",
                name="国家统计局",
                source_type=DataSourceType.GOVERNMENT_OFFICIAL,
                reliability_level=DataReliabilityLevel.VERY_HIGH,
                base_url="http://www.stats.gov.cn",
                description="中华人民共和国国家统计局官方网站，提供宏观经济统计数据",
                last_updated=datetime.now(),
                access_method="Web Scraping",
                is_free=True
            ),
            
            DataSource(
                source_id="pbc_gov_cn",
                name="中国人民银行",
                source_type=DataSourceType.GOVERNMENT_OFFICIAL,
                reliability_level=DataReliabilityLevel.VERY_HIGH,
                base_url="http://www.pbc.gov.cn",
                description="中国人民银行官方网站，提供货币政策和金融统计数据",
                last_updated=datetime.now(),
                access_method="Web Scraping",
                is_free=True
            ),
            
            # 证券交易所数据源
            DataSource(
                source_id="hkex_com_hk",
                name="香港交易所",
                source_type=DataSourceType.STOCK_EXCHANGE,
                reliability_level=DataReliabilityLevel.VERY_HIGH,
                base_url="https://www.hkex.com.hk",
                description="香港交易及结算所有限公司，提供港股交易和公司信息",
                last_updated=datetime.now(),
                access_method="API",
                is_free=True
            ),
            
            DataSource(
                source_id="sse_com_cn",
                name="上海证券交易所",
                source_type=DataSourceType.STOCK_EXCHANGE,
                reliability_level=DataReliabilityLevel.VERY_HIGH,
                base_url="http://www.sse.com.cn",
                description="上海证券交易所官方网站，提供A股交易和公司信息",
                last_updated=datetime.now(),
                access_method="Web Scraping",
                is_free=True
            ),
            
            # 财经媒体数据源
            DataSource(
                source_id="sina_finance",
                name="新浪财经",
                source_type=DataSourceType.FINANCIAL_MEDIA,
                reliability_level=DataReliabilityLevel.HIGH,
                base_url="https://finance.sina.com.cn",
                description="新浪财经频道，提供实时财经新闻和市场数据",
                last_updated=datetime.now(),
                access_method="Web Scraping",
                is_free=True
            ),
            
            DataSource(
                source_id="eastmoney_com",
                name="东方财富",
                source_type=DataSourceType.FINANCIAL_MEDIA,
                reliability_level=DataReliabilityLevel.HIGH,
                base_url="http://www.eastmoney.com",
                description="东方财富网，提供股票行情、财经资讯和数据服务",
                last_updated=datetime.now(),
                access_method="Web Scraping",
                is_free=True
            ),
            
            # 公司官方数据源
            DataSource(
                source_id="sensetime_official",
                name="商汤科技官方",
                source_type=DataSourceType.COMPANY_OFFICIAL,
                reliability_level=DataReliabilityLevel.VERY_HIGH,
                base_url="https://www.sensetime.com",
                description="商汤科技集团有限公司官方网站和公告",
                last_updated=datetime.now(),
                access_method="Web Scraping",
                is_free=True
            ),
            
            # 研究机构数据源
            DataSource(
                source_id="wind_data",
                name="万得资讯",
                source_type=DataSourceType.RESEARCH_INSTITUTION,
                reliability_level=DataReliabilityLevel.HIGH,
                base_url="https://www.wind.com.cn",
                description="万得资讯金融数据服务商（付费数据源，仅作参考）",
                last_updated=datetime.now(),
                access_method="API",
                api_key_required=True,
                is_free=False
            ),
            
            # 计算衍生数据源
            DataSource(
                source_id="afac_calculated",
                name="AFAC计算衍生",
                source_type=DataSourceType.CALCULATED_DERIVED,
                reliability_level=DataReliabilityLevel.MEDIUM,
                base_url="internal://afac",
                description="AFAC系统内部计算和衍生的数据",
                last_updated=datetime.now(),
                access_method="Internal Calculation",
                is_free=True
            )
        ]

    def _register_predefined_sources(self):
        """注册预定义数据源"""
        try:
            for source in self.predefined_sources:
                self.register_data_source(source)
            self.logger.info(f"注册了 {len(self.predefined_sources)} 个预定义数据源")
        except Exception as e:
            self.logger.error(f"注册预定义数据源失败: {e}")

    def register_data_source(self, source: DataSource) -> bool:
        """
        注册数据源
        
        Args:
            source: 数据源信息
            
        Returns:
            是否注册成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO data_sources 
                    (source_id, name, source_type, reliability_level, base_url, description,
                     last_updated, access_method, rate_limit, api_key_required, is_free, contact_info)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    source.source_id,
                    source.name,
                    source.source_type.value,
                    source.reliability_level.value,
                    source.base_url,
                    source.description,
                    source.last_updated.isoformat(),
                    source.access_method,
                    source.rate_limit,
                    source.api_key_required,
                    source.is_free,
                    source.contact_info
                ))
                
                conn.commit()
                self.logger.debug(f"数据源已注册: {source.name}")
                return True
                
        except Exception as e:
            self.logger.error(f"注册数据源失败 {source.source_id}: {e}")
            return False

    def record_data(self, data_type: str, data_value: Any, source_id: str,
                   data_timestamp: Optional[datetime] = None,
                   confidence_score: float = 1.0,
                   metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        记录数据及其来源
        
        Args:
            data_type: 数据类型
            data_value: 数据值
            source_id: 数据源ID
            data_timestamp: 数据时间戳
            confidence_score: 置信度分数
            metadata: 元数据
            
        Returns:
            数据记录ID
        """
        try:
            # 生成记录ID
            record_id = self._generate_record_id(data_type, data_value, source_id)
            
            # 计算数据哈希
            hash_value = self._calculate_data_hash(data_value)
            
            # 默认值处理
            if data_timestamp is None:
                data_timestamp = datetime.now()
            if metadata is None:
                metadata = {}
            
            # 数据验证
            verification_status = self._verify_data(data_value, source_id)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO data_records
                    (record_id, data_type, data_value, source_id, collection_time,
                     data_timestamp, confidence_score, verification_status, metadata, hash_value)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    record_id,
                    data_type,
                    json.dumps(data_value) if not isinstance(data_value, str) else data_value,
                    source_id,
                    datetime.now().isoformat(),
                    data_timestamp.isoformat(),
                    confidence_score,
                    verification_status,
                    json.dumps(metadata),
                    hash_value
                ))
                
                conn.commit()
                self.logger.debug(f"数据已记录: {record_id}")
                return record_id
                
        except Exception as e:
            self.logger.error(f"记录数据失败: {e}")
            return ""

    def create_data_lineage(self, target_record_id: str, source_record_ids: List[str],
                           transformation_type: str, transformation_description: str = "") -> bool:
        """
        创建数据血缘关系
        
        Args:
            target_record_id: 目标数据记录ID
            source_record_ids: 源数据记录ID列表
            transformation_type: 转换类型
            transformation_description: 转换描述
            
        Returns:
            是否创建成功
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO data_lineage
                    (target_record_id, source_record_ids, transformation_type,
                     transformation_description, created_time)
                    VALUES (?, ?, ?, ?, ?)
                ''', (
                    target_record_id,
                    json.dumps(source_record_ids),
                    transformation_type,
                    transformation_description,
                    datetime.now().isoformat()
                ))
                
                conn.commit()
                self.logger.debug(f"数据血缘已创建: {target_record_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"创建数据血缘失败: {e}")
            return False

    def get_data_provenance(self, record_id: str) -> Dict[str, Any]:
        """
        获取数据溯源信息

        Args:
            record_id: 数据记录ID

        Returns:
            数据溯源信息
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 获取数据记录信息
                cursor.execute('''
                    SELECT dr.*, ds.name, ds.source_type, ds.reliability_level, ds.description
                    FROM data_records dr
                    JOIN data_sources ds ON dr.source_id = ds.source_id
                    WHERE dr.record_id = ?
                ''', (record_id,))

                record_row = cursor.fetchone()
                if not record_row:
                    return {'error': 'Record not found'}

                # 获取数据血缘信息
                cursor.execute('''
                    SELECT source_record_ids, transformation_type, transformation_description, created_time
                    FROM data_lineage
                    WHERE target_record_id = ?
                ''', (record_id,))

                lineage_rows = cursor.fetchall()

                # 构建溯源信息
                provenance = {
                    'record_id': record_row[0],
                    'data_type': record_row[1],
                    'data_value': record_row[2],
                    'source_info': {
                        'source_id': record_row[3],
                        'source_name': record_row[11],
                        'source_type': record_row[12],
                        'reliability_level': record_row[13],
                        'description': record_row[14]
                    },
                    'collection_time': record_row[4],
                    'data_timestamp': record_row[5],
                    'confidence_score': record_row[6],
                    'verification_status': record_row[7],
                    'metadata': json.loads(record_row[8]) if record_row[8] else {},
                    'hash_value': record_row[9],
                    'lineage': []
                }

                # 添加血缘信息
                for lineage_row in lineage_rows:
                    source_ids = json.loads(lineage_row[0])
                    lineage_info = {
                        'source_record_ids': source_ids,
                        'transformation_type': lineage_row[1],
                        'transformation_description': lineage_row[2],
                        'created_time': lineage_row[3]
                    }
                    provenance['lineage'].append(lineage_info)

                return provenance

        except Exception as e:
            self.logger.error(f"获取数据溯源失败 {record_id}: {e}")
            return {'error': str(e)}

    def generate_data_citation(self, record_id: str, citation_style: str = 'academic') -> str:
        """
        生成数据引用格式

        Args:
            record_id: 数据记录ID
            citation_style: 引用格式 (academic, news, report)

        Returns:
            格式化的引用文本
        """
        try:
            provenance = self.get_data_provenance(record_id)
            if 'error' in provenance:
                return f"数据引用生成失败: {provenance['error']}"

            source_info = provenance['source_info']
            data_timestamp = datetime.fromisoformat(provenance['data_timestamp'])
            collection_time = datetime.fromisoformat(provenance['collection_time'])

            if citation_style == 'academic':
                citation = f"{source_info['source_name']}. ({data_timestamp.year}). {provenance['data_type']}. " \
                          f"检索时间: {collection_time.strftime('%Y年%m月%d日')}, " \
                          f"来源: {source_info['description']}. " \
                          f"可靠性等级: {source_info['reliability_level']}"

            elif citation_style == 'news':
                citation = f"据{source_info['source_name']}数据显示，{provenance['data_type']}为{provenance['data_value']}。" \
                          f"（数据时间：{data_timestamp.strftime('%Y年%m月%d日')}）"

            elif citation_style == 'report':
                citation = f"【数据来源】{source_info['source_name']} | " \
                          f"【数据类型】{provenance['data_type']} | " \
                          f"【数据时间】{data_timestamp.strftime('%Y-%m-%d')} | " \
                          f"【可靠性】{source_info['reliability_level']} | " \
                          f"【置信度】{provenance['confidence_score']:.2f}"

            else:
                citation = f"数据来源：{source_info['source_name']}，时间：{data_timestamp.strftime('%Y-%m-%d')}"

            return citation

        except Exception as e:
            self.logger.error(f"生成数据引用失败 {record_id}: {e}")
            return f"引用生成失败: {str(e)}"

    def _generate_record_id(self, data_type: str, data_value: Any, source_id: str) -> str:
        """生成数据记录ID"""
        content = f"{data_type}_{data_value}_{source_id}_{datetime.now().isoformat()}"
        return hashlib.md5(content.encode()).hexdigest()

    def _calculate_data_hash(self, data_value: Any) -> str:
        """计算数据哈希值"""
        if isinstance(data_value, (dict, list)):
            content = json.dumps(data_value, sort_keys=True)
        else:
            content = str(data_value)
        return hashlib.sha256(content.encode()).hexdigest()

    def _verify_data(self, data_value: Any, source_id: str) -> str:
        """验证数据"""
        try:
            # 基础验证
            if data_value is None or data_value == "":
                return "invalid_empty"

            # 根据数据源可靠性判断
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT reliability_level FROM data_sources WHERE source_id = ?', (source_id,))
                result = cursor.fetchone()

                if result:
                    reliability = result[0]
                    if reliability in ['very_high', 'high']:
                        return "verified"
                    elif reliability == 'medium':
                        return "partially_verified"
                    else:
                        return "unverified"
                else:
                    return "unknown_source"

        except Exception as e:
            self.logger.warning(f"数据验证失败: {e}")
            return "verification_failed"

    def get_source_statistics(self) -> Dict[str, Any]:
        """获取数据源统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 数据源总数
                cursor.execute('SELECT COUNT(*) FROM data_sources')
                total_sources = cursor.fetchone()[0]

                # 数据记录总数
                cursor.execute('SELECT COUNT(*) FROM data_records')
                total_records = cursor.fetchone()[0]

                # 可靠性分布
                cursor.execute('''
                    SELECT ds.reliability_level, COUNT(dr.record_id)
                    FROM data_sources ds
                    LEFT JOIN data_records dr ON ds.source_id = dr.source_id
                    GROUP BY ds.reliability_level
                ''')
                reliability_dist = dict(cursor.fetchall())

                # 验证状态分布
                cursor.execute('''
                    SELECT verification_status, COUNT(*)
                    FROM data_records
                    GROUP BY verification_status
                ''')
                verification_stats = dict(cursor.fetchall())

                return {
                    'total_sources': total_sources,
                    'total_records': total_records,
                    'reliability_distribution': reliability_dist,
                    'verification_stats': verification_stats,
                    'last_updated': datetime.now().isoformat()
                }

        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {'error': str(e)}
