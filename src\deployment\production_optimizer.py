#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产级部署优化器
提升系统生成效率、简化部署复杂度，增强实际业务场景适用性
"""

import asyncio
import logging
import json
import time
import psutil
import threading
from typing import Dict, List, Any, Optional, Union, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import queue
import pickle


class DeploymentMode(Enum):
    """部署模式"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class ScalingStrategy(Enum):
    """扩展策略"""
    VERTICAL = "vertical"      # 垂直扩展
    HORIZONTAL = "horizontal"  # 水平扩展
    AUTO = "auto"             # 自动扩展


@dataclass
class PerformanceMetrics:
    """性能指标"""
    throughput: float          # 吞吐量 (requests/second)
    latency_p50: float        # 50%延迟 (ms)
    latency_p95: float        # 95%延迟 (ms)
    latency_p99: float        # 99%延迟 (ms)
    cpu_usage: float          # CPU使用率 (%)
    memory_usage: float       # 内存使用率 (%)
    error_rate: float         # 错误率 (%)
    availability: float       # 可用性 (%)
    
    def to_dict(self) -> Dict[str, float]:
        """转换为字典"""
        return {
            'throughput': self.throughput,
            'latency_p50': self.latency_p50,
            'latency_p95': self.latency_p95,
            'latency_p99': self.latency_p99,
            'cpu_usage': self.cpu_usage,
            'memory_usage': self.memory_usage,
            'error_rate': self.error_rate,
            'availability': self.availability
        }


class ResourceManager:
    """资源管理器"""
    
    def __init__(self, max_workers: Optional[int] = None):
        """
        初始化资源管理器
        
        Args:
            max_workers: 最大工作线程数
        """
        self.logger = logging.getLogger(__name__)
        self.max_workers = max_workers or min(32, (mp.cpu_count() or 1) + 4)
        
        # 线程池和进程池
        self.thread_pool = ThreadPoolExecutor(max_workers=self.max_workers)
        self.process_pool = ProcessPoolExecutor(max_workers=min(self.max_workers, mp.cpu_count()))
        
        # 资源监控
        self.resource_stats = {
            'cpu_usage_history': [],
            'memory_usage_history': [],
            'active_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0
        }
        
        # 任务队列
        self.task_queue = queue.PriorityQueue()
        self.result_cache = {}
        
        # 监控线程
        self.monitoring = False
        self.monitor_thread = None
    
    def start_monitoring(self) -> None:
        """启动资源监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_resources, daemon=True)
        self.monitor_thread.start()
        self.logger.info("资源监控已启动")
    
    def stop_monitoring(self) -> None:
        """停止资源监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
        self.logger.info("资源监控已停止")
    
    def _monitor_resources(self) -> None:
        """监控资源使用情况"""
        while self.monitoring:
            try:
                # 获取系统资源使用情况
                cpu_percent = psutil.cpu_percent(interval=1)
                memory_percent = psutil.virtual_memory().percent
                
                # 记录历史数据
                self.resource_stats['cpu_usage_history'].append(cpu_percent)
                self.resource_stats['memory_usage_history'].append(memory_percent)
                
                # 保持历史数据在合理范围内
                if len(self.resource_stats['cpu_usage_history']) > 100:
                    self.resource_stats['cpu_usage_history'].pop(0)
                if len(self.resource_stats['memory_usage_history']) > 100:
                    self.resource_stats['memory_usage_history'].pop(0)
                
                # 资源告警
                if cpu_percent > 90:
                    self.logger.warning(f"CPU使用率过高: {cpu_percent:.1f}%")
                if memory_percent > 90:
                    self.logger.warning(f"内存使用率过高: {memory_percent:.1f}%")
                
                time.sleep(5)  # 每5秒监控一次
                
            except Exception as e:
                self.logger.error(f"资源监控错误: {e}")
                time.sleep(5)
    
    async def execute_task_async(self, task_func: Callable, *args, **kwargs) -> Any:
        """异步执行任务"""
        self.resource_stats['active_tasks'] += 1
        
        try:
            # 根据任务类型选择执行方式
            if self._is_cpu_intensive(task_func):
                # CPU密集型任务使用进程池
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(self.process_pool, task_func, *args, **kwargs)
            else:
                # IO密集型任务使用线程池
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(self.thread_pool, task_func, *args, **kwargs)
            
            self.resource_stats['completed_tasks'] += 1
            return result
            
        except Exception as e:
            self.resource_stats['failed_tasks'] += 1
            self.logger.error(f"任务执行失败: {e}")
            raise e
        finally:
            self.resource_stats['active_tasks'] -= 1
    
    def _is_cpu_intensive(self, task_func: Callable) -> bool:
        """判断是否为CPU密集型任务"""
        # 简化判断逻辑，实际可以根据函数名或装饰器判断
        func_name = task_func.__name__.lower()
        cpu_intensive_keywords = ['calculate', 'compute', 'analyze', 'process', 'transform']
        return any(keyword in func_name for keyword in cpu_intensive_keywords)
    
    def get_resource_status(self) -> Dict[str, Any]:
        """获取资源状态"""
        cpu_history = self.resource_stats['cpu_usage_history']
        memory_history = self.resource_stats['memory_usage_history']
        
        return {
            'current_cpu': psutil.cpu_percent(),
            'current_memory': psutil.virtual_memory().percent,
            'avg_cpu': sum(cpu_history) / len(cpu_history) if cpu_history else 0,
            'avg_memory': sum(memory_history) / len(memory_history) if memory_history else 0,
            'active_tasks': self.resource_stats['active_tasks'],
            'completed_tasks': self.resource_stats['completed_tasks'],
            'failed_tasks': self.resource_stats['failed_tasks'],
            'thread_pool_size': self.max_workers,
            'process_pool_size': self.process_pool._max_workers
        }
    
    def cleanup(self) -> None:
        """清理资源"""
        self.stop_monitoring()
        self.thread_pool.shutdown(wait=True)
        self.process_pool.shutdown(wait=True)
        self.logger.info("资源管理器已清理")


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, max_size: int = 1000, ttl: int = 3600):
        """
        初始化缓存管理器
        
        Args:
            max_size: 最大缓存条目数
            ttl: 生存时间(秒)
        """
        self.logger = logging.getLogger(__name__)
        self.max_size = max_size
        self.ttl = ttl
        self.cache = {}
        self.access_times = {}
        self.lock = threading.RLock()
        
        # 缓存统计
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'total_requests': 0
        }
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            self.stats['total_requests'] += 1
            
            if key in self.cache:
                value, timestamp = self.cache[key]
                
                # 检查是否过期
                if time.time() - timestamp > self.ttl:
                    del self.cache[key]
                    if key in self.access_times:
                        del self.access_times[key]
                    self.stats['misses'] += 1
                    return None
                
                # 更新访问时间
                self.access_times[key] = time.time()
                self.stats['hits'] += 1
                return value
            
            self.stats['misses'] += 1
            return None
    
    def set(self, key: str, value: Any) -> None:
        """设置缓存值"""
        with self.lock:
            # 检查缓存大小
            if len(self.cache) >= self.max_size:
                self._evict_lru()
            
            # 存储值和时间戳
            self.cache[key] = (value, time.time())
            self.access_times[key] = time.time()
    
    def _evict_lru(self) -> None:
        """淘汰最少使用的条目"""
        if not self.access_times:
            return
        
        # 找到最少使用的键
        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        
        # 删除条目
        if lru_key in self.cache:
            del self.cache[lru_key]
        if lru_key in self.access_times:
            del self.access_times[lru_key]
        
        self.stats['evictions'] += 1
    
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self.lock:
            hit_rate = (self.stats['hits'] / max(self.stats['total_requests'], 1)) * 100
            
            return {
                **self.stats,
                'hit_rate': hit_rate,
                'cache_size': len(self.cache),
                'max_size': self.max_size
            }


class LoadBalancer:
    """负载均衡器"""
    
    def __init__(self, strategy: str = 'round_robin'):
        """
        初始化负载均衡器
        
        Args:
            strategy: 负载均衡策略 ('round_robin', 'least_connections', 'weighted')
        """
        self.logger = logging.getLogger(__name__)
        self.strategy = strategy
        self.workers = []
        self.worker_stats = {}
        self.current_index = 0
        self.lock = threading.Lock()
    
    def add_worker(self, worker_id: str, weight: float = 1.0) -> None:
        """添加工作节点"""
        with self.lock:
            self.workers.append({
                'id': worker_id,
                'weight': weight,
                'active_connections': 0,
                'total_requests': 0,
                'failed_requests': 0,
                'last_used': time.time()
            })
            self.worker_stats[worker_id] = {
                'response_times': [],
                'success_rate': 100.0
            }
        self.logger.info(f"添加工作节点: {worker_id}")
    
    def get_worker(self) -> Optional[str]:
        """获取工作节点"""
        with self.lock:
            if not self.workers:
                return None
            
            if self.strategy == 'round_robin':
                return self._round_robin()
            elif self.strategy == 'least_connections':
                return self._least_connections()
            elif self.strategy == 'weighted':
                return self._weighted_round_robin()
            else:
                return self._round_robin()
    
    def _round_robin(self) -> str:
        """轮询策略"""
        worker = self.workers[self.current_index]
        self.current_index = (self.current_index + 1) % len(self.workers)
        return worker['id']
    
    def _least_connections(self) -> str:
        """最少连接策略"""
        min_connections = min(worker['active_connections'] for worker in self.workers)
        for worker in self.workers:
            if worker['active_connections'] == min_connections:
                return worker['id']
        return self.workers[0]['id']
    
    def _weighted_round_robin(self) -> str:
        """加权轮询策略"""
        # 简化实现，基于权重选择
        total_weight = sum(worker['weight'] for worker in self.workers)
        if total_weight == 0:
            return self._round_robin()
        
        # 根据权重和成功率选择
        best_worker = None
        best_score = -1
        
        for worker in self.workers:
            success_rate = self.worker_stats[worker['id']]['success_rate']
            score = worker['weight'] * (success_rate / 100)
            if score > best_score:
                best_score = score
                best_worker = worker
        
        return best_worker['id'] if best_worker else self.workers[0]['id']
    
    def report_request_start(self, worker_id: str) -> None:
        """报告请求开始"""
        with self.lock:
            for worker in self.workers:
                if worker['id'] == worker_id:
                    worker['active_connections'] += 1
                    worker['total_requests'] += 1
                    worker['last_used'] = time.time()
                    break
    
    def report_request_end(self, worker_id: str, success: bool, response_time: float) -> None:
        """报告请求结束"""
        with self.lock:
            for worker in self.workers:
                if worker['id'] == worker_id:
                    worker['active_connections'] -= 1
                    if not success:
                        worker['failed_requests'] += 1
                    break
            
            # 更新统计信息
            if worker_id in self.worker_stats:
                stats = self.worker_stats[worker_id]
                stats['response_times'].append(response_time)
                
                # 保持响应时间历史在合理范围内
                if len(stats['response_times']) > 100:
                    stats['response_times'].pop(0)
                
                # 更新成功率
                worker = next((w for w in self.workers if w['id'] == worker_id), None)
                if worker:
                    success_rate = ((worker['total_requests'] - worker['failed_requests']) / 
                                  max(worker['total_requests'], 1)) * 100
                    stats['success_rate'] = success_rate
    
    def get_worker_stats(self) -> Dict[str, Any]:
        """获取工作节点统计"""
        with self.lock:
            stats = {}
            for worker in self.workers:
                worker_id = worker['id']
                worker_stats = self.worker_stats.get(worker_id, {})
                
                stats[worker_id] = {
                    'active_connections': worker['active_connections'],
                    'total_requests': worker['total_requests'],
                    'failed_requests': worker['failed_requests'],
                    'success_rate': worker_stats.get('success_rate', 100.0),
                    'avg_response_time': (sum(worker_stats.get('response_times', [])) / 
                                        len(worker_stats.get('response_times', []))) if worker_stats.get('response_times') else 0,
                    'weight': worker['weight']
                }
            
            return stats


class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        """初始化性能分析器"""
        self.logger = logging.getLogger(__name__)
        self.profiles = {}
        self.benchmarks = {}
    
    def profile_function(self, func_name: str):
        """函数性能分析装饰器"""
        def decorator(func):
            async def async_wrapper(*args, **kwargs):
                start_time = time.time()
                start_memory = psutil.Process().memory_info().rss
                
                try:
                    if asyncio.iscoroutinefunction(func):
                        result = await func(*args, **kwargs)
                    else:
                        result = func(*args, **kwargs)
                    
                    end_time = time.time()
                    end_memory = psutil.Process().memory_info().rss
                    
                    # 记录性能数据
                    self._record_performance(func_name, {
                        'execution_time': end_time - start_time,
                        'memory_delta': end_memory - start_memory,
                        'success': True
                    })
                    
                    return result
                    
                except Exception as e:
                    end_time = time.time()
                    self._record_performance(func_name, {
                        'execution_time': end_time - start_time,
                        'memory_delta': 0,
                        'success': False,
                        'error': str(e)
                    })
                    raise e
            
            def sync_wrapper(*args, **kwargs):
                start_time = time.time()
                start_memory = psutil.Process().memory_info().rss
                
                try:
                    result = func(*args, **kwargs)
                    
                    end_time = time.time()
                    end_memory = psutil.Process().memory_info().rss
                    
                    self._record_performance(func_name, {
                        'execution_time': end_time - start_time,
                        'memory_delta': end_memory - start_memory,
                        'success': True
                    })
                    
                    return result
                    
                except Exception as e:
                    end_time = time.time()
                    self._record_performance(func_name, {
                        'execution_time': end_time - start_time,
                        'memory_delta': 0,
                        'success': False,
                        'error': str(e)
                    })
                    raise e
            
            return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
        return decorator
    
    def _record_performance(self, func_name: str, metrics: Dict[str, Any]) -> None:
        """记录性能数据"""
        if func_name not in self.profiles:
            self.profiles[func_name] = {
                'call_count': 0,
                'total_time': 0.0,
                'total_memory': 0,
                'success_count': 0,
                'error_count': 0,
                'execution_times': [],
                'memory_deltas': []
            }
        
        profile = self.profiles[func_name]
        profile['call_count'] += 1
        profile['total_time'] += metrics['execution_time']
        profile['total_memory'] += metrics['memory_delta']
        
        if metrics['success']:
            profile['success_count'] += 1
        else:
            profile['error_count'] += 1
        
        # 记录历史数据
        profile['execution_times'].append(metrics['execution_time'])
        profile['memory_deltas'].append(metrics['memory_delta'])
        
        # 保持历史数据在合理范围内
        if len(profile['execution_times']) > 1000:
            profile['execution_times'].pop(0)
        if len(profile['memory_deltas']) > 1000:
            profile['memory_deltas'].pop(0)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        report = {}
        
        for func_name, profile in self.profiles.items():
            if profile['call_count'] > 0:
                avg_time = profile['total_time'] / profile['call_count']
                avg_memory = profile['total_memory'] / profile['call_count']
                success_rate = (profile['success_count'] / profile['call_count']) * 100
                
                # 计算百分位数
                execution_times = sorted(profile['execution_times'])
                if execution_times:
                    p50 = execution_times[len(execution_times) // 2]
                    p95 = execution_times[int(len(execution_times) * 0.95)]
                    p99 = execution_times[int(len(execution_times) * 0.99)]
                else:
                    p50 = p95 = p99 = 0
                
                report[func_name] = {
                    'call_count': profile['call_count'],
                    'avg_execution_time': avg_time,
                    'avg_memory_delta': avg_memory,
                    'success_rate': success_rate,
                    'p50_time': p50,
                    'p95_time': p95,
                    'p99_time': p99,
                    'total_time': profile['total_time']
                }
        
        return report
    
    def set_benchmark(self, func_name: str, target_time: float, target_memory: int) -> None:
        """设置性能基准"""
        self.benchmarks[func_name] = {
            'target_time': target_time,
            'target_memory': target_memory
        }
    
    def check_benchmarks(self) -> Dict[str, Any]:
        """检查性能基准"""
        results = {}
        
        for func_name, benchmark in self.benchmarks.items():
            if func_name in self.profiles:
                profile = self.profiles[func_name]
                if profile['call_count'] > 0:
                    avg_time = profile['total_time'] / profile['call_count']
                    avg_memory = profile['total_memory'] / profile['call_count']
                    
                    results[func_name] = {
                        'time_benchmark_met': avg_time <= benchmark['target_time'],
                        'memory_benchmark_met': avg_memory <= benchmark['target_memory'],
                        'actual_time': avg_time,
                        'target_time': benchmark['target_time'],
                        'actual_memory': avg_memory,
                        'target_memory': benchmark['target_memory']
                    }
        
        return results


class ProductionOptimizer:
    """生产级优化器主类"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化生产级优化器

        Args:
            config: 配置参数
        """
        self.logger = logging.getLogger(__name__)
        self.config = config or {}

        # 初始化组件
        self.resource_manager = ResourceManager(
            max_workers=self.config.get('max_workers', None)
        )

        self.cache_manager = CacheManager(
            max_size=self.config.get('cache_size', 1000),
            ttl=self.config.get('cache_ttl', 3600)
        )

        self.load_balancer = LoadBalancer(
            strategy=self.config.get('load_balance_strategy', 'round_robin')
        )

        self.profiler = PerformanceProfiler()

        # 部署配置
        self.deployment_mode = DeploymentMode(self.config.get('deployment_mode', 'development'))
        self.scaling_strategy = ScalingStrategy(self.config.get('scaling_strategy', 'auto'))

        # 性能目标
        self.performance_targets = {
            'max_latency_p95': self.config.get('max_latency_p95', 2000),  # ms
            'min_throughput': self.config.get('min_throughput', 10),      # requests/second
            'max_error_rate': self.config.get('max_error_rate', 1),       # %
            'min_availability': self.config.get('min_availability', 99.9) # %
        }

        # 运行统计
        self.runtime_stats = {
            'start_time': datetime.now(),
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_response_time': 0.0,
            'response_times': [],
            'error_types': {}
        }

        # 启动监控
        self.resource_manager.start_monitoring()

    async def optimize_request_processing(self, request_handler: Callable,
                                        request_data: Any, **kwargs) -> Dict[str, Any]:
        """
        优化请求处理

        Args:
            request_handler: 请求处理函数
            request_data: 请求数据
            **kwargs: 额外参数

        Returns:
            处理结果
        """
        request_id = f"req_{int(time.time() * 1000)}"
        start_time = time.time()

        try:
            self.runtime_stats['total_requests'] += 1

            # 1. 缓存检查
            cache_key = self._generate_cache_key(request_handler.__name__, request_data, kwargs)
            cached_result = self.cache_manager.get(cache_key)

            if cached_result is not None:
                self.logger.debug(f"缓存命中: {request_id}")
                response_time = (time.time() - start_time) * 1000
                self._update_stats(response_time, True)

                return {
                    'request_id': request_id,
                    'success': True,
                    'result': cached_result,
                    'cached': True,
                    'response_time': response_time
                }

            # 2. 负载均衡
            worker_id = self.load_balancer.get_worker()
            if worker_id:
                self.load_balancer.report_request_start(worker_id)

            # 3. 性能分析
            profiled_handler = self.profiler.profile_function(request_handler.__name__)(request_handler)

            # 4. 资源管理执行
            result = await self.resource_manager.execute_task_async(
                profiled_handler, request_data, **kwargs
            )

            # 5. 缓存结果
            if self._should_cache_result(result):
                self.cache_manager.set(cache_key, result)

            response_time = (time.time() - start_time) * 1000
            self._update_stats(response_time, True)

            # 6. 报告负载均衡器
            if worker_id:
                self.load_balancer.report_request_end(worker_id, True, response_time)

            return {
                'request_id': request_id,
                'success': True,
                'result': result,
                'cached': False,
                'response_time': response_time,
                'worker_id': worker_id
            }

        except Exception as e:
            response_time = (time.time() - start_time) * 1000
            self._update_stats(response_time, False)

            # 记录错误类型
            error_type = type(e).__name__
            self.runtime_stats['error_types'][error_type] = self.runtime_stats['error_types'].get(error_type, 0) + 1

            # 报告负载均衡器
            if 'worker_id' in locals():
                self.load_balancer.report_request_end(worker_id, False, response_time)

            self.logger.error(f"请求处理失败 {request_id}: {e}")

            return {
                'request_id': request_id,
                'success': False,
                'error': str(e),
                'error_type': error_type,
                'response_time': response_time
            }

    def _generate_cache_key(self, func_name: str, request_data: Any, kwargs: Dict[str, Any]) -> str:
        """生成缓存键"""
        try:
            # 创建可序列化的键数据
            key_data = {
                'function': func_name,
                'data': str(request_data),
                'kwargs': sorted(kwargs.items())
            }

            # 使用pickle序列化并生成哈希
            import hashlib
            serialized = pickle.dumps(key_data)
            return hashlib.md5(serialized).hexdigest()

        except Exception:
            # 如果序列化失败，使用简单字符串
            return f"{func_name}_{hash(str(request_data))}_{hash(str(sorted(kwargs.items())))}"

    def _should_cache_result(self, result: Any) -> bool:
        """判断是否应该缓存结果"""
        # 简化逻辑：成功的结果且不是错误
        if isinstance(result, dict):
            return result.get('success', True) and 'error' not in result
        return result is not None

    def _update_stats(self, response_time: float, success: bool) -> None:
        """更新统计信息"""
        if success:
            self.runtime_stats['successful_requests'] += 1
        else:
            self.runtime_stats['failed_requests'] += 1

        self.runtime_stats['total_response_time'] += response_time
        self.runtime_stats['response_times'].append(response_time)

        # 保持响应时间历史在合理范围内
        if len(self.runtime_stats['response_times']) > 10000:
            self.runtime_stats['response_times'].pop(0)

    def get_performance_metrics(self) -> PerformanceMetrics:
        """获取性能指标"""
        total_requests = self.runtime_stats['total_requests']
        response_times = self.runtime_stats['response_times']

        if total_requests == 0:
            return PerformanceMetrics(0, 0, 0, 0, 0, 0, 0, 0)

        # 计算吞吐量
        uptime = (datetime.now() - self.runtime_stats['start_time']).total_seconds()
        throughput = total_requests / max(uptime, 1)

        # 计算延迟百分位数
        if response_times:
            sorted_times = sorted(response_times)
            latency_p50 = sorted_times[len(sorted_times) // 2]
            latency_p95 = sorted_times[int(len(sorted_times) * 0.95)]
            latency_p99 = sorted_times[int(len(sorted_times) * 0.99)]
        else:
            latency_p50 = latency_p95 = latency_p99 = 0

        # 获取系统资源使用情况
        resource_status = self.resource_manager.get_resource_status()
        cpu_usage = resource_status['current_cpu']
        memory_usage = resource_status['current_memory']

        # 计算错误率
        error_rate = (self.runtime_stats['failed_requests'] / total_requests) * 100

        # 计算可用性
        availability = (self.runtime_stats['successful_requests'] / total_requests) * 100

        return PerformanceMetrics(
            throughput=throughput,
            latency_p50=latency_p50,
            latency_p95=latency_p95,
            latency_p99=latency_p99,
            cpu_usage=cpu_usage,
            memory_usage=memory_usage,
            error_rate=error_rate,
            availability=availability
        )

    def check_performance_targets(self) -> Dict[str, Any]:
        """检查性能目标"""
        metrics = self.get_performance_metrics()

        checks = {
            'latency_p95_ok': metrics.latency_p95 <= self.performance_targets['max_latency_p95'],
            'throughput_ok': metrics.throughput >= self.performance_targets['min_throughput'],
            'error_rate_ok': metrics.error_rate <= self.performance_targets['max_error_rate'],
            'availability_ok': metrics.availability >= self.performance_targets['min_availability']
        }

        all_targets_met = all(checks.values())

        return {
            'all_targets_met': all_targets_met,
            'individual_checks': checks,
            'current_metrics': metrics.to_dict(),
            'performance_targets': self.performance_targets,
            'recommendations': self._get_performance_recommendations(metrics, checks)
        }

    def _get_performance_recommendations(self, metrics: PerformanceMetrics,
                                       checks: Dict[str, bool]) -> List[str]:
        """获取性能改进建议"""
        recommendations = []

        if not checks['latency_p95_ok']:
            recommendations.append(f"P95延迟过高({metrics.latency_p95:.1f}ms)，建议优化算法或增加缓存")

        if not checks['throughput_ok']:
            recommendations.append(f"吞吐量过低({metrics.throughput:.1f} req/s)，建议增加并发处理能力")

        if not checks['error_rate_ok']:
            recommendations.append(f"错误率过高({metrics.error_rate:.1f}%)，建议检查错误处理逻辑")

        if not checks['availability_ok']:
            recommendations.append(f"可用性不足({metrics.availability:.1f}%)，建议增强容错机制")

        if metrics.cpu_usage > 80:
            recommendations.append("CPU使用率过高，建议优化计算密集型操作")

        if metrics.memory_usage > 80:
            recommendations.append("内存使用率过高，建议优化内存管理")

        # 缓存相关建议
        cache_stats = self.cache_manager.get_stats()
        if cache_stats['hit_rate'] < 50:
            recommendations.append("缓存命中率较低，建议优化缓存策略")

        return recommendations

    def auto_scale(self) -> Dict[str, Any]:
        """自动扩展"""
        if self.scaling_strategy != ScalingStrategy.AUTO:
            return {'scaling': False, 'reason': '自动扩展未启用'}

        metrics = self.get_performance_metrics()
        scaling_actions = []

        # 基于CPU使用率扩展
        if metrics.cpu_usage > 80:
            scaling_actions.append('增加CPU资源或工作线程')
        elif metrics.cpu_usage < 20:
            scaling_actions.append('可以减少CPU资源')

        # 基于内存使用率扩展
        if metrics.memory_usage > 80:
            scaling_actions.append('增加内存资源')
        elif metrics.memory_usage < 20:
            scaling_actions.append('可以减少内存资源')

        # 基于延迟扩展
        if metrics.latency_p95 > self.performance_targets['max_latency_p95']:
            scaling_actions.append('增加处理能力以降低延迟')

        # 基于吞吐量扩展
        if metrics.throughput < self.performance_targets['min_throughput']:
            scaling_actions.append('增加并发处理能力')

        return {
            'scaling': len(scaling_actions) > 0,
            'actions': scaling_actions,
            'current_metrics': metrics.to_dict(),
            'timestamp': datetime.now().isoformat()
        }

    def get_deployment_report(self) -> Dict[str, Any]:
        """获取部署报告"""
        uptime = datetime.now() - self.runtime_stats['start_time']

        return {
            'deployment_info': {
                'mode': self.deployment_mode.value,
                'scaling_strategy': self.scaling_strategy.value,
                'uptime_seconds': uptime.total_seconds(),
                'start_time': self.runtime_stats['start_time'].isoformat()
            },
            'performance_metrics': self.get_performance_metrics().to_dict(),
            'performance_targets': self.check_performance_targets(),
            'resource_status': self.resource_manager.get_resource_status(),
            'cache_stats': self.cache_manager.get_stats(),
            'load_balancer_stats': self.load_balancer.get_worker_stats(),
            'profiler_report': self.profiler.get_performance_report(),
            'runtime_statistics': {
                'total_requests': self.runtime_stats['total_requests'],
                'successful_requests': self.runtime_stats['successful_requests'],
                'failed_requests': self.runtime_stats['failed_requests'],
                'success_rate': (self.runtime_stats['successful_requests'] /
                               max(self.runtime_stats['total_requests'], 1)) * 100,
                'avg_response_time': (self.runtime_stats['total_response_time'] /
                                    max(self.runtime_stats['total_requests'], 1)),
                'error_distribution': self.runtime_stats['error_types']
            },
            'scaling_recommendations': self.auto_scale(),
            'deployment_recommendations': self._get_deployment_recommendations()
        }

    def _get_deployment_recommendations(self) -> List[str]:
        """获取部署改进建议"""
        recommendations = []

        # 基于部署模式的建议
        if self.deployment_mode == DeploymentMode.DEVELOPMENT:
            recommendations.append("开发模式：建议启用详细日志和调试功能")
        elif self.deployment_mode == DeploymentMode.PRODUCTION:
            recommendations.append("生产模式：确保监控和告警系统正常运行")

        # 基于性能的建议
        metrics = self.get_performance_metrics()
        if metrics.throughput < 1:
            recommendations.append("吞吐量极低，检查系统配置和资源分配")

        if metrics.error_rate > 5:
            recommendations.append("错误率过高，建议增强错误处理和重试机制")

        # 基于资源使用的建议
        resource_status = self.resource_manager.get_resource_status()
        if resource_status['failed_tasks'] > resource_status['completed_tasks'] * 0.1:
            recommendations.append("任务失败率较高，检查任务执行逻辑")

        # 缓存建议
        cache_stats = self.cache_manager.get_stats()
        if cache_stats['evictions'] > cache_stats['hits']:
            recommendations.append("缓存淘汰过于频繁，建议增加缓存大小")

        recommendations.extend([
            "定期备份重要数据和配置",
            "建立完善的监控和告警体系",
            "实施蓝绿部署或滚动更新策略",
            "配置负载均衡和故障转移",
            "建立性能基准和回归测试"
        ])

        return recommendations

    def cleanup(self) -> None:
        """清理资源"""
        try:
            self.resource_manager.cleanup()
            self.cache_manager.clear()
            self.logger.info("生产级优化器已清理")
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")

    def __enter__(self):
        """上下文管理器入口"""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.cleanup()
