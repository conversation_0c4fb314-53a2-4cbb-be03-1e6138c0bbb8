#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自适应分析框架
构建跨行业、跨场景的智能分析系统，提升任务泛化能力
"""

import asyncio
import logging
import json
import numpy as np
from typing import Dict, List, Any, Optional, Union, Tuple
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import re


class IndustryType(Enum):
    """行业类型"""
    BANKING = "banking"                    # 银行业
    INSURANCE = "insurance"                # 保险业
    SECURITIES = "securities"              # 证券业
    MANUFACTURING = "manufacturing"        # 制造业
    TECHNOLOGY = "technology"              # 科技业
    HEALTHCARE = "healthcare"              # 医疗健康
    ENERGY = "energy"                      # 能源业
    REAL_ESTATE = "real_estate"           # 房地产
    RETAIL = "retail"                      # 零售业
    TELECOMMUNICATIONS = "telecommunications" # 电信业
    UTILITIES = "utilities"                # 公用事业
    TRANSPORTATION = "transportation"      # 交通运输
    AGRICULTURE = "agriculture"            # 农业
    MINING = "mining"                      # 采矿业
    CONSTRUCTION = "construction"          # 建筑业
    GENERAL = "general"                    # 通用


class AnalysisScope(Enum):
    """分析范围"""
    COMPANY = "company"                    # 公司分析
    INDUSTRY = "industry"                  # 行业分析
    MACRO = "macro"                        # 宏观分析
    SECTOR = "sector"                      # 板块分析
    REGIONAL = "regional"                  # 区域分析
    GLOBAL = "global"                      # 全球分析


@dataclass
class IndustryProfile:
    """行业特征配置"""
    industry_type: IndustryType
    key_metrics: List[str]
    regulatory_factors: List[str]
    risk_factors: List[str]
    valuation_methods: List[str]
    seasonal_patterns: Dict[str, float]
    benchmark_ratios: Dict[str, float]
    analysis_focus: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'industry_type': self.industry_type.value,
            'key_metrics': self.key_metrics,
            'regulatory_factors': self.regulatory_factors,
            'risk_factors': self.risk_factors,
            'valuation_methods': self.valuation_methods,
            'seasonal_patterns': self.seasonal_patterns,
            'benchmark_ratios': self.benchmark_ratios,
            'analysis_focus': self.analysis_focus
        }


class IndustryProfileManager:
    """行业特征管理器"""
    
    def __init__(self):
        """初始化行业特征管理器"""
        self.logger = logging.getLogger(__name__)
        self.industry_profiles = self._initialize_industry_profiles()
    
    def _initialize_industry_profiles(self) -> Dict[IndustryType, IndustryProfile]:
        """初始化行业特征配置"""
        profiles = {}
        
        # 银行业
        profiles[IndustryType.BANKING] = IndustryProfile(
            industry_type=IndustryType.BANKING,
            key_metrics=['net_interest_margin', 'loan_loss_provision', 'tier1_capital_ratio', 'roa', 'roe'],
            regulatory_factors=['capital_adequacy', 'liquidity_ratio', 'leverage_ratio'],
            risk_factors=['credit_risk', 'market_risk', 'operational_risk', 'liquidity_risk'],
            valuation_methods=['price_to_book', 'price_to_tangible_book', 'dividend_yield'],
            seasonal_patterns={'Q1': 1.0, 'Q2': 1.05, 'Q3': 1.02, 'Q4': 1.08},
            benchmark_ratios={'roe': 12.0, 'roa': 1.0, 'nim': 2.5, 'cost_income_ratio': 60.0},
            analysis_focus=['asset_quality', 'capital_strength', 'profitability', 'efficiency']
        )
        
        # 制造业
        profiles[IndustryType.MANUFACTURING] = IndustryProfile(
            industry_type=IndustryType.MANUFACTURING,
            key_metrics=['gross_margin', 'operating_margin', 'inventory_turnover', 'asset_turnover'],
            regulatory_factors=['environmental_compliance', 'safety_standards', 'trade_policies'],
            risk_factors=['supply_chain_risk', 'commodity_price_risk', 'demand_volatility'],
            valuation_methods=['price_to_earnings', 'ev_to_ebitda', 'price_to_sales'],
            seasonal_patterns={'Q1': 0.95, 'Q2': 1.05, 'Q3': 1.0, 'Q4': 1.1},
            benchmark_ratios={'gross_margin': 25.0, 'operating_margin': 10.0, 'roe': 15.0},
            analysis_focus=['operational_efficiency', 'cost_management', 'capacity_utilization']
        )
        
        # 科技业
        profiles[IndustryType.TECHNOLOGY] = IndustryProfile(
            industry_type=IndustryType.TECHNOLOGY,
            key_metrics=['revenue_growth', 'rd_intensity', 'gross_margin', 'customer_acquisition_cost'],
            regulatory_factors=['data_privacy', 'antitrust', 'intellectual_property'],
            risk_factors=['technology_obsolescence', 'competition', 'regulatory_changes'],
            valuation_methods=['price_to_sales', 'ev_to_revenue', 'price_to_earnings_growth'],
            seasonal_patterns={'Q1': 1.0, 'Q2': 1.02, 'Q3': 0.98, 'Q4': 1.15},
            benchmark_ratios={'revenue_growth': 20.0, 'gross_margin': 70.0, 'rd_ratio': 15.0},
            analysis_focus=['innovation_capability', 'market_position', 'scalability']
        )
        
        # 房地产
        profiles[IndustryType.REAL_ESTATE] = IndustryProfile(
            industry_type=IndustryType.REAL_ESTATE,
            key_metrics=['occupancy_rate', 'rental_yield', 'debt_to_equity', 'nav_per_share'],
            regulatory_factors=['zoning_laws', 'rent_control', 'property_taxes'],
            risk_factors=['interest_rate_risk', 'market_cycles', 'regulatory_risk'],
            valuation_methods=['price_to_nav', 'dividend_yield', 'price_to_funds_from_operations'],
            seasonal_patterns={'Q1': 0.9, 'Q2': 1.1, 'Q3': 1.05, 'Q4': 0.95},
            benchmark_ratios={'occupancy_rate': 90.0, 'debt_ratio': 40.0, 'dividend_yield': 4.0},
            analysis_focus=['portfolio_quality', 'geographic_diversification', 'development_pipeline']
        )
        
        # 能源业
        profiles[IndustryType.ENERGY] = IndustryProfile(
            industry_type=IndustryType.ENERGY,
            key_metrics=['production_volume', 'reserves_replacement', 'finding_costs', 'netback'],
            regulatory_factors=['environmental_regulations', 'carbon_pricing', 'drilling_permits'],
            risk_factors=['commodity_price_volatility', 'geopolitical_risk', 'environmental_risk'],
            valuation_methods=['ev_to_ebitda', 'price_to_cash_flow', 'nav_per_share'],
            seasonal_patterns={'Q1': 1.1, 'Q2': 0.95, 'Q3': 1.0, 'Q4': 1.05},
            benchmark_ratios={'debt_ratio': 30.0, 'roe': 12.0, 'capex_ratio': 15.0},
            analysis_focus=['reserve_quality', 'cost_structure', 'capital_discipline']
        )
        
        # 通用配置
        profiles[IndustryType.GENERAL] = IndustryProfile(
            industry_type=IndustryType.GENERAL,
            key_metrics=['revenue', 'net_income', 'total_assets', 'roe', 'roa'],
            regulatory_factors=['general_compliance', 'tax_regulations'],
            risk_factors=['market_risk', 'operational_risk', 'financial_risk'],
            valuation_methods=['price_to_earnings', 'price_to_book', 'ev_to_ebitda'],
            seasonal_patterns={'Q1': 1.0, 'Q2': 1.0, 'Q3': 1.0, 'Q4': 1.0},
            benchmark_ratios={'roe': 15.0, 'roa': 5.0, 'debt_ratio': 40.0},
            analysis_focus=['profitability', 'efficiency', 'growth']
        )
        
        return profiles
    
    def get_industry_profile(self, industry: Union[str, IndustryType]) -> IndustryProfile:
        """获取行业特征配置"""
        if isinstance(industry, str):
            # 尝试匹配行业类型
            industry_type = self._match_industry_type(industry)
        else:
            industry_type = industry
        
        return self.industry_profiles.get(industry_type, self.industry_profiles[IndustryType.GENERAL])
    
    def _match_industry_type(self, industry_name: str) -> IndustryType:
        """匹配行业类型"""
        industry_name = industry_name.lower().strip()
        
        # 关键词匹配
        industry_keywords = {
            IndustryType.BANKING: ['银行', '银行业', 'bank', 'banking'],
            IndustryType.INSURANCE: ['保险', '保险业', 'insurance'],
            IndustryType.SECURITIES: ['证券', '券商', 'securities', 'brokerage'],
            IndustryType.MANUFACTURING: ['制造', '制造业', 'manufacturing', '生产'],
            IndustryType.TECHNOLOGY: ['科技', '技术', 'technology', 'tech', '软件', 'software'],
            IndustryType.HEALTHCARE: ['医疗', '健康', 'healthcare', 'medical', '医药'],
            IndustryType.ENERGY: ['能源', 'energy', '石油', 'oil', '天然气', 'gas'],
            IndustryType.REAL_ESTATE: ['房地产', 'real estate', '地产', 'property'],
            IndustryType.RETAIL: ['零售', 'retail', '商贸'],
            IndustryType.TELECOMMUNICATIONS: ['电信', 'telecom', '通信'],
            IndustryType.UTILITIES: ['公用事业', 'utilities', '水电'],
            IndustryType.TRANSPORTATION: ['交通', 'transportation', '运输'],
            IndustryType.AGRICULTURE: ['农业', 'agriculture', '农林'],
            IndustryType.MINING: ['采矿', 'mining', '矿业'],
            IndustryType.CONSTRUCTION: ['建筑', 'construction', '建设']
        }
        
        for industry_type, keywords in industry_keywords.items():
            for keyword in keywords:
                if keyword in industry_name:
                    return industry_type
        
        return IndustryType.GENERAL
    
    def get_adaptive_metrics(self, industry: Union[str, IndustryType], 
                           analysis_scope: AnalysisScope) -> List[str]:
        """获取自适应指标"""
        profile = self.get_industry_profile(industry)
        base_metrics = profile.key_metrics.copy()
        
        # 根据分析范围调整指标
        if analysis_scope == AnalysisScope.COMPANY:
            base_metrics.extend(['market_cap', 'pe_ratio', 'pb_ratio'])
        elif analysis_scope == AnalysisScope.INDUSTRY:
            base_metrics.extend(['industry_concentration', 'market_share', 'competitive_position'])
        elif analysis_scope == AnalysisScope.MACRO:
            base_metrics.extend(['gdp_correlation', 'interest_rate_sensitivity', 'inflation_impact'])
        
        return list(set(base_metrics))  # 去重
    
    def get_risk_assessment_framework(self, industry: Union[str, IndustryType]) -> Dict[str, Any]:
        """获取风险评估框架"""
        profile = self.get_industry_profile(industry)
        
        return {
            'primary_risks': profile.risk_factors,
            'regulatory_risks': profile.regulatory_factors,
            'risk_weights': self._calculate_risk_weights(profile),
            'mitigation_strategies': self._get_mitigation_strategies(profile)
        }
    
    def _calculate_risk_weights(self, profile: IndustryProfile) -> Dict[str, float]:
        """计算风险权重"""
        risk_weights = {}
        total_risks = len(profile.risk_factors)
        
        # 基础权重分配
        base_weight = 1.0 / total_risks
        
        for risk in profile.risk_factors:
            if 'market' in risk.lower():
                risk_weights[risk] = base_weight * 1.2
            elif 'regulatory' in risk.lower():
                risk_weights[risk] = base_weight * 1.1
            elif 'operational' in risk.lower():
                risk_weights[risk] = base_weight * 0.9
            else:
                risk_weights[risk] = base_weight
        
        # 归一化
        total_weight = sum(risk_weights.values())
        for risk in risk_weights:
            risk_weights[risk] /= total_weight
        
        return risk_weights
    
    def _get_mitigation_strategies(self, profile: IndustryProfile) -> Dict[str, List[str]]:
        """获取风险缓解策略"""
        strategies = {}
        
        for risk in profile.risk_factors:
            if 'credit' in risk.lower():
                strategies[risk] = ['多元化投资组合', '严格信用审查', '动态风险定价']
            elif 'market' in risk.lower():
                strategies[risk] = ['对冲交易', '分散投资', '动态资产配置']
            elif 'operational' in risk.lower():
                strategies[risk] = ['流程优化', '技术升级', '人员培训']
            elif 'regulatory' in risk.lower():
                strategies[risk] = ['合规监控', '政策跟踪', '预案制定']
            else:
                strategies[risk] = ['风险监控', '应急预案', '保险覆盖']
        
        return strategies


class AdaptiveAnalysisEngine:
    """自适应分析引擎"""
    
    def __init__(self):
        """初始化自适应分析引擎"""
        self.logger = logging.getLogger(__name__)
        self.profile_manager = IndustryProfileManager()
        self.analysis_templates = self._initialize_analysis_templates()
        self.adaptation_history = {}
    
    def _initialize_analysis_templates(self) -> Dict[str, Dict[str, Any]]:
        """初始化分析模板"""
        return {
            'financial_health': {
                'required_metrics': ['revenue', 'net_income', 'total_assets', 'total_equity'],
                'optional_metrics': ['cash_flow', 'debt_ratio', 'current_ratio'],
                'analysis_steps': ['liquidity_analysis', 'profitability_analysis', 'leverage_analysis'],
                'output_format': 'comprehensive_report'
            },
            'valuation_analysis': {
                'required_metrics': ['market_cap', 'revenue', 'net_income'],
                'optional_metrics': ['book_value', 'cash_flow', 'growth_rate'],
                'analysis_steps': ['multiple_valuation', 'dcf_analysis', 'comparable_analysis'],
                'output_format': 'valuation_report'
            },
            'risk_assessment': {
                'required_metrics': ['volatility', 'beta', 'debt_ratio'],
                'optional_metrics': ['var', 'credit_rating', 'liquidity_ratio'],
                'analysis_steps': ['market_risk', 'credit_risk', 'operational_risk'],
                'output_format': 'risk_report'
            },
            'growth_analysis': {
                'required_metrics': ['revenue_growth', 'earnings_growth'],
                'optional_metrics': ['market_share', 'capex', 'rd_spending'],
                'analysis_steps': ['historical_growth', 'growth_drivers', 'future_prospects'],
                'output_format': 'growth_report'
            }
        }
    
    async def adapt_analysis(self, analysis_type: str, target_data: Dict[str, Any], 
                           context: Dict[str, Any]) -> Dict[str, Any]:
        """
        自适应分析
        
        Args:
            analysis_type: 分析类型
            target_data: 目标数据
            context: 分析上下文
            
        Returns:
            自适应分析结果
        """
        try:
            adaptation_id = f"adapt_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 1. 识别行业和范围
            industry = self._identify_industry(target_data, context)
            scope = self._identify_scope(context)
            
            # 2. 获取行业特征
            industry_profile = self.profile_manager.get_industry_profile(industry)
            
            # 3. 自适应指标选择
            adaptive_metrics = self.profile_manager.get_adaptive_metrics(industry, scope)
            
            # 4. 自适应分析模板
            adapted_template = self._adapt_analysis_template(analysis_type, industry_profile, adaptive_metrics)
            
            # 5. 执行自适应分析
            analysis_result = await self._execute_adaptive_analysis(
                adapted_template, target_data, industry_profile, context
            )
            
            # 6. 记录适应历史
            adaptation_record = {
                'adaptation_id': adaptation_id,
                'analysis_type': analysis_type,
                'identified_industry': industry.value if hasattr(industry, 'value') else str(industry),
                'analysis_scope': scope.value,
                'adaptive_metrics': adaptive_metrics,
                'adaptation_time': datetime.now().isoformat(),
                'success': analysis_result.get('success', False)
            }
            self.adaptation_history[adaptation_id] = adaptation_record
            
            # 7. 构建最终结果
            final_result = {
                'adaptation_id': adaptation_id,
                'analysis_type': analysis_type,
                'industry_context': {
                    'industry': industry.value if hasattr(industry, 'value') else str(industry),
                    'scope': scope.value,
                    'profile': industry_profile.to_dict()
                },
                'adaptive_configuration': {
                    'selected_metrics': adaptive_metrics,
                    'analysis_template': adapted_template,
                    'customizations': self._get_customizations(industry_profile, analysis_type)
                },
                'analysis_result': analysis_result,
                'generalization_score': self._calculate_generalization_score(adaptation_record, analysis_result)
            }
            
            self.logger.info(f"自适应分析完成: {adaptation_id} ({industry} - {scope})")
            
            return final_result
            
        except Exception as e:
            self.logger.error(f"自适应分析失败: {e}")
            return {
                'adaptation_id': 'error',
                'success': False,
                'error': str(e)
            }
    
    def _identify_industry(self, target_data: Dict[str, Any], context: Dict[str, Any]) -> IndustryType:
        """识别行业"""
        # 从上下文中获取行业信息
        if 'industry' in context:
            return self.profile_manager._match_industry_type(context['industry'])
        
        # 从公司信息中获取
        if 'company_info' in target_data and 'industry' in target_data['company_info']:
            return self.profile_manager._match_industry_type(target_data['company_info']['industry'])
        
        # 从股票代码推断（简化实现）
        if 'symbol' in target_data:
            symbol = target_data['symbol']
            if symbol.startswith('00'):  # 银行股
                return IndustryType.BANKING
            elif symbol.startswith('30'):  # 科技股
                return IndustryType.TECHNOLOGY
        
        return IndustryType.GENERAL
    
    def _identify_scope(self, context: Dict[str, Any]) -> AnalysisScope:
        """识别分析范围"""
        scope_indicators = context.get('scope', 'company').lower()
        
        if 'macro' in scope_indicators:
            return AnalysisScope.MACRO
        elif 'industry' in scope_indicators:
            return AnalysisScope.INDUSTRY
        elif 'sector' in scope_indicators:
            return AnalysisScope.SECTOR
        elif 'regional' in scope_indicators:
            return AnalysisScope.REGIONAL
        elif 'global' in scope_indicators:
            return AnalysisScope.GLOBAL
        else:
            return AnalysisScope.COMPANY

    def _adapt_analysis_template(self, analysis_type: str, industry_profile: IndustryProfile,
                               adaptive_metrics: List[str]) -> Dict[str, Any]:
        """自适应分析模板"""
        base_template = self.analysis_templates.get(analysis_type, self.analysis_templates['financial_health'])
        adapted_template = base_template.copy()

        # 根据行业特征调整模板
        adapted_template['industry_specific_metrics'] = industry_profile.key_metrics
        adapted_template['regulatory_considerations'] = industry_profile.regulatory_factors
        adapted_template['risk_factors'] = industry_profile.risk_factors
        adapted_template['valuation_methods'] = industry_profile.valuation_methods
        adapted_template['seasonal_adjustments'] = industry_profile.seasonal_patterns

        # 合并自适应指标
        all_metrics = set(adapted_template['required_metrics'])
        all_metrics.update(adaptive_metrics)
        all_metrics.update(industry_profile.key_metrics)
        adapted_template['comprehensive_metrics'] = list(all_metrics)

        # 调整分析步骤
        if industry_profile.industry_type == IndustryType.BANKING:
            adapted_template['analysis_steps'].extend(['capital_adequacy', 'asset_quality'])
        elif industry_profile.industry_type == IndustryType.TECHNOLOGY:
            adapted_template['analysis_steps'].extend(['innovation_analysis', 'market_position'])
        elif industry_profile.industry_type == IndustryType.REAL_ESTATE:
            adapted_template['analysis_steps'].extend(['portfolio_analysis', 'location_analysis'])

        return adapted_template

    async def _execute_adaptive_analysis(self, template: Dict[str, Any], target_data: Dict[str, Any],
                                       industry_profile: IndustryProfile, context: Dict[str, Any]) -> Dict[str, Any]:
        """执行自适应分析"""
        try:
            analysis_results = {}

            # 1. 数据预处理和验证
            processed_data = self._preprocess_data(target_data, template['comprehensive_metrics'])

            # 2. 行业特定分析
            industry_analysis = await self._perform_industry_specific_analysis(
                processed_data, industry_profile
            )
            analysis_results['industry_analysis'] = industry_analysis

            # 3. 核心财务分析
            financial_analysis = await self._perform_core_financial_analysis(
                processed_data, template, industry_profile
            )
            analysis_results['financial_analysis'] = financial_analysis

            # 4. 风险评估
            risk_analysis = await self._perform_adaptive_risk_assessment(
                processed_data, industry_profile
            )
            analysis_results['risk_analysis'] = risk_analysis

            # 5. 估值分析
            valuation_analysis = await self._perform_adaptive_valuation(
                processed_data, industry_profile
            )
            analysis_results['valuation_analysis'] = valuation_analysis

            # 6. 综合评分
            comprehensive_score = self._calculate_comprehensive_score(analysis_results, industry_profile)

            return {
                'success': True,
                'analysis_results': analysis_results,
                'comprehensive_score': comprehensive_score,
                'industry_insights': self._generate_industry_insights(analysis_results, industry_profile),
                'recommendations': self._generate_adaptive_recommendations(analysis_results, industry_profile)
            }

        except Exception as e:
            self.logger.error(f"自适应分析执行失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _preprocess_data(self, data: Dict[str, Any], required_metrics: List[str]) -> Dict[str, Any]:
        """数据预处理"""
        processed = {}

        # 提取财务数据
        if 'financial_statements' in data:
            processed.update(data['financial_statements'])

        # 提取股票数据
        if 'stock_data' in data:
            processed.update(data['stock_data'])

        # 提取公司信息
        if 'company_info' in data:
            processed.update(data['company_info'])

        # 计算衍生指标
        processed.update(self._calculate_derived_metrics(processed))

        # 填充缺失值
        for metric in required_metrics:
            if metric not in processed:
                processed[metric] = self._estimate_missing_value(metric, processed)

        return processed

    def _calculate_derived_metrics(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """计算衍生指标"""
        derived = {}

        # ROE计算
        if 'net_income' in data and 'total_equity' in data and data['total_equity'] > 0:
            derived['roe'] = (data['net_income'] / data['total_equity']) * 100

        # ROA计算
        if 'net_income' in data and 'total_assets' in data and data['total_assets'] > 0:
            derived['roa'] = (data['net_income'] / data['total_assets']) * 100

        # 债务比率
        if 'total_liabilities' in data and 'total_assets' in data and data['total_assets'] > 0:
            derived['debt_ratio'] = (data['total_liabilities'] / data['total_assets']) * 100

        # 流动比率
        if 'current_assets' in data and 'current_liabilities' in data and data['current_liabilities'] > 0:
            derived['current_ratio'] = data['current_assets'] / data['current_liabilities']

        # 净利润率
        if 'net_income' in data and 'revenue' in data and data['revenue'] > 0:
            derived['net_margin'] = (data['net_income'] / data['revenue']) * 100

        # 资产周转率
        if 'revenue' in data and 'total_assets' in data and data['total_assets'] > 0:
            derived['asset_turnover'] = data['revenue'] / data['total_assets']

        return derived

    def _estimate_missing_value(self, metric: str, data: Dict[str, Any]) -> float:
        """估算缺失值"""
        # 基于行业平均值或相关指标估算
        if metric == 'roe' and 'roa' in data:
            return data['roa'] * 2.5  # 简化估算
        elif metric == 'current_ratio':
            return 1.5  # 行业平均值
        elif metric == 'debt_ratio':
            return 40.0  # 行业平均值
        else:
            return 0.0

    async def _perform_industry_specific_analysis(self, data: Dict[str, Any],
                                                industry_profile: IndustryProfile) -> Dict[str, Any]:
        """执行行业特定分析"""
        analysis = {}

        if industry_profile.industry_type == IndustryType.BANKING:
            analysis = await self._banking_specific_analysis(data, industry_profile)
        elif industry_profile.industry_type == IndustryType.TECHNOLOGY:
            analysis = await self._technology_specific_analysis(data, industry_profile)
        elif industry_profile.industry_type == IndustryType.REAL_ESTATE:
            analysis = await self._real_estate_specific_analysis(data, industry_profile)
        elif industry_profile.industry_type == IndustryType.MANUFACTURING:
            analysis = await self._manufacturing_specific_analysis(data, industry_profile)
        else:
            analysis = await self._general_industry_analysis(data, industry_profile)

        return analysis

    async def _banking_specific_analysis(self, data: Dict[str, Any],
                                       industry_profile: IndustryProfile) -> Dict[str, Any]:
        """银行业特定分析"""
        analysis = {
            'analysis_type': 'banking_specific',
            'key_findings': [],
            'metrics': {}
        }

        # 净息差分析
        if 'net_interest_margin' in data:
            nim = data['net_interest_margin']
            benchmark_nim = industry_profile.benchmark_ratios.get('nim', 2.5)
            analysis['metrics']['net_interest_margin'] = {
                'value': nim,
                'benchmark': benchmark_nim,
                'performance': 'above' if nim > benchmark_nim else 'below'
            }
            analysis['key_findings'].append(f"净息差{nim:.2f}%，{'优于' if nim > benchmark_nim else '低于'}行业平均{benchmark_nim:.2f}%")

        # 资本充足率分析
        if 'tier1_capital_ratio' in data:
            car = data['tier1_capital_ratio']
            analysis['metrics']['capital_adequacy'] = {
                'tier1_ratio': car,
                'regulatory_minimum': 8.0,
                'status': 'adequate' if car >= 8.0 else 'insufficient'
            }
            analysis['key_findings'].append(f"一级资本充足率{car:.2f}%，{'满足' if car >= 8.0 else '不满足'}监管要求")

        # 资产质量分析
        if 'loan_loss_provision' in data:
            llp = data['loan_loss_provision']
            analysis['metrics']['asset_quality'] = {
                'loan_loss_provision': llp,
                'quality_assessment': 'good' if llp < 1.0 else 'concerning' if llp < 2.0 else 'poor'
            }
            analysis['key_findings'].append(f"贷款损失准备率{llp:.2f}%")

        return analysis

    async def _technology_specific_analysis(self, data: Dict[str, Any],
                                          industry_profile: IndustryProfile) -> Dict[str, Any]:
        """科技业特定分析"""
        analysis = {
            'analysis_type': 'technology_specific',
            'key_findings': [],
            'metrics': {}
        }

        # 研发强度分析
        if 'rd_intensity' in data:
            rd_ratio = data['rd_intensity']
            benchmark_rd = industry_profile.benchmark_ratios.get('rd_ratio', 15.0)
            analysis['metrics']['innovation_capability'] = {
                'rd_intensity': rd_ratio,
                'benchmark': benchmark_rd,
                'innovation_level': 'high' if rd_ratio > benchmark_rd else 'moderate'
            }
            analysis['key_findings'].append(f"研发强度{rd_ratio:.1f}%，创新能力{'较强' if rd_ratio > benchmark_rd else '一般'}")

        # 收入增长分析
        if 'revenue_growth' in data:
            growth = data['revenue_growth']
            benchmark_growth = industry_profile.benchmark_ratios.get('revenue_growth', 20.0)
            analysis['metrics']['growth_momentum'] = {
                'revenue_growth': growth,
                'benchmark': benchmark_growth,
                'growth_stage': 'high_growth' if growth > benchmark_growth else 'mature'
            }
            analysis['key_findings'].append(f"收入增长率{growth:.1f}%")

        # 毛利率分析
        if 'gross_margin' in data:
            margin = data['gross_margin']
            benchmark_margin = industry_profile.benchmark_ratios.get('gross_margin', 70.0)
            analysis['metrics']['profitability'] = {
                'gross_margin': margin,
                'benchmark': benchmark_margin,
                'competitive_position': 'strong' if margin > benchmark_margin else 'weak'
            }
            analysis['key_findings'].append(f"毛利率{margin:.1f}%")

        return analysis

    async def _real_estate_specific_analysis(self, data: Dict[str, Any],
                                           industry_profile: IndustryProfile) -> Dict[str, Any]:
        """房地产业特定分析"""
        analysis = {
            'analysis_type': 'real_estate_specific',
            'key_findings': [],
            'metrics': {}
        }

        # 出租率分析
        if 'occupancy_rate' in data:
            occupancy = data['occupancy_rate']
            benchmark_occupancy = industry_profile.benchmark_ratios.get('occupancy_rate', 90.0)
            analysis['metrics']['operational_efficiency'] = {
                'occupancy_rate': occupancy,
                'benchmark': benchmark_occupancy,
                'performance': 'excellent' if occupancy > 95 else 'good' if occupancy > 90 else 'poor'
            }
            analysis['key_findings'].append(f"出租率{occupancy:.1f}%")

        # 租金收益率分析
        if 'rental_yield' in data:
            yield_rate = data['rental_yield']
            analysis['metrics']['investment_return'] = {
                'rental_yield': yield_rate,
                'market_comparison': 'attractive' if yield_rate > 4.0 else 'moderate'
            }
            analysis['key_findings'].append(f"租金收益率{yield_rate:.2f}%")

        return analysis

    async def _manufacturing_specific_analysis(self, data: Dict[str, Any],
                                             industry_profile: IndustryProfile) -> Dict[str, Any]:
        """制造业特定分析"""
        analysis = {
            'analysis_type': 'manufacturing_specific',
            'key_findings': [],
            'metrics': {}
        }

        # 存货周转率分析
        if 'inventory_turnover' in data:
            turnover = data['inventory_turnover']
            analysis['metrics']['operational_efficiency'] = {
                'inventory_turnover': turnover,
                'efficiency_level': 'high' if turnover > 6 else 'moderate' if turnover > 4 else 'low'
            }
            analysis['key_findings'].append(f"存货周转率{turnover:.1f}次")

        # 毛利率分析
        if 'gross_margin' in data:
            margin = data['gross_margin']
            benchmark_margin = industry_profile.benchmark_ratios.get('gross_margin', 25.0)
            analysis['metrics']['cost_management'] = {
                'gross_margin': margin,
                'benchmark': benchmark_margin,
                'cost_control': 'effective' if margin > benchmark_margin else 'needs_improvement'
            }
            analysis['key_findings'].append(f"毛利率{margin:.1f}%")

        return analysis

    async def _general_industry_analysis(self, data: Dict[str, Any],
                                       industry_profile: IndustryProfile) -> Dict[str, Any]:
        """通用行业分析"""
        analysis = {
            'analysis_type': 'general_industry',
            'key_findings': [],
            'metrics': {}
        }

        # 基础财务指标分析
        if 'roe' in data:
            roe = data['roe']
            benchmark_roe = industry_profile.benchmark_ratios.get('roe', 15.0)
            analysis['metrics']['profitability'] = {
                'roe': roe,
                'benchmark': benchmark_roe,
                'performance': 'strong' if roe > benchmark_roe else 'weak'
            }
            analysis['key_findings'].append(f"ROE {roe:.1f}%")

        if 'debt_ratio' in data:
            debt_ratio = data['debt_ratio']
            analysis['metrics']['financial_stability'] = {
                'debt_ratio': debt_ratio,
                'leverage_level': 'high' if debt_ratio > 60 else 'moderate' if debt_ratio > 40 else 'low'
            }
            analysis['key_findings'].append(f"债务比率{debt_ratio:.1f}%")

        return analysis

    async def _perform_core_financial_analysis(self, data: Dict[str, Any], template: Dict[str, Any],
                                             industry_profile: IndustryProfile) -> Dict[str, Any]:
        """执行核心财务分析"""
        analysis = {
            'profitability': {},
            'liquidity': {},
            'efficiency': {},
            'leverage': {}
        }

        # 盈利能力分析
        if 'roe' in data:
            analysis['profitability']['roe'] = data['roe']
        if 'roa' in data:
            analysis['profitability']['roa'] = data['roa']
        if 'net_margin' in data:
            analysis['profitability']['net_margin'] = data['net_margin']

        # 流动性分析
        if 'current_ratio' in data:
            analysis['liquidity']['current_ratio'] = data['current_ratio']
        if 'quick_ratio' in data:
            analysis['liquidity']['quick_ratio'] = data['quick_ratio']

        # 效率分析
        if 'asset_turnover' in data:
            analysis['efficiency']['asset_turnover'] = data['asset_turnover']
        if 'inventory_turnover' in data:
            analysis['efficiency']['inventory_turnover'] = data['inventory_turnover']

        # 杠杆分析
        if 'debt_ratio' in data:
            analysis['leverage']['debt_ratio'] = data['debt_ratio']
        if 'debt_to_equity' in data:
            analysis['leverage']['debt_to_equity'] = data['debt_to_equity']

        return analysis

    async def _perform_adaptive_risk_assessment(self, data: Dict[str, Any],
                                              industry_profile: IndustryProfile) -> Dict[str, Any]:
        """执行自适应风险评估"""
        risk_framework = self.profile_manager.get_risk_assessment_framework(industry_profile.industry_type)

        risk_assessment = {
            'overall_risk_score': 0.0,
            'risk_breakdown': {},
            'risk_factors': risk_framework['primary_risks'],
            'mitigation_strategies': risk_framework['mitigation_strategies']
        }

        # 计算各类风险评分
        for risk_factor in risk_framework['primary_risks']:
            risk_score = self._calculate_risk_score(risk_factor, data, industry_profile)
            weight = risk_framework['risk_weights'].get(risk_factor, 0.2)

            risk_assessment['risk_breakdown'][risk_factor] = {
                'score': risk_score,
                'weight': weight,
                'level': 'high' if risk_score > 0.7 else 'medium' if risk_score > 0.4 else 'low'
            }

            risk_assessment['overall_risk_score'] += risk_score * weight

        return risk_assessment

    def _calculate_risk_score(self, risk_factor: str, data: Dict[str, Any],
                            industry_profile: IndustryProfile) -> float:
        """计算风险评分"""
        if 'market' in risk_factor.lower():
            # 市场风险基于波动性和beta
            volatility = data.get('volatility', 0.2)
            beta = data.get('beta', 1.0)
            return min((volatility * 2 + abs(beta - 1)) / 3, 1.0)

        elif 'credit' in risk_factor.lower():
            # 信用风险基于债务比率和流动性
            debt_ratio = data.get('debt_ratio', 40) / 100
            current_ratio = data.get('current_ratio', 1.5)
            return min(debt_ratio + max(0, (1.5 - current_ratio) * 0.5), 1.0)

        elif 'operational' in risk_factor.lower():
            # 运营风险基于效率指标
            asset_turnover = data.get('asset_turnover', 1.0)
            efficiency_score = min(asset_turnover / 2, 1.0)
            return 1.0 - efficiency_score

        elif 'liquidity' in risk_factor.lower():
            # 流动性风险
            current_ratio = data.get('current_ratio', 1.5)
            return max(0, (2.0 - current_ratio) / 2.0)

        else:
            # 默认风险评分
            return 0.5

    async def _perform_adaptive_valuation(self, data: Dict[str, Any],
                                        industry_profile: IndustryProfile) -> Dict[str, Any]:
        """执行自适应估值分析"""
        valuation_analysis = {
            'valuation_methods': industry_profile.valuation_methods,
            'multiples': {},
            'intrinsic_value_indicators': {},
            'relative_valuation': {}
        }

        # 计算估值倍数
        for method in industry_profile.valuation_methods:
            if method == 'price_to_earnings' and 'market_cap' in data and 'net_income' in data:
                if data['net_income'] > 0:
                    pe_ratio = data['market_cap'] / data['net_income']
                    valuation_analysis['multiples']['pe_ratio'] = pe_ratio

            elif method == 'price_to_book' and 'market_cap' in data and 'total_equity' in data:
                if data['total_equity'] > 0:
                    pb_ratio = data['market_cap'] / data['total_equity']
                    valuation_analysis['multiples']['pb_ratio'] = pb_ratio

            elif method == 'price_to_sales' and 'market_cap' in data and 'revenue' in data:
                if data['revenue'] > 0:
                    ps_ratio = data['market_cap'] / data['revenue']
                    valuation_analysis['multiples']['ps_ratio'] = ps_ratio

            elif method == 'dividend_yield' and 'dividend_per_share' in data and 'current_price' in data:
                if data['current_price'] > 0:
                    dividend_yield = (data['dividend_per_share'] / data['current_price']) * 100
                    valuation_analysis['multiples']['dividend_yield'] = dividend_yield

        # 相对估值分析
        for ratio_name, ratio_value in valuation_analysis['multiples'].items():
            benchmark = self._get_industry_benchmark(ratio_name, industry_profile)
            if benchmark:
                valuation_analysis['relative_valuation'][ratio_name] = {
                    'current': ratio_value,
                    'benchmark': benchmark,
                    'relative_position': 'overvalued' if ratio_value > benchmark * 1.2 else 'undervalued' if ratio_value < benchmark * 0.8 else 'fairly_valued'
                }

        return valuation_analysis

    def _get_industry_benchmark(self, ratio_name: str, industry_profile: IndustryProfile) -> Optional[float]:
        """获取行业基准"""
        benchmark_mapping = {
            'pe_ratio': 15.0,
            'pb_ratio': 2.0,
            'ps_ratio': 3.0,
            'dividend_yield': 3.0
        }

        # 行业特定调整
        base_value = benchmark_mapping.get(ratio_name)
        if not base_value:
            return None

        if industry_profile.industry_type == IndustryType.TECHNOLOGY:
            if ratio_name == 'pe_ratio':
                return base_value * 2.0  # 科技股PE较高
            elif ratio_name == 'ps_ratio':
                return base_value * 1.5
        elif industry_profile.industry_type == IndustryType.BANKING:
            if ratio_name == 'pb_ratio':
                return base_value * 0.8  # 银行股PB较低

        return base_value

    def _calculate_comprehensive_score(self, analysis_results: Dict[str, Any],
                                     industry_profile: IndustryProfile) -> float:
        """计算综合评分"""
        scores = []
        weights = []

        # 行业特定分析评分
        if 'industry_analysis' in analysis_results:
            industry_score = self._score_industry_analysis(analysis_results['industry_analysis'], industry_profile)
            scores.append(industry_score)
            weights.append(0.3)

        # 财务分析评分
        if 'financial_analysis' in analysis_results:
            financial_score = self._score_financial_analysis(analysis_results['financial_analysis'])
            scores.append(financial_score)
            weights.append(0.3)

        # 风险分析评分
        if 'risk_analysis' in analysis_results:
            risk_score = 1.0 - analysis_results['risk_analysis']['overall_risk_score']  # 风险越低评分越高
            scores.append(risk_score)
            weights.append(0.2)

        # 估值分析评分
        if 'valuation_analysis' in analysis_results:
            valuation_score = self._score_valuation_analysis(analysis_results['valuation_analysis'])
            scores.append(valuation_score)
            weights.append(0.2)

        # 加权平均
        if scores and weights:
            total_weight = sum(weights)
            weighted_score = sum(score * weight for score, weight in zip(scores, weights)) / total_weight
            return min(max(weighted_score, 0.0), 1.0)

        return 0.5  # 默认评分

    def _score_industry_analysis(self, industry_analysis: Dict[str, Any],
                               industry_profile: IndustryProfile) -> float:
        """评分行业分析"""
        if not industry_analysis.get('metrics'):
            return 0.5

        scores = []
        for metric_name, metric_data in industry_analysis['metrics'].items():
            if isinstance(metric_data, dict) and 'performance' in metric_data:
                performance = metric_data['performance']
                if performance in ['excellent', 'above', 'strong', 'high']:
                    scores.append(0.9)
                elif performance in ['good', 'adequate', 'moderate']:
                    scores.append(0.7)
                else:
                    scores.append(0.4)

        return sum(scores) / len(scores) if scores else 0.5

    def _score_financial_analysis(self, financial_analysis: Dict[str, Any]) -> float:
        """评分财务分析"""
        category_scores = []

        for category, metrics in financial_analysis.items():
            if metrics:
                # 简化评分逻辑
                if category == 'profitability':
                    roe = metrics.get('roe', 0)
                    category_scores.append(min(roe / 20, 1.0))  # ROE 20%为满分
                elif category == 'liquidity':
                    current_ratio = metrics.get('current_ratio', 1.0)
                    category_scores.append(min(current_ratio / 2, 1.0))  # 流动比率2为满分
                elif category == 'efficiency':
                    asset_turnover = metrics.get('asset_turnover', 0.5)
                    category_scores.append(min(asset_turnover / 2, 1.0))  # 资产周转率2为满分
                elif category == 'leverage':
                    debt_ratio = metrics.get('debt_ratio', 50)
                    category_scores.append(max(0, 1 - debt_ratio / 100))  # 债务比率越低越好

        return sum(category_scores) / len(category_scores) if category_scores else 0.5

    def _score_valuation_analysis(self, valuation_analysis: Dict[str, Any]) -> float:
        """评分估值分析"""
        relative_valuations = valuation_analysis.get('relative_valuation', {})
        if not relative_valuations:
            return 0.5

        scores = []
        for ratio_name, ratio_data in relative_valuations.items():
            position = ratio_data.get('relative_position', 'fairly_valued')
            if position == 'undervalued':
                scores.append(0.8)  # 低估是好事
            elif position == 'fairly_valued':
                scores.append(0.7)
            else:  # overvalued
                scores.append(0.4)

        return sum(scores) / len(scores) if scores else 0.5

    def _generate_industry_insights(self, analysis_results: Dict[str, Any],
                                  industry_profile: IndustryProfile) -> List[str]:
        """生成行业洞察"""
        insights = []

        # 基于行业类型生成洞察
        if industry_profile.industry_type == IndustryType.BANKING:
            insights.append("银行业关注资本充足率、资产质量和净息差表现")
        elif industry_profile.industry_type == IndustryType.TECHNOLOGY:
            insights.append("科技行业重点关注研发投入、增长速度和市场地位")
        elif industry_profile.industry_type == IndustryType.REAL_ESTATE:
            insights.append("房地产行业关注出租率、地理位置和政策影响")

        # 基于分析结果生成洞察
        if 'risk_analysis' in analysis_results:
            risk_score = analysis_results['risk_analysis']['overall_risk_score']
            if risk_score > 0.7:
                insights.append("当前风险水平较高，建议谨慎投资")
            elif risk_score < 0.3:
                insights.append("风险水平较低，投资安全性较高")

        # 基于估值分析生成洞察
        if 'valuation_analysis' in analysis_results:
            relative_valuations = analysis_results['valuation_analysis'].get('relative_valuation', {})
            undervalued_count = sum(1 for v in relative_valuations.values() if v.get('relative_position') == 'undervalued')
            if undervalued_count > len(relative_valuations) / 2:
                insights.append("多项估值指标显示可能存在投资机会")

        return insights

    def _generate_adaptive_recommendations(self, analysis_results: Dict[str, Any],
                                         industry_profile: IndustryProfile) -> List[str]:
        """生成自适应建议"""
        recommendations = []

        # 基于综合评分生成建议
        if hasattr(self, '_last_comprehensive_score'):
            score = self._last_comprehensive_score
            if score > 0.8:
                recommendations.append("综合表现优秀，建议积极关注")
            elif score > 0.6:
                recommendations.append("表现良好，可适度配置")
            else:
                recommendations.append("表现一般，建议谨慎对待")

        # 基于行业特征生成建议
        for focus_area in industry_profile.analysis_focus:
            if focus_area == 'asset_quality':
                recommendations.append("重点关注资产质量变化趋势")
            elif focus_area == 'innovation_capability':
                recommendations.append("关注研发投入和技术创新能力")
            elif focus_area == 'operational_efficiency':
                recommendations.append("监控运营效率和成本控制")

        # 基于风险评估生成建议
        if 'risk_analysis' in analysis_results:
            risk_breakdown = analysis_results['risk_analysis'].get('risk_breakdown', {})
            for risk_factor, risk_data in risk_breakdown.items():
                if risk_data.get('level') == 'high':
                    strategies = industry_profile.risk_factors
                    if risk_factor in strategies:
                        recommendations.append(f"高{risk_factor}风险，建议采取相应缓解措施")

        return recommendations

    def _get_customizations(self, industry_profile: IndustryProfile, analysis_type: str) -> Dict[str, Any]:
        """获取定制化配置"""
        return {
            'industry_focus': industry_profile.analysis_focus,
            'seasonal_adjustments': industry_profile.seasonal_patterns,
            'regulatory_considerations': industry_profile.regulatory_factors,
            'valuation_preferences': industry_profile.valuation_methods,
            'risk_priorities': industry_profile.risk_factors
        }

    def _calculate_generalization_score(self, adaptation_record: Dict[str, Any],
                                      analysis_result: Dict[str, Any]) -> float:
        """计算泛化能力评分"""
        factors = []

        # 成功率因子
        success_factor = 1.0 if analysis_result.get('success', False) else 0.0
        factors.append(success_factor * 0.4)

        # 适应性因子
        adaptive_metrics_count = len(adaptation_record.get('adaptive_metrics', []))
        adaptation_factor = min(adaptive_metrics_count / 10, 1.0)  # 10个指标为满分
        factors.append(adaptation_factor * 0.3)

        # 行业特异性因子
        industry = adaptation_record.get('identified_industry', 'general')
        specificity_factor = 0.9 if industry != 'general' else 0.6
        factors.append(specificity_factor * 0.3)

        return sum(factors)

    def get_generalization_report(self) -> Dict[str, Any]:
        """获取泛化能力报告"""
        if not self.adaptation_history:
            return {'total_adaptations': 0}

        total_adaptations = len(self.adaptation_history)
        successful_adaptations = sum(1 for record in self.adaptation_history.values() if record['success'])

        # 行业覆盖度
        industries_covered = set(record['identified_industry'] for record in self.adaptation_history.values())

        # 平均适应时间
        adaptation_times = [
            (datetime.fromisoformat(record['adaptation_time']) - datetime.fromisoformat(record['adaptation_time'])).total_seconds()
            for record in self.adaptation_history.values()
        ]
        avg_adaptation_time = sum(adaptation_times) / len(adaptation_times) if adaptation_times else 0

        return {
            'total_adaptations': total_adaptations,
            'success_rate': (successful_adaptations / total_adaptations) * 100,
            'industries_covered': list(industries_covered),
            'industry_coverage': len(industries_covered),
            'average_adaptation_time': avg_adaptation_time,
            'generalization_recommendations': self._get_generalization_recommendations()
        }

    def _get_generalization_recommendations(self) -> List[str]:
        """获取泛化改进建议"""
        recommendations = []

        if not self.adaptation_history:
            recommendations.append("增加更多行业和场景的测试")
            return recommendations

        success_rate = sum(1 for record in self.adaptation_history.values() if record['success']) / len(self.adaptation_history)

        if success_rate < 0.8:
            recommendations.append("提高自适应算法的准确性")

        industries_covered = set(record['identified_industry'] for record in self.adaptation_history.values())
        if len(industries_covered) < 5:
            recommendations.append("扩展更多行业的支持")

        recommendations.extend([
            "建立更完善的行业知识库",
            "优化指标选择算法",
            "增强跨行业对比能力",
            "实施动态基准更新机制"
        ])

        return recommendations
