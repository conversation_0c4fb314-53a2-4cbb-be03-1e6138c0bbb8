#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业金融知识库
提供RAG增强的金融专业知识支持
"""

import logging
import json
from typing import Dict, List, Any, Optional
from pathlib import Path


class FinancialKnowledgeBase:
    """专业金融知识库"""
    
    def __init__(self):
        """初始化金融知识库"""
        self.logger = logging.getLogger(__name__)
        self.knowledge_base = self._build_knowledge_base()
        
    def _build_knowledge_base(self) -> Dict[str, Any]:
        """构建金融知识库"""
        return {
            "dupont_analysis": self._get_dupont_knowledge(),
            "financial_ratios": self._get_financial_ratios_knowledge(),
            "trend_analysis": self._get_trend_analysis_knowledge(),
            "technical_indicators": self._get_technical_indicators_knowledge(),
            "risk_assessment": self._get_risk_assessment_knowledge(),
            "valuation_methods": self._get_valuation_methods_knowledge(),
            "data_sources": self._get_data_sources_knowledge(),
            "industry_analysis": self._get_industry_analysis_knowledge(),
            "macro_analysis": self._get_macro_analysis_knowledge()
        }
    
    def _get_dupont_knowledge(self) -> Dict[str, Any]:
        """杜邦分析知识"""
        return {
            "definition": "三步杜邦分析法将ROE分解为盈利能力、资产管理效率和财务杠杆三个核心部分",
            "formula": "ROE = 净利润率 × 总资产周转率 × 权益乘数",
            "components": {
                "净利润率": {
                    "formula": "净利润 / 营业收入",
                    "meaning": "衡量公司的盈利能力，反映每一元销售收入能产生多少净利润"
                },
                "总资产周转率": {
                    "formula": "营业收入 / 总资产平均余额",
                    "meaning": "衡量资产管理效率，反映公司利用资产创造销售收入的能力"
                },
                "权益乘数": {
                    "formula": "总资产 / 股东权益",
                    "meaning": "衡量财务杠杆，反映公司的负债程度"
                }
            },
            "analysis_framework": {
                "step1": "计算三个核心比率",
                "step2": "分析各比率的变化趋势",
                "step3": "识别ROE变化的主要驱动因素",
                "step4": "提出针对性的改进建议"
            }
        }
    
    def _get_financial_ratios_knowledge(self) -> Dict[str, Any]:
        """财务比率知识"""
        return {
            "profitability_ratios": {
                "销售毛利率": {
                    "formula": "(营业收入 - 营业成本) / 营业收入 × 100%",
                    "benchmark": {"excellent": ">30%", "good": "20-30%", "average": "10-20%", "poor": "<10%"},
                    "interpretation": "反映公司产品或服务的初始获利空间"
                },
                "销售净利率": {
                    "formula": "净利润 / 营业收入 × 100%",
                    "benchmark": {"excellent": ">15%", "good": "10-15%", "average": "5-10%", "poor": "<5%"},
                    "interpretation": "衡量公司最终的盈利能力"
                },
                "ROE": {
                    "formula": "净利润 / 股东权益平均余额 × 100%",
                    "benchmark": {"excellent": ">20%", "good": "15-20%", "average": "10-15%", "poor": "<10%"},
                    "interpretation": "衡量股东投入资本的回报水平"
                },
                "ROA": {
                    "formula": "净利润 / 总资产平均余额 × 100%",
                    "benchmark": {"excellent": ">10%", "good": "5-10%", "average": "2-5%", "poor": "<2%"},
                    "interpretation": "衡量公司利用资产创造利润的效率"
                }
            },
            "solvency_ratios": {
                "流动比率": {
                    "formula": "流动资产 / 流动负债",
                    "benchmark": {"excellent": ">2.5", "good": "2.0-2.5", "average": "1.5-2.0", "poor": "<1.5"},
                    "interpretation": "衡量公司用流动资产偿还短期债务的能力"
                },
                "速动比率": {
                    "formula": "(流动资产 - 存货) / 流动负债",
                    "benchmark": {"excellent": ">1.5", "good": "1.0-1.5", "average": "0.8-1.0", "poor": "<0.8"},
                    "interpretation": "比流动比率更严格，剔除了变现能力较慢的存货"
                },
                "资产负债率": {
                    "formula": "总负债 / 总资产 × 100%",
                    "benchmark": {"excellent": "<30%", "good": "30-50%", "average": "50-70%", "poor": ">70%"},
                    "interpretation": "反映公司的总资产中有多少是通过负债筹集的"
                },
                "产权比率": {
                    "formula": "总负债 / 股东权益",
                    "benchmark": {"excellent": "<0.5", "good": "0.5-1.0", "average": "1.0-2.0", "poor": ">2.0"},
                    "interpretation": "衡量公司的负债相对于股东权益的水平"
                }
            },
            "efficiency_ratios": {
                "总资产周转率": {
                    "formula": "营业收入 / 总资产平均余额",
                    "benchmark": {"excellent": ">1.5", "good": "1.0-1.5", "average": "0.5-1.0", "poor": "<0.5"},
                    "interpretation": "反映公司利用其全部资产创造销售收入的效率"
                },
                "应收账款周转率": {
                    "formula": "营业收入 / 应收账款平均余额",
                    "benchmark": {"excellent": ">12", "good": "8-12", "average": "4-8", "poor": "<4"},
                    "interpretation": "衡量公司收回应收账款的速度"
                },
                "存货周转率": {
                    "formula": "营业成本 / 存货平均余额",
                    "benchmark": {"excellent": ">8", "good": "6-8", "average": "4-6", "poor": "<4"},
                    "interpretation": "衡量公司销售存货的速度"
                }
            }
        }
    
    def _get_trend_analysis_knowledge(self) -> Dict[str, Any]:
        """趋势分析知识"""
        return {
            "trend_types": {
                "上升趋势": {
                    "definition": "价格走势出现一系列更高的高点和更高的低点",
                    "characteristics": ["Higher Highs", "Higher Lows", "市场情绪乐观"],
                    "strategy": "逢低做多为主"
                },
                "下降趋势": {
                    "definition": "价格走势出现一系列更低的高点和更低的低点", 
                    "characteristics": ["Lower Highs", "Lower Lows", "市场情绪悲观"],
                    "strategy": "逢高做空或持币观望为主"
                },
                "盘整趋势": {
                    "definition": "价格在一定区间内波动，没有明确的向上或向下方向",
                    "characteristics": ["横向震荡", "区间波动", "方向不明"],
                    "strategy": "高抛低吸或等待趋势突破为主"
                }
            },
            "analysis_tools": {
                "趋势线": {
                    "上升趋势线": "连接两个或多个关键的低点",
                    "下降趋势线": "连接两个或多个关键的高点",
                    "作用": "动态的支撑位或阻力位"
                },
                "K线形态": {
                    "反转形态": ["锤子线", "看涨吞没", "黄昏之星"],
                    "持续形态": ["上升三法", "旗形", "三角形"]
                },
                "经典图表形态": {
                    "反转形态": ["头肩顶/底", "双重顶/底", "V形反转"],
                    "持续形态": ["三角形", "旗形", "矩形"]
                }
            }
        }
    
    def _get_technical_indicators_knowledge(self) -> Dict[str, Any]:
        """技术指标知识"""
        return {
            "trend_indicators": {
                "移动平均线": {
                    "SMA": "简单移动平均线，一定周期内收盘价的算术平均值",
                    "EMA": "指数移动平均线，对近期价格给予更高权重",
                    "应用": "MA方向指示趋势方向，金叉死叉判断买卖信号"
                },
                "MACD": {
                    "计算": "基于12日和26日EMA的差值，再进行平滑处理",
                    "信号": "DIF上穿DEA形成金叉，顶背离和底背离预警反转"
                }
            },
            "momentum_indicators": {
                "RSI": {
                    "计算": "比较一定时期内上涨和下跌的幅度来测量动能",
                    "应用": "高于70-80为超买区，低于20-30为超卖区"
                },
                "KDJ": {
                    "计算": "基于统计学原理，通过当前收盘价在近期价格区间的相对位置",
                    "应用": "K线在80以上为超买，20以下为超卖"
                }
            },
            "volatility_indicators": {
                "布林带": {
                    "计算": "中轨为20日SMA，上下轨根据标准差计算",
                    "应用": "通道收窄预示大行情，价格触及上下轨为阻力支撑"
                }
            }
        }
    
    def _get_risk_assessment_knowledge(self) -> Dict[str, Any]:
        """风险评估知识"""
        return {
            "risk_management": {
                "止损设定": {
                    "技术位止损": "设在关键支撑位下方或阻力位上方",
                    "百分比止损": "设定固定的亏损比例，如本金的2%",
                    "波动性止损": "使用ATR指标设定适应市场波动的止损位"
                },
                "止盈设定": {
                    "目标位止盈": "设在下一个关键阻力位或支撑位",
                    "形态目标位": "根据图表形态测算目标价位",
                    "移动止盈": "随价格向有利方向移动，不断调高止损位"
                },
                "仓位管理": {
                    "公式": "仓位数量 = (总资金 × 单次可接受风险百分比) / (入场价 - 止损价)",
                    "原则": "每次交易风险不超过总资金的2-3%"
                },
                "风险回报比": {
                    "定义": "(止盈价 - 入场价) / (入场价 - 止损价)",
                    "标准": "潜在回报至少是潜在风险的2倍或3倍以上"
                }
            }
        }
    
    def _get_valuation_methods_knowledge(self) -> Dict[str, Any]:
        """估值方法知识"""
        return {
            "DCF估值": {
                "原理": "现金流折现模型，基于未来现金流的现值",
                "步骤": ["预测自由现金流", "计算终值", "折现计算", "敏感性分析"],
                "适用": "现金流稳定、可预测的成熟企业"
            },
            "相对估值": {
                "PE估值": "市盈率估值，适用于盈利稳定的企业",
                "PB估值": "市净率估值，适用于资产密集型企业",
                "PS估值": "市销率估值，适用于高成长但暂时亏损的企业",
                "EV/EBITDA": "企业价值倍数，适用于资本密集型企业"
            },
            "资产估值": {
                "账面价值": "基于资产负债表的净资产价值",
                "重置成本": "重新建设同等规模企业所需成本",
                "清算价值": "企业清算时资产的变现价值"
            }
        }
    
    def _get_data_sources_knowledge(self) -> Dict[str, Any]:
        """数据源知识"""
        return {
            "企业财务数据": {
                "官方渠道": ["上交所", "深交所", "巨潮资讯网", "公司官网"],
                "商业数据库": ["Wind", "Choice", "iFinD", "Bloomberg"],
                "开源工具": ["AKShare", "TuShare", "yfinance"]
            },
            "宏观经济数据": {
                "官方机构": ["国家统计局", "央行", "外管局", "发改委"],
                "国际机构": ["IMF", "世界银行", "OECD", "各国央行"],
                "商业服务": ["CEIC", "彭博", "路透", "万得"]
            },
            "行业数据": {
                "行业协会": ["各行业协会年报", "行业统计数据"],
                "研究机构": ["券商研报", "咨询公司报告", "智库研究"],
                "政府部门": ["工信部", "商务部", "各部委统计数据"]
            }
        }
    
    def _get_industry_analysis_knowledge(self) -> Dict[str, Any]:
        """行业分析知识"""
        return {
            "分析框架": {
                "波特五力模型": ["供应商议价能力", "买方议价能力", "潜在进入者威胁", "替代品威胁", "行业内竞争"],
                "生命周期分析": ["导入期", "成长期", "成熟期", "衰退期"],
                "价值链分析": ["上游", "中游", "下游", "产业链整合"]
            },
            "关键指标": {
                "市场规模": "行业总收入、总产值",
                "增长率": "年复合增长率CAGR",
                "集中度": "CR4、CR8、HHI指数",
                "盈利能力": "行业平均毛利率、净利率"
            }
        }
    
    def _get_macro_analysis_knowledge(self) -> Dict[str, Any]:
        """宏观分析知识"""
        return {
            "核心指标": {
                "GDP": {"含义": "国内生产总值", "影响": "经济增长的核心指标"},
                "CPI": {"含义": "消费者价格指数", "影响": "通胀水平的主要衡量"},
                "PMI": {"含义": "采购经理指数", "影响": "经济景气度的先行指标"},
                "利率": {"含义": "货币政策工具", "影响": "资金成本和流动性"},
                "汇率": {"含义": "货币相对价值", "影响": "进出口和资本流动"}
            },
            "分析框架": {
                "经济周期": ["复苏", "繁荣", "衰退", "萧条"],
                "货币政策": ["宽松", "中性", "紧缩"],
                "财政政策": ["积极", "稳健", "紧缩"],
                "政策传导": ["货币政策传导机制", "财政政策乘数效应"]
            }
        }
    
    def get_knowledge(self, category: str, subcategory: Optional[str] = None) -> Dict[str, Any]:
        """获取指定类别的知识"""
        try:
            if category not in self.knowledge_base:
                return {}
            
            knowledge = self.knowledge_base[category]
            
            if subcategory and subcategory in knowledge:
                return knowledge[subcategory]
            
            return knowledge
            
        except Exception as e:
            self.logger.error(f"获取知识失败: {e}")
            return {}
    
    def search_knowledge(self, query: str) -> List[Dict[str, Any]]:
        """搜索相关知识"""
        try:
            results = []
            query_lower = query.lower()
            
            for category, content in self.knowledge_base.items():
                if self._match_content(content, query_lower):
                    results.append({
                        'category': category,
                        'content': content,
                        'relevance': self._calculate_relevance(content, query_lower)
                    })
            
            # 按相关性排序
            results.sort(key=lambda x: x['relevance'], reverse=True)
            return results[:5]  # 返回前5个最相关的结果
            
        except Exception as e:
            self.logger.error(f"搜索知识失败: {e}")
            return []
    
    def _match_content(self, content: Any, query: str) -> bool:
        """匹配内容"""
        if isinstance(content, str):
            return query in content.lower()
        elif isinstance(content, dict):
            return any(self._match_content(v, query) for v in content.values())
        elif isinstance(content, list):
            return any(self._match_content(item, query) for item in content)
        return False
    
    def _calculate_relevance(self, content: Any, query: str) -> float:
        """计算相关性分数"""
        # 简化的相关性计算
        content_str = str(content).lower()
        query_words = query.split()
        
        score = 0
        for word in query_words:
            score += content_str.count(word)
        
        return score / len(query_words) if query_words else 0
