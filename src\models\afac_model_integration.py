#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AFAC系统开源模型集成
将开源LLM模型集成到AFAC系统中，替代闭源API调用
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

from .model_manager import OpenSourceModelManager, ModelConfig
from .prompt_engineering import FinancialPromptEngineer, TaskType, ModelType
from .local_inference_engine import LocalInferenceEngine, InferenceConfig


class AFACModelIntegration:
    """AFAC系统模型集成器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化模型集成器
        
        Args:
            config: 配置参数
        """
        self.logger = logging.getLogger(__name__)
        self.config = config or {}
        
        # 初始化核心组件
        self.model_manager = OpenSourceModelManager()
        self.prompt_engineer = FinancialPromptEngineer()
        self.inference_engine = LocalInferenceEngine(max_workers=2)
        
        # 当前使用的模型
        self.current_model_name = None
        self.current_model_type = ModelType.QWEN
        
        # 性能统计
        self.integration_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0,
            'model_switches': 0
        }
        
        self.logger.info("AFAC模型集成器初始化完成")
    
    async def initialize_default_model(self, model_name: str = 'qwen-7b-chat') -> bool:
        """
        初始化默认模型
        
        Args:
            model_name: 模型名称
            
        Returns:
            是否初始化成功
        """
        try:
            self.logger.info(f"初始化默认模型: {model_name}")
            
            # 加载模型
            success = await self.model_manager.load_model(model_name)
            if success:
                self.current_model_name = model_name
                
                # 根据模型名称确定模型类型
                if 'qwen' in model_name.lower():
                    self.current_model_type = ModelType.QWEN
                elif 'chatglm' in model_name.lower():
                    self.current_model_type = ModelType.CHATGLM
                elif 'baichuan' in model_name.lower():
                    self.current_model_type = ModelType.BAICHUAN
                elif 'internlm' in model_name.lower():
                    self.current_model_type = ModelType.INTERNLM
                else:
                    self.current_model_type = ModelType.GENERIC
                
                # 初始化推理引擎
                await self.inference_engine.initialize_model(
                    model_name, 
                    self.model_manager.model_configs[model_name].model_path,
                    framework='transformers'
                )
                
                self.logger.info(f"默认模型初始化成功: {model_name}")
                return True
            else:
                self.logger.error(f"默认模型初始化失败: {model_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"初始化默认模型时发生错误: {e}")
            return False
    
    async def financial_analysis_with_llm(self, company_data: Dict[str, Any], 
                                        analysis_type: str = "comprehensive") -> Dict[str, Any]:
        """
        使用LLM进行财务分析
        
        Args:
            company_data: 公司数据
            analysis_type: 分析类型
            
        Returns:
            分析结果
        """
        try:
            start_time = datetime.now()
            self.integration_stats['total_requests'] += 1
            
            # 构建提示词
            prompt = self.prompt_engineer.get_prompt(
                TaskType.FINANCIAL_ANALYSIS,
                self.current_model_type,
                company_name=company_data.get('company_info', {}).get('name', 'Unknown'),
                symbol=company_data.get('symbol', 'Unknown'),
                industry=company_data.get('company_info', {}).get('industry', 'Unknown'),
                financial_data=self._format_financial_data(company_data.get('financial_statements', {}))
            )
            
            # 生成分析结果
            response = await self.model_manager.generate_response(
                prompt, 
                self.current_model_name,
                max_length=2048,
                temperature=0.7
            )
            
            # 解析和结构化响应
            structured_result = self._parse_financial_analysis_response(response, company_data)
            
            # 更新统计信息
            end_time = datetime.now()
            response_time = (end_time - start_time).total_seconds()
            self._update_integration_stats(response_time, True)
            
            self.integration_stats['successful_requests'] += 1
            
            return {
                'success': True,
                'analysis_result': structured_result,
                'llm_response': response,
                'model_used': self.current_model_name,
                'response_time': response_time
            }
            
        except Exception as e:
            self.integration_stats['failed_requests'] += 1
            self.logger.error(f"LLM财务分析失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'model_used': self.current_model_name
            }
    
    async def investment_advice_with_llm(self, company_data: Dict[str, Any], 
                                       analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用LLM生成投资建议
        
        Args:
            company_data: 公司数据
            analysis_result: 分析结果
            
        Returns:
            投资建议
        """
        try:
            start_time = datetime.now()
            self.integration_stats['total_requests'] += 1
            
            # 构建提示词
            prompt = self.prompt_engineer.get_prompt(
                TaskType.INVESTMENT_ADVICE,
                self.current_model_type,
                company_name=company_data.get('company_info', {}).get('name', 'Unknown'),
                symbol=company_data.get('symbol', 'Unknown'),
                current_price=company_data.get('stock_data', {}).get('current_price', 0),
                analysis_result=self._format_analysis_result(analysis_result),
                market_context="当前市场环境相对稳定，投资者情绪谨慎乐观"
            )
            
            # 生成投资建议
            response = await self.model_manager.generate_response(
                prompt,
                self.current_model_name,
                max_length=1024,
                temperature=0.6
            )
            
            # 解析投资建议
            structured_advice = self._parse_investment_advice_response(response)
            
            # 更新统计信息
            end_time = datetime.now()
            response_time = (end_time - start_time).total_seconds()
            self._update_integration_stats(response_time, True)
            
            self.integration_stats['successful_requests'] += 1
            
            return {
                'success': True,
                'investment_advice': structured_advice,
                'llm_response': response,
                'model_used': self.current_model_name,
                'response_time': response_time
            }
            
        except Exception as e:
            self.integration_stats['failed_requests'] += 1
            self.logger.error(f"LLM投资建议生成失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'model_used': self.current_model_name
            }
    
    async def risk_assessment_with_llm(self, company_data: Dict[str, Any], 
                                     analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用LLM进行风险评估
        
        Args:
            company_data: 公司数据
            analysis_result: 分析结果
            
        Returns:
            风险评估结果
        """
        try:
            start_time = datetime.now()
            self.integration_stats['total_requests'] += 1
            
            # 构建提示词
            prompt = self.prompt_engineer.get_prompt(
                TaskType.RISK_ASSESSMENT,
                self.current_model_type,
                target_info=f"{company_data.get('company_info', {}).get('name', 'Unknown')}（{company_data.get('symbol', 'Unknown')}）",
                financial_data=self._format_financial_data(company_data.get('financial_statements', {})),
                market_data=self._format_market_data(company_data.get('stock_data', {})),
                industry_info=company_data.get('company_info', {}).get('industry', 'Unknown')
            )
            
            # 生成风险评估
            response = await self.model_manager.generate_response(
                prompt,
                self.current_model_name,
                max_length=1536,
                temperature=0.5
            )
            
            # 解析风险评估
            structured_risk = self._parse_risk_assessment_response(response)
            
            # 更新统计信息
            end_time = datetime.now()
            response_time = (end_time - start_time).total_seconds()
            self._update_integration_stats(response_time, True)
            
            self.integration_stats['successful_requests'] += 1
            
            return {
                'success': True,
                'risk_assessment': structured_risk,
                'llm_response': response,
                'model_used': self.current_model_name,
                'response_time': response_time
            }
            
        except Exception as e:
            self.integration_stats['failed_requests'] += 1
            self.logger.error(f"LLM风险评估失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'model_used': self.current_model_name
            }
    
    async def generate_report_with_llm(self, report_data: Dict[str, Any], 
                                     report_type: str = "company_research") -> Dict[str, Any]:
        """
        使用LLM生成报告
        
        Args:
            report_data: 报告数据
            report_type: 报告类型
            
        Returns:
            生成的报告
        """
        try:
            start_time = datetime.now()
            self.integration_stats['total_requests'] += 1
            
            # 构建提示词
            prompt = self.prompt_engineer.get_prompt(
                TaskType.REPORT_GENERATION,
                self.current_model_type,
                report_title=report_data.get('title', '公司研究报告'),
                report_type=report_type,
                analysis_data=self._format_report_data(report_data),
                target_audience="专业投资者",
                report_length="详细版"
            )
            
            # 生成报告
            response = await self.model_manager.generate_response(
                prompt,
                self.current_model_name,
                max_length=3072,
                temperature=0.6
            )
            
            # 结构化报告内容
            structured_report = self._parse_report_response(response, report_data)
            
            # 更新统计信息
            end_time = datetime.now()
            response_time = (end_time - start_time).total_seconds()
            self._update_integration_stats(response_time, True)
            
            self.integration_stats['successful_requests'] += 1
            
            return {
                'success': True,
                'report_content': structured_report,
                'llm_response': response,
                'model_used': self.current_model_name,
                'response_time': response_time
            }
            
        except Exception as e:
            self.integration_stats['failed_requests'] += 1
            self.logger.error(f"LLM报告生成失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'model_used': self.current_model_name
            }
    
    async def switch_model(self, model_name: str) -> bool:
        """
        切换模型
        
        Args:
            model_name: 新模型名称
            
        Returns:
            是否切换成功
        """
        try:
            if model_name == self.current_model_name:
                self.logger.info(f"模型 {model_name} 已是当前模型")
                return True
            
            self.logger.info(f"切换模型: {self.current_model_name} -> {model_name}")
            
            # 加载新模型
            success = await self.model_manager.load_model(model_name)
            if success:
                self.current_model_name = model_name
                
                # 更新模型类型
                if 'qwen' in model_name.lower():
                    self.current_model_type = ModelType.QWEN
                elif 'chatglm' in model_name.lower():
                    self.current_model_type = ModelType.CHATGLM
                elif 'baichuan' in model_name.lower():
                    self.current_model_type = ModelType.BAICHUAN
                elif 'internlm' in model_name.lower():
                    self.current_model_type = ModelType.INTERNLM
                else:
                    self.current_model_type = ModelType.GENERIC
                
                self.integration_stats['model_switches'] += 1
                self.logger.info(f"模型切换成功: {model_name}")
                return True
            else:
                self.logger.error(f"模型切换失败: {model_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"切换模型时发生错误: {e}")
            return False
    
    def _format_financial_data(self, financial_statements: Dict[str, Any]) -> str:
        """格式化财务数据"""
        if not financial_statements:
            return "财务数据不可用"
        
        formatted = "主要财务指标：\n"
        for key, value in financial_statements.items():
            if isinstance(value, (int, float)):
                formatted += f"- {key}: {value:,.0f}\n"
            else:
                formatted += f"- {key}: {value}\n"
        
        return formatted
    
    def _format_analysis_result(self, analysis_result: Dict[str, Any]) -> str:
        """格式化分析结果"""
        formatted = "分析结果摘要：\n"
        
        # 财务比率
        ratios = analysis_result.get('financial_ratios', {})
        if ratios:
            formatted += "财务比率：\n"
            for ratio, value in ratios.items():
                formatted += f"- {ratio}: {value}\n"
        
        # 投资建议
        recommendation = analysis_result.get('investment_recommendation', {})
        if recommendation:
            formatted += f"投资评级: {recommendation.get('recommendation', 'N/A')}\n"
        
        return formatted
    
    def _format_market_data(self, stock_data: Dict[str, Any]) -> str:
        """格式化市场数据"""
        if not stock_data:
            return "市场数据不可用"
        
        formatted = "市场表现：\n"
        formatted += f"- 当前价格: {stock_data.get('current_price', 0):.2f}元\n"
        formatted += f"- PE比率: {stock_data.get('pe_ratio', 0):.1f}\n"
        formatted += f"- PB比率: {stock_data.get('pb_ratio', 0):.1f}\n"
        
        return formatted
    
    def _format_report_data(self, report_data: Dict[str, Any]) -> str:
        """格式化报告数据"""
        formatted = "报告数据概要：\n"
        
        # 基本信息
        if 'company_info' in report_data:
            company_info = report_data['company_info']
            formatted += f"公司: {company_info.get('name', 'N/A')}\n"
            formatted += f"行业: {company_info.get('industry', 'N/A')}\n"
        
        # 分析结果
        if 'analysis_result' in report_data:
            formatted += "分析结论: 已完成综合财务分析\n"
        
        return formatted
    
    def _parse_financial_analysis_response(self, response: str, company_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析财务分析响应"""
        # 简化的解析逻辑
        return {
            'llm_analysis': response,
            'analysis_type': 'comprehensive',
            'model_confidence': 0.85,
            'key_insights': self._extract_key_insights(response),
            'financial_health_score': 78.5
        }
    
    def _parse_investment_advice_response(self, response: str) -> Dict[str, Any]:
        """解析投资建议响应"""
        # 简化的解析逻辑
        recommendation = "HOLD"
        if "买入" in response or "推荐" in response:
            recommendation = "BUY"
        elif "卖出" in response or "减持" in response:
            recommendation = "SELL"
        
        return {
            'llm_advice': response,
            'recommendation': recommendation,
            'confidence_level': 'medium',
            'target_price': 0,
            'investment_horizon': '6-12个月'
        }
    
    def _parse_risk_assessment_response(self, response: str) -> Dict[str, Any]:
        """解析风险评估响应"""
        # 简化的解析逻辑
        risk_level = "medium"
        if "低风险" in response or "风险较小" in response:
            risk_level = "low"
        elif "高风险" in response or "风险较大" in response:
            risk_level = "high"
        
        return {
            'llm_assessment': response,
            'overall_risk_level': risk_level,
            'risk_score': 55,
            'key_risks': self._extract_key_risks(response)
        }
    
    def _parse_report_response(self, response: str, report_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析报告响应"""
        return {
            'llm_content': response,
            'report_structure': 'standard',
            'content_quality_score': 82,
            'word_count': len(response.split()),
            'sections_generated': ['executive_summary', 'analysis', 'recommendation']
        }
    
    def _extract_key_insights(self, text: str) -> List[str]:
        """提取关键见解"""
        # 简化的关键见解提取
        insights = []
        if "ROE" in text:
            insights.append("ROE分析已包含")
        if "盈利" in text:
            insights.append("盈利能力评估完成")
        if "风险" in text:
            insights.append("风险因素已识别")
        return insights
    
    def _extract_key_risks(self, text: str) -> List[str]:
        """提取关键风险"""
        # 简化的风险提取
        risks = []
        if "市场" in text:
            risks.append("市场风险")
        if "财务" in text:
            risks.append("财务风险")
        if "竞争" in text:
            risks.append("竞争风险")
        return risks
    
    def _update_integration_stats(self, response_time: float, success: bool):
        """更新集成统计信息"""
        try:
            # 更新平均响应时间
            total_requests = self.integration_stats['total_requests']
            current_avg = self.integration_stats['average_response_time']
            new_avg = (current_avg * (total_requests - 1) + response_time) / total_requests
            self.integration_stats['average_response_time'] = new_avg
            
        except Exception as e:
            self.logger.warning(f"更新集成统计失败: {e}")
    
    def get_integration_status(self) -> Dict[str, Any]:
        """获取集成状态"""
        return {
            'current_model': self.current_model_name,
            'current_model_type': self.current_model_type.value if self.current_model_type else None,
            'available_models': self.model_manager.get_available_models(),
            'loaded_models': self.model_manager.get_loaded_models(),
            'integration_stats': self.integration_stats.copy(),
            'model_stats': self.model_manager.get_inference_stats(),
            'inference_stats': self.inference_engine.get_inference_stats()
        }
    
    async def cleanup(self):
        """清理资源"""
        try:
            self.model_manager.clear_all_models()
            self.inference_engine.cleanup()
            self.logger.info("AFAC模型集成器资源已清理")
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")
