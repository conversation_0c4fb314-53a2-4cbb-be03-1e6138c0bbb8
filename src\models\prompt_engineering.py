#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
针对开源模型的提示词工程系统
优化不同开源模型的提示词模板和策略
"""

import json
import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum


class TaskType(Enum):
    """任务类型枚举"""
    FINANCIAL_ANALYSIS = "financial_analysis"
    INVESTMENT_ADVICE = "investment_advice"
    RISK_ASSESSMENT = "risk_assessment"
    MARKET_ANALYSIS = "market_analysis"
    COMPANY_RESEARCH = "company_research"
    INDUSTRY_ANALYSIS = "industry_analysis"
    MACRO_ANALYSIS = "macro_analysis"
    REPORT_GENERATION = "report_generation"


class ModelType(Enum):
    """模型类型枚举"""
    QWEN = "qwen"
    CHATGLM = "chatglm"
    BAICHUAN = "baichuan"
    INTERNLM = "internlm"
    LLAMA = "llama"
    GENERIC = "generic"


@dataclass
class PromptTemplate:
    """提示词模板类"""
    task_type: TaskType
    model_type: ModelType
    system_prompt: str
    user_template: str
    few_shot_examples: List[Dict[str, str]]
    constraints: List[str]
    output_format: str


class FinancialPromptEngineer:
    """金融领域提示词工程师"""
    
    def __init__(self):
        """初始化提示词工程师"""
        self.logger = logging.getLogger(__name__)
        self.templates = {}
        self._initialize_templates()
    
    def _initialize_templates(self):
        """初始化提示词模板"""
        # 财务分析模板
        self._init_financial_analysis_templates()
        # 投资建议模板
        self._init_investment_advice_templates()
        # 风险评估模板
        self._init_risk_assessment_templates()
        # 报告生成模板
        self._init_report_generation_templates()
    
    def _init_financial_analysis_templates(self):
        """初始化财务分析模板"""
        # Qwen模型的财务分析模板
        qwen_financial = PromptTemplate(
            task_type=TaskType.FINANCIAL_ANALYSIS,
            model_type=ModelType.QWEN,
            system_prompt="""你是一位专业的金融分析师，具有CFA资格和丰富的财务分析经验。
你需要基于提供的财务数据进行专业、客观的分析，使用标准的财务分析方法。

分析要求：
1. 使用杜邦分析法分解ROE
2. 计算并解释关键财务比率
3. 进行同业对比分析
4. 提供专业的投资建议

请确保分析结果准确、逻辑清晰、结论有据。""",
            user_template="""请分析以下公司的财务状况：

公司信息：
- 公司名称：{company_name}
- 股票代码：{symbol}
- 行业：{industry}

财务数据：
{financial_data}

请从以下维度进行分析：
1. 盈利能力分析（ROE、ROA、净利润率等）
2. 偿债能力分析（流动比率、资产负债率等）
3. 运营效率分析（资产周转率、存货周转率等）
4. 成长性分析（收入增长率、利润增长率等）

请提供详细的分析报告。""",
            few_shot_examples=[
                {
                    "input": "分析某银行的ROE为15.2%，净利润率为25.8%，资产周转率为0.59",
                    "output": "基于杜邦分析法，该银行ROE=净利润率×资产周转率×权益乘数=25.8%×0.59×1.0=15.2%。净利润率25.8%显示银行具有良好的盈利能力，资产周转率0.59符合银行业特点，整体ROE水平优秀。"
                }
            ],
            constraints=[
                "必须使用专业的财务术语",
                "分析结论必须有数据支撑",
                "需要提供具体的数值计算",
                "避免主观臆断"
            ],
            output_format="结构化的财务分析报告，包含数据分析、比率计算、结论总结"
        )
        
        # ChatGLM模型的财务分析模板
        chatglm_financial = PromptTemplate(
            task_type=TaskType.FINANCIAL_ANALYSIS,
            model_type=ModelType.CHATGLM,
            system_prompt="""作为专业金融分析师，请基于财务数据进行客观分析。

核心要求：
- 运用杜邦分析等专业方法
- 提供量化的分析结果
- 保持分析的客观性和专业性""",
            user_template="""财务分析任务：

【公司基本信息】
公司：{company_name}（{symbol}）
行业：{industry}

【财务数据】
{financial_data}

【分析要求】
请进行全面的财务分析，包括：
• 盈利能力评估
• 偿债能力评估  
• 运营效率评估
• 发展能力评估

请提供专业的分析意见。""",
            few_shot_examples=[],
            constraints=[
                "使用标准财务分析框架",
                "提供量化分析结果",
                "结论基于数据事实"
            ],
            output_format="分点式财务分析报告"
        )
        
        self.templates[(TaskType.FINANCIAL_ANALYSIS, ModelType.QWEN)] = qwen_financial
        self.templates[(TaskType.FINANCIAL_ANALYSIS, ModelType.CHATGLM)] = chatglm_financial
    
    def _init_investment_advice_templates(self):
        """初始化投资建议模板"""
        qwen_investment = PromptTemplate(
            task_type=TaskType.INVESTMENT_ADVICE,
            model_type=ModelType.QWEN,
            system_prompt="""你是一位资深的投资顾问，需要基于财务分析结果提供专业的投资建议。

投资建议要求：
1. 明确的投资评级（强烈买入/买入/持有/减持/卖出）
2. 合理的目标价格区间
3. 详细的投资逻辑
4. 全面的风险提示
5. 适当的投资期限建议

请确保建议客观、专业、负责任。""",
            user_template="""基于以下分析结果，请提供投资建议：

【公司信息】
公司：{company_name}（{symbol}）
当前股价：{current_price}元

【财务分析结果】
{analysis_result}

【市场环境】
{market_context}

请提供：
1. 投资评级及理由
2. 目标价格及计算依据
3. 关键投资亮点
4. 主要风险因素
5. 投资期限建议""",
            few_shot_examples=[
                {
                    "input": "某公司ROE 18%，PE 12倍，行业平均PE 15倍，业绩增长稳定",
                    "output": "投资评级：买入。目标价：基于15倍PE估值，目标价XX元。投资亮点：ROE优秀，估值偏低。风险：行业竞争加剧。建议持有期：6-12个月。"
                }
            ],
            constraints=[
                "必须提供明确的投资评级",
                "目标价格需要计算依据",
                "风险提示必须全面",
                "避免过度乐观或悲观"
            ],
            output_format="标准投资建议报告格式"
        )
        
        self.templates[(TaskType.INVESTMENT_ADVICE, ModelType.QWEN)] = qwen_investment
    
    def _init_risk_assessment_templates(self):
        """初始化风险评估模板"""
        qwen_risk = PromptTemplate(
            task_type=TaskType.RISK_ASSESSMENT,
            model_type=ModelType.QWEN,
            system_prompt="""你是一位专业的风险管理专家，需要对投资标的进行全面的风险评估。

风险评估框架：
1. 财务风险（流动性、偿债能力、盈利稳定性）
2. 业务风险（行业风险、竞争风险、经营风险）
3. 市场风险（系统性风险、估值风险）
4. 其他风险（政策风险、声誉风险等）

请提供量化的风险评分和具体的风险缓解建议。""",
            user_template="""请对以下投资标的进行风险评估：

【标的信息】
{target_info}

【财务数据】
{financial_data}

【市场数据】
{market_data}

【行业信息】
{industry_info}

请评估：
1. 各类风险的具体表现
2. 风险等级评分（1-10分）
3. 风险缓解措施
4. 整体风险结论""",
            few_shot_examples=[],
            constraints=[
                "必须覆盖所有主要风险类型",
                "提供量化的风险评分",
                "给出具体的缓解建议",
                "保持客观中性的评估"
            ],
            output_format="结构化风险评估报告"
        )
        
        self.templates[(TaskType.RISK_ASSESSMENT, ModelType.QWEN)] = qwen_risk
    
    def _init_report_generation_templates(self):
        """初始化报告生成模板"""
        qwen_report = PromptTemplate(
            task_type=TaskType.REPORT_GENERATION,
            model_type=ModelType.QWEN,
            system_prompt="""你是一位专业的金融研究报告撰写专家，需要基于分析结果生成高质量的研究报告。

报告要求：
1. 结构清晰，逻辑严密
2. 数据准确，分析深入
3. 语言专业，表达精确
4. 结论明确，建议具体
5. 符合行业标准格式

请确保报告的专业性和可读性。""",
            user_template="""请基于以下信息生成{report_type}研究报告：

【报告主题】
{report_title}

【分析数据】
{analysis_data}

【报告要求】
- 报告类型：{report_type}
- 目标读者：{target_audience}
- 报告长度：{report_length}

请生成包含以下部分的报告：
1. 执行摘要
2. 公司/行业概况
3. 财务分析
4. 投资建议
5. 风险提示""",
            few_shot_examples=[],
            constraints=[
                "遵循标准报告格式",
                "确保内容的逻辑性",
                "使用专业术语",
                "提供明确的结论"
            ],
            output_format="标准金融研究报告格式"
        )
        
        self.templates[(TaskType.REPORT_GENERATION, ModelType.QWEN)] = qwen_report
    
    def get_prompt(self, task_type: TaskType, model_type: ModelType, **kwargs) -> str:
        """
        获取优化后的提示词
        
        Args:
            task_type: 任务类型
            model_type: 模型类型
            **kwargs: 模板参数
            
        Returns:
            完整的提示词
        """
        try:
            # 获取模板
            template = self.templates.get((task_type, model_type))
            if not template:
                # 如果没有特定模型的模板，尝试使用通用模板
                template = self.templates.get((task_type, ModelType.GENERIC))
                if not template:
                    self.logger.warning(f"未找到模板: {task_type}, {model_type}")
                    return self._get_fallback_prompt(task_type, **kwargs)
            
            # 构建完整提示词
            prompt_parts = []
            
            # 系统提示
            if template.system_prompt:
                prompt_parts.append(f"<|system|>\n{template.system_prompt}\n<|end|>")
            
            # Few-shot示例
            for example in template.few_shot_examples:
                prompt_parts.append(f"<|user|>\n{example['input']}\n<|end|>")
                prompt_parts.append(f"<|assistant|>\n{example['output']}\n<|end|>")
            
            # 用户输入
            user_prompt = template.user_template.format(**kwargs)
            prompt_parts.append(f"<|user|>\n{user_prompt}\n<|end|>")
            prompt_parts.append("<|assistant|>")
            
            return "\n\n".join(prompt_parts)
            
        except Exception as e:
            self.logger.error(f"生成提示词失败: {e}")
            return self._get_fallback_prompt(task_type, **kwargs)
    
    def _get_fallback_prompt(self, task_type: TaskType, **kwargs) -> str:
        """获取备用提示词"""
        fallback_prompts = {
            TaskType.FINANCIAL_ANALYSIS: "请对提供的财务数据进行专业分析，包括盈利能力、偿债能力、运营效率等方面。",
            TaskType.INVESTMENT_ADVICE: "请基于分析结果提供投资建议，包括评级、目标价、投资逻辑和风险提示。",
            TaskType.RISK_ASSESSMENT: "请对投资标的进行全面的风险评估，包括财务风险、业务风险和市场风险。",
            TaskType.REPORT_GENERATION: "请生成专业的金融研究报告，包含分析结论和投资建议。"
        }
        
        base_prompt = fallback_prompts.get(task_type, "请提供专业的金融分析。")
        
        # 添加数据信息
        if kwargs:
            data_info = "\n\n相关数据：\n"
            for key, value in kwargs.items():
                data_info += f"{key}: {value}\n"
            base_prompt += data_info
        
        return base_prompt
    
    def optimize_for_model(self, prompt: str, model_type: ModelType) -> str:
        """
        针对特定模型优化提示词
        
        Args:
            prompt: 原始提示词
            model_type: 模型类型
            
        Returns:
            优化后的提示词
        """
        try:
            if model_type == ModelType.QWEN:
                return self._optimize_for_qwen(prompt)
            elif model_type == ModelType.CHATGLM:
                return self._optimize_for_chatglm(prompt)
            elif model_type == ModelType.BAICHUAN:
                return self._optimize_for_baichuan(prompt)
            elif model_type == ModelType.INTERNLM:
                return self._optimize_for_internlm(prompt)
            else:
                return prompt
                
        except Exception as e:
            self.logger.error(f"优化提示词失败: {e}")
            return prompt
    
    def _optimize_for_qwen(self, prompt: str) -> str:
        """针对Qwen模型优化"""
        # Qwen模型喜欢结构化的输入
        if not prompt.startswith("<|"):
            optimized = f"<|user|>\n{prompt}\n<|end|>\n<|assistant|>"
            return optimized
        return prompt
    
    def _optimize_for_chatglm(self, prompt: str) -> str:
        """针对ChatGLM模型优化"""
        # ChatGLM使用特定的对话格式
        if not prompt.startswith("[Round"):
            optimized = f"[Round 1]\n\n问：{prompt}\n\n答："
            return optimized
        return prompt
    
    def _optimize_for_baichuan(self, prompt: str) -> str:
        """针对Baichuan模型优化"""
        # Baichuan模型的优化策略
        return prompt
    
    def _optimize_for_internlm(self, prompt: str) -> str:
        """针对InternLM模型优化"""
        # InternLM模型的优化策略
        return prompt
    
    def add_template(self, template: PromptTemplate):
        """添加新的提示词模板"""
        key = (template.task_type, template.model_type)
        self.templates[key] = template
        self.logger.info(f"已添加模板: {template.task_type.value} - {template.model_type.value}")
    
    def get_available_templates(self) -> List[Dict[str, str]]:
        """获取可用的模板列表"""
        templates = []
        for (task_type, model_type), template in self.templates.items():
            templates.append({
                'task_type': task_type.value,
                'model_type': model_type.value,
                'description': f"{task_type.value} template for {model_type.value}"
            })
        return templates
    
    def validate_prompt(self, prompt: str) -> Dict[str, Any]:
        """验证提示词质量"""
        validation_result = {
            'is_valid': True,
            'issues': [],
            'suggestions': [],
            'score': 0
        }
        
        # 检查长度
        if len(prompt) < 50:
            validation_result['issues'].append("提示词过短")
            validation_result['score'] -= 20
        elif len(prompt) > 4000:
            validation_result['issues'].append("提示词过长")
            validation_result['score'] -= 10
        
        # 检查结构
        if "分析" not in prompt and "评估" not in prompt:
            validation_result['issues'].append("缺少明确的任务指令")
            validation_result['score'] -= 15
        
        # 检查专业术语
        financial_terms = ["ROE", "ROA", "财务", "投资", "风险", "估值"]
        if not any(term in prompt for term in financial_terms):
            validation_result['suggestions'].append("建议添加更多金融专业术语")
            validation_result['score'] -= 5
        
        # 计算最终分数
        validation_result['score'] = max(0, 100 + validation_result['score'])
        validation_result['is_valid'] = validation_result['score'] >= 60
        
        return validation_result
