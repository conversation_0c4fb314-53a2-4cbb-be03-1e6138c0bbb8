#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析深度增强器
提升财务分析的专业性和深度，增加更多分析维度
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import math


class AnalysisDepth(Enum):
    """分析深度等级"""
    BASIC = "basic"           # 基础分析
    INTERMEDIATE = "intermediate"  # 中级分析
    ADVANCED = "advanced"     # 高级分析
    EXPERT = "expert"         # 专家级分析


@dataclass
class AnalysisResult:
    """分析结果"""
    analysis_type: str
    depth_level: AnalysisDepth
    score: float
    insights: List[str]
    recommendations: List[str]
    confidence: float
    data_quality: float


class AdvancedFinancialAnalyzer:
    """高级财务分析器"""
    
    def __init__(self):
        """初始化分析器"""
        self.logger = logging.getLogger(__name__)
        
        # 行业基准数据（模拟）
        self.industry_benchmarks = {
            '银行业': {
                'roe': 12.5, 'roa': 1.1, 'net_margin': 25.0,
                'equity_ratio': 8.5, 'npl_ratio': 1.5
            },
            '制造业': {
                'roe': 15.0, 'roa': 6.5, 'net_margin': 8.0,
                'current_ratio': 1.8, 'debt_ratio': 0.45
            },
            '科技业': {
                'roe': 18.0, 'roa': 12.0, 'net_margin': 15.0,
                'rd_ratio': 12.0, 'growth_rate': 25.0
            },
            '房地产': {
                'roe': 10.0, 'roa': 3.5, 'net_margin': 12.0,
                'debt_ratio': 0.75, 'turnover_ratio': 0.3
            }
        }
    
    async def perform_zscore_analysis(self, financial_data: Dict[str, Any]) -> AnalysisResult:
        """执行Z-Score破产风险分析"""
        try:
            # Altman Z-Score计算
            # Z = 1.2*X1 + 1.4*X2 + 3.3*X3 + 0.6*X4 + 1.0*X5
            
            total_assets = financial_data.get('total_assets', 0)
            if total_assets == 0:
                return AnalysisResult(
                    analysis_type="Z-Score分析",
                    depth_level=AnalysisDepth.ADVANCED,
                    score=0,
                    insights=["缺少总资产数据，无法计算Z-Score"],
                    recommendations=["补充完整的资产负债表数据"],
                    confidence=0.0,
                    data_quality=0.0
                )
            
            # X1: 营运资本/总资产
            current_assets = financial_data.get('current_assets', 0)
            current_liabilities = financial_data.get('current_liabilities', 0)
            working_capital = current_assets - current_liabilities
            x1 = working_capital / total_assets
            
            # X2: 留存收益/总资产
            retained_earnings = financial_data.get('retained_earnings', 
                                                 financial_data.get('total_equity', 0) * 0.6)  # 估算
            x2 = retained_earnings / total_assets
            
            # X3: 息税前利润/总资产
            ebit = financial_data.get('ebit', 
                                    financial_data.get('net_income', 0) * 1.3)  # 估算
            x3 = ebit / total_assets
            
            # X4: 股权市值/总负债
            market_cap = financial_data.get('market_cap', 0)
            total_liabilities = financial_data.get('total_liabilities', 
                                                 total_assets - financial_data.get('total_equity', 0))
            x4 = market_cap / max(total_liabilities, 1)
            
            # X5: 销售收入/总资产
            revenue = financial_data.get('revenue', 0)
            x5 = revenue / total_assets
            
            # 计算Z-Score
            z_score = 1.2*x1 + 1.4*x2 + 3.3*x3 + 0.6*x4 + 1.0*x5
            
            # 风险评估
            if z_score > 2.99:
                risk_level = "低风险"
                risk_desc = "财务状况健康，破产风险很低"
            elif z_score > 1.81:
                risk_level = "中等风险"
                risk_desc = "财务状况一般，需要关注"
            else:
                risk_level = "高风险"
                risk_desc = "财务状况较差，破产风险较高"
            
            insights = [
                f"Z-Score: {z_score:.2f}",
                f"风险等级: {risk_level}",
                f"营运资本比率: {x1:.3f}",
                f"留存收益比率: {x2:.3f}",
                f"资产收益率: {x3:.3f}",
                f"市值负债比: {x4:.3f}",
                f"资产周转率: {x5:.3f}"
            ]
            
            recommendations = []
            if z_score < 1.81:
                recommendations.extend([
                    "加强现金流管理",
                    "优化资本结构",
                    "提高盈利能力",
                    "降低财务杠杆"
                ])
            elif z_score < 2.99:
                recommendations.extend([
                    "保持财务稳健性",
                    "适度扩张业务",
                    "优化运营效率"
                ])
            else:
                recommendations.extend([
                    "可考虑适度增加杠杆",
                    "扩大市场份额",
                    "投资新项目"
                ])
            
            return AnalysisResult(
                analysis_type="Z-Score破产风险分析",
                depth_level=AnalysisDepth.ADVANCED,
                score=z_score,
                insights=insights,
                recommendations=recommendations,
                confidence=0.85,
                data_quality=0.8
            )
            
        except Exception as e:
            self.logger.error(f"Z-Score分析失败: {e}")
            return AnalysisResult(
                analysis_type="Z-Score分析",
                depth_level=AnalysisDepth.ADVANCED,
                score=0,
                insights=[f"分析失败: {str(e)}"],
                recommendations=["检查数据完整性"],
                confidence=0.0,
                data_quality=0.0
            )
    
    async def perform_industry_comparison(self, financial_data: Dict[str, Any], 
                                        industry: str) -> AnalysisResult:
        """执行行业对比分析"""
        try:
            benchmark = self.industry_benchmarks.get(industry, self.industry_benchmarks['制造业'])
            
            # 计算关键指标
            total_assets = financial_data.get('total_assets', 1)
            total_equity = financial_data.get('total_equity', 1)
            revenue = financial_data.get('revenue', 0)
            net_income = financial_data.get('net_income', 0)
            
            company_roe = (net_income / total_equity) * 100 if total_equity > 0 else 0
            company_roa = (net_income / total_assets) * 100 if total_assets > 0 else 0
            company_margin = (net_income / revenue) * 100 if revenue > 0 else 0
            
            # 对比分析
            roe_diff = company_roe - benchmark['roe']
            roa_diff = company_roa - benchmark['roa']
            margin_diff = company_margin - benchmark['net_margin']
            
            insights = [
                f"行业: {industry}",
                f"ROE: {company_roe:.1f}% (行业平均: {benchmark['roe']:.1f}%, 差异: {roe_diff:+.1f}%)",
                f"ROA: {company_roa:.1f}% (行业平均: {benchmark['roa']:.1f}%, 差异: {roa_diff:+.1f}%)",
                f"净利润率: {company_margin:.1f}% (行业平均: {benchmark['net_margin']:.1f}%, 差异: {margin_diff:+.1f}%)"
            ]
            
            # 综合评分
            score_components = []
            if roe_diff >= 0:
                score_components.append(min(100, 80 + roe_diff * 2))
            else:
                score_components.append(max(0, 80 + roe_diff * 2))
            
            if roa_diff >= 0:
                score_components.append(min(100, 80 + roa_diff * 3))
            else:
                score_components.append(max(0, 80 + roa_diff * 3))
            
            if margin_diff >= 0:
                score_components.append(min(100, 80 + margin_diff))
            else:
                score_components.append(max(0, 80 + margin_diff))
            
            overall_score = sum(score_components) / len(score_components)
            
            recommendations = []
            if overall_score >= 85:
                recommendations.append("公司表现优于行业平均水平")
            elif overall_score >= 70:
                recommendations.append("公司表现接近行业平均水平")
            else:
                recommendations.append("公司表现低于行业平均水平，需要改进")
            
            if roe_diff < -2:
                recommendations.append("需要提升股东回报率")
            if roa_diff < -1:
                recommendations.append("需要提高资产使用效率")
            if margin_diff < -2:
                recommendations.append("需要改善盈利能力")
            
            return AnalysisResult(
                analysis_type="行业对比分析",
                depth_level=AnalysisDepth.INTERMEDIATE,
                score=overall_score,
                insights=insights,
                recommendations=recommendations,
                confidence=0.8,
                data_quality=0.85
            )
            
        except Exception as e:
            self.logger.error(f"行业对比分析失败: {e}")
            return AnalysisResult(
                analysis_type="行业对比分析",
                depth_level=AnalysisDepth.INTERMEDIATE,
                score=0,
                insights=[f"分析失败: {str(e)}"],
                recommendations=["检查行业数据"],
                confidence=0.0,
                data_quality=0.0
            )
    
    async def perform_trend_analysis(self, historical_data: List[Dict[str, Any]]) -> AnalysisResult:
        """执行趋势分析"""
        try:
            if len(historical_data) < 3:
                return AnalysisResult(
                    analysis_type="趋势分析",
                    depth_level=AnalysisDepth.INTERMEDIATE,
                    score=0,
                    insights=["历史数据不足，无法进行趋势分析"],
                    recommendations=["收集至少3年的历史数据"],
                    confidence=0.0,
                    data_quality=0.0
                )
            
            # 提取关键指标的时间序列
            years = []
            revenues = []
            net_incomes = []
            total_assets = []
            
            for data in historical_data:
                years.append(data.get('year', 0))
                revenues.append(data.get('revenue', 0))
                net_incomes.append(data.get('net_income', 0))
                total_assets.append(data.get('total_assets', 0))
            
            # 计算增长率
            revenue_growth = self._calculate_cagr(revenues)
            income_growth = self._calculate_cagr(net_incomes)
            asset_growth = self._calculate_cagr(total_assets)
            
            # 计算趋势稳定性
            revenue_volatility = np.std(revenues) / np.mean(revenues) if np.mean(revenues) > 0 else 0
            income_volatility = np.std(net_incomes) / np.mean(net_incomes) if np.mean(net_incomes) > 0 else 0
            
            insights = [
                f"分析期间: {min(years)}-{max(years)}",
                f"营业收入复合增长率: {revenue_growth:.1f}%",
                f"净利润复合增长率: {income_growth:.1f}%",
                f"总资产复合增长率: {asset_growth:.1f}%",
                f"收入波动性: {revenue_volatility:.2f}",
                f"利润波动性: {income_volatility:.2f}"
            ]
            
            # 趋势评分
            growth_score = (revenue_growth + income_growth) / 2
            stability_score = 100 - (revenue_volatility + income_volatility) * 50
            overall_score = (growth_score * 0.6 + stability_score * 0.4)
            
            recommendations = []
            if revenue_growth > 10:
                recommendations.append("收入增长强劲")
            elif revenue_growth > 5:
                recommendations.append("收入增长稳健")
            else:
                recommendations.append("收入增长缓慢，需要寻找新的增长点")
            
            if income_growth > revenue_growth:
                recommendations.append("盈利效率在提升")
            else:
                recommendations.append("需要关注成本控制")
            
            if revenue_volatility > 0.2:
                recommendations.append("收入波动较大，需要稳定业务")
            
            return AnalysisResult(
                analysis_type="趋势分析",
                depth_level=AnalysisDepth.INTERMEDIATE,
                score=max(0, min(100, overall_score)),
                insights=insights,
                recommendations=recommendations,
                confidence=0.75,
                data_quality=0.8
            )
            
        except Exception as e:
            self.logger.error(f"趋势分析失败: {e}")
            return AnalysisResult(
                analysis_type="趋势分析",
                depth_level=AnalysisDepth.INTERMEDIATE,
                score=0,
                insights=[f"分析失败: {str(e)}"],
                recommendations=["检查历史数据质量"],
                confidence=0.0,
                data_quality=0.0
            )
    
    def _calculate_cagr(self, values: List[float]) -> float:
        """计算复合年增长率"""
        if len(values) < 2 or values[0] <= 0 or values[-1] <= 0:
            return 0
        
        years = len(values) - 1
        cagr = (values[-1] / values[0]) ** (1/years) - 1
        return cagr * 100
    
    async def perform_esg_analysis(self, company_data: Dict[str, Any]) -> AnalysisResult:
        """执行ESG分析"""
        try:
            # 模拟ESG评分计算
            # 实际应用中需要接入ESG数据源
            
            industry = company_data.get('company_info', {}).get('industry', '制造业')
            employees = company_data.get('company_info', {}).get('employees', 1000)
            revenue = company_data.get('financial_statements', {}).get('revenue', 0)
            
            # 环境评分 (E)
            environmental_score = 70  # 基础分
            if industry in ['科技业', '金融业']:
                environmental_score += 10  # 低碳行业加分
            elif industry in ['化工', '钢铁', '煤炭']:
                environmental_score -= 15  # 高碳行业减分
            
            # 社会责任评分 (S)
            social_score = 75  # 基础分
            if employees > 10000:
                social_score += 5  # 大企业社会责任
            
            # 治理评分 (G)
            governance_score = 80  # 基础分
            if revenue > 10000000000:  # 100亿以上
                governance_score += 5  # 大公司治理相对完善
            
            overall_esg_score = (environmental_score + social_score + governance_score) / 3
            
            insights = [
                f"ESG总体评分: {overall_esg_score:.1f}/100",
                f"环境评分(E): {environmental_score}/100",
                f"社会责任评分(S): {social_score}/100",
                f"治理评分(G): {governance_score}/100",
                f"行业ESG风险等级: {'低' if industry in ['科技业', '金融业'] else '中等'}"
            ]
            
            recommendations = []
            if environmental_score < 70:
                recommendations.append("加强环境保护措施")
            if social_score < 75:
                recommendations.append("提升社会责任履行")
            if governance_score < 80:
                recommendations.append("完善公司治理结构")
            
            if overall_esg_score >= 80:
                recommendations.append("ESG表现良好，有助于可持续发展")
            else:
                recommendations.append("需要加强ESG管理")
            
            return AnalysisResult(
                analysis_type="ESG分析",
                depth_level=AnalysisDepth.ADVANCED,
                score=overall_esg_score,
                insights=insights,
                recommendations=recommendations,
                confidence=0.7,
                data_quality=0.6  # ESG数据通常不够完整
            )
            
        except Exception as e:
            self.logger.error(f"ESG分析失败: {e}")
            return AnalysisResult(
                analysis_type="ESG分析",
                depth_level=AnalysisDepth.ADVANCED,
                score=0,
                insights=[f"分析失败: {str(e)}"],
                recommendations=["补充ESG相关数据"],
                confidence=0.0,
                data_quality=0.0
            )


class AnalysisEnhancer:
    """分析增强器主类"""
    
    def __init__(self):
        """初始化分析增强器"""
        self.logger = logging.getLogger(__name__)
        self.advanced_analyzer = AdvancedFinancialAnalyzer()
        
        # 分析统计
        self.analysis_stats = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'average_confidence': 0.0,
            'analysis_types': {}
        }
    
    async def enhance_financial_analysis(self, company_data: Dict[str, Any], 
                                       depth_level: AnalysisDepth = AnalysisDepth.ADVANCED) -> Dict[str, Any]:
        """
        增强财务分析
        
        Args:
            company_data: 公司数据
            depth_level: 分析深度等级
            
        Returns:
            增强后的分析结果
        """
        try:
            self.logger.info(f"开始增强财务分析，深度等级: {depth_level.value}")
            
            enhanced_analysis = {
                'basic_analysis': {},
                'advanced_analysis': {},
                'analysis_summary': {},
                'enhancement_metadata': {
                    'depth_level': depth_level.value,
                    'analysis_time': datetime.now().isoformat(),
                    'total_analyses': 0,
                    'successful_analyses': 0
                }
            }
            
            financial_data = company_data.get('financial_statements', {})
            industry = company_data.get('company_info', {}).get('industry', '制造业')
            
            # 执行不同深度的分析
            analysis_results = []
            
            if depth_level in [AnalysisDepth.INTERMEDIATE, AnalysisDepth.ADVANCED, AnalysisDepth.EXPERT]:
                # 行业对比分析
                industry_result = await self.advanced_analyzer.perform_industry_comparison(
                    financial_data, industry
                )
                analysis_results.append(industry_result)
                enhanced_analysis['advanced_analysis']['industry_comparison'] = industry_result
            
            if depth_level in [AnalysisDepth.ADVANCED, AnalysisDepth.EXPERT]:
                # Z-Score分析
                zscore_result = await self.advanced_analyzer.perform_zscore_analysis(financial_data)
                analysis_results.append(zscore_result)
                enhanced_analysis['advanced_analysis']['zscore_analysis'] = zscore_result
                
                # ESG分析
                esg_result = await self.advanced_analyzer.perform_esg_analysis(company_data)
                analysis_results.append(esg_result)
                enhanced_analysis['advanced_analysis']['esg_analysis'] = esg_result
            
            if depth_level == AnalysisDepth.EXPERT:
                # 趋势分析（需要历史数据）
                historical_data = company_data.get('historical_data', [])
                if historical_data:
                    trend_result = await self.advanced_analyzer.perform_trend_analysis(historical_data)
                    analysis_results.append(trend_result)
                    enhanced_analysis['advanced_analysis']['trend_analysis'] = trend_result
            
            # 生成分析摘要
            enhanced_analysis['analysis_summary'] = self._generate_analysis_summary(analysis_results)
            
            # 更新统计
            self.analysis_stats['total_analyses'] += len(analysis_results)
            successful_count = sum(1 for result in analysis_results if result.confidence > 0.5)
            self.analysis_stats['successful_analyses'] += successful_count
            
            # 更新平均置信度
            if analysis_results:
                avg_confidence = sum(result.confidence for result in analysis_results) / len(analysis_results)
                total_analyses = self.analysis_stats['total_analyses']
                current_avg = self.analysis_stats['average_confidence']
                new_avg = (current_avg * (total_analyses - len(analysis_results)) + avg_confidence * len(analysis_results)) / total_analyses
                self.analysis_stats['average_confidence'] = new_avg
            
            # 更新分析类型统计
            for result in analysis_results:
                analysis_type = result.analysis_type
                if analysis_type not in self.analysis_stats['analysis_types']:
                    self.analysis_stats['analysis_types'][analysis_type] = 0
                self.analysis_stats['analysis_types'][analysis_type] += 1
            
            enhanced_analysis['enhancement_metadata']['total_analyses'] = len(analysis_results)
            enhanced_analysis['enhancement_metadata']['successful_analyses'] = successful_count
            
            self.logger.info(f"财务分析增强完成，执行了{len(analysis_results)}项分析")
            
            return enhanced_analysis
            
        except Exception as e:
            self.logger.error(f"财务分析增强失败: {e}")
            return {
                'error': str(e),
                'enhancement_metadata': {
                    'depth_level': depth_level.value,
                    'analysis_time': datetime.now().isoformat(),
                    'success': False
                }
            }
    
    def _generate_analysis_summary(self, analysis_results: List[AnalysisResult]) -> Dict[str, Any]:
        """生成分析摘要"""
        if not analysis_results:
            return {'summary': '无可用分析结果'}
        
        # 计算总体评分
        valid_scores = [result.score for result in analysis_results if result.confidence > 0.5]
        overall_score = sum(valid_scores) / len(valid_scores) if valid_scores else 0
        
        # 收集所有见解和建议
        all_insights = []
        all_recommendations = []
        
        for result in analysis_results:
            if result.confidence > 0.5:
                all_insights.extend(result.insights)
                all_recommendations.extend(result.recommendations)
        
        # 计算平均置信度
        avg_confidence = sum(result.confidence for result in analysis_results) / len(analysis_results)
        
        # 确定分析质量等级
        if overall_score >= 85 and avg_confidence >= 0.8:
            quality_level = "优秀"
        elif overall_score >= 75 and avg_confidence >= 0.7:
            quality_level = "良好"
        elif overall_score >= 65 and avg_confidence >= 0.6:
            quality_level = "一般"
        else:
            quality_level = "需要改进"
        
        return {
            'overall_score': overall_score,
            'quality_level': quality_level,
            'average_confidence': avg_confidence,
            'total_analyses': len(analysis_results),
            'successful_analyses': sum(1 for r in analysis_results if r.confidence > 0.5),
            'key_insights': all_insights[:10],  # 取前10个见解
            'top_recommendations': list(set(all_recommendations))[:8],  # 去重后取前8个建议
            'analysis_coverage': [result.analysis_type for result in analysis_results]
        }
    
    def get_enhancement_report(self) -> Dict[str, Any]:
        """获取增强报告"""
        total_analyses = self.analysis_stats['total_analyses']
        success_rate = (self.analysis_stats['successful_analyses'] / max(total_analyses, 1)) * 100
        
        return {
            'total_analyses_performed': total_analyses,
            'success_rate': success_rate,
            'average_confidence': self.analysis_stats['average_confidence'],
            'analysis_types_distribution': self.analysis_stats['analysis_types'],
            'enhancement_recommendations': self._get_enhancement_recommendations()
        }
    
    def _get_enhancement_recommendations(self) -> List[str]:
        """获取增强建议"""
        recommendations = []
        
        success_rate = (self.analysis_stats['successful_analyses'] / max(self.analysis_stats['total_analyses'], 1)) * 100
        
        if success_rate < 80:
            recommendations.append("建议改进数据质量以提高分析成功率")
        
        if self.analysis_stats['average_confidence'] < 0.7:
            recommendations.append("建议增加更多数据源以提高分析置信度")
        
        # 检查分析类型覆盖度
        analysis_types = self.analysis_stats['analysis_types']
        if 'Z-Score分析' not in analysis_types:
            recommendations.append("建议增加破产风险分析")
        
        if 'ESG分析' not in analysis_types:
            recommendations.append("建议增加ESG可持续发展分析")
        
        if '趋势分析' not in analysis_types:
            recommendations.append("建议收集历史数据进行趋势分析")
        
        recommendations.extend([
            "定期更新行业基准数据",
            "建立分析结果验证机制",
            "开发更多专业分析模型"
        ])
        
        return recommendations
