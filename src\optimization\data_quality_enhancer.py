#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据质量增强器
提升数据收集的准确性、实时性和完整性
"""

import asyncio
import logging
import json
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import re


class DataQualityLevel(Enum):
    """数据质量等级"""
    EXCELLENT = "excellent"  # 90-100分
    GOOD = "good"           # 80-89分
    FAIR = "fair"           # 70-79分
    POOR = "poor"           # 60-69分
    CRITICAL = "critical"   # <60分


@dataclass
class DataQualityMetrics:
    """数据质量指标"""
    completeness: float      # 完整性 (0-100)
    accuracy: float         # 准确性 (0-100)
    consistency: float      # 一致性 (0-100)
    timeliness: float       # 时效性 (0-100)
    validity: float         # 有效性 (0-100)
    overall_score: float    # 总体评分 (0-100)
    quality_level: DataQualityLevel
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'completeness': self.completeness,
            'accuracy': self.accuracy,
            'consistency': self.consistency,
            'timeliness': self.timeliness,
            'validity': self.validity,
            'overall_score': self.overall_score,
            'quality_level': self.quality_level.value
        }


class DataValidator:
    """数据验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.logger = logging.getLogger(__name__)
        
        # 财务数据验证规则
        self.financial_rules = {
            'revenue': {'min': 0, 'type': 'numeric', 'required': True},
            'net_income': {'type': 'numeric', 'required': True},
            'total_assets': {'min': 0, 'type': 'numeric', 'required': True},
            'total_equity': {'min': 0, 'type': 'numeric', 'required': True},
            'current_assets': {'min': 0, 'type': 'numeric'},
            'current_liabilities': {'min': 0, 'type': 'numeric'},
            'cash_and_equivalents': {'min': 0, 'type': 'numeric'}
        }
        
        # 股票数据验证规则
        self.stock_rules = {
            'current_price': {'min': 0, 'type': 'numeric', 'required': True},
            'volume': {'min': 0, 'type': 'numeric'},
            'market_cap': {'min': 0, 'type': 'numeric'},
            'pe_ratio': {'min': 0, 'type': 'numeric'},
            'pb_ratio': {'min': 0, 'type': 'numeric'}
        }
        
        # 公司信息验证规则
        self.company_rules = {
            'name': {'type': 'string', 'required': True, 'min_length': 2},
            'symbol': {'type': 'string', 'required': True, 'pattern': r'^[A-Z0-9]{6}$'},
            'industry': {'type': 'string', 'required': True},
            'employees': {'min': 0, 'type': 'numeric'}
        }
    
    def validate_financial_data(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证财务数据"""
        return self._validate_data(data, self.financial_rules, "财务数据")
    
    def validate_stock_data(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证股票数据"""
        return self._validate_data(data, self.stock_rules, "股票数据")
    
    def validate_company_info(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证公司信息"""
        return self._validate_data(data, self.company_rules, "公司信息")
    
    def _validate_data(self, data: Dict[str, Any], rules: Dict[str, Any], 
                      data_type: str) -> Tuple[bool, List[str]]:
        """通用数据验证"""
        errors = []
        
        for field, rule in rules.items():
            value = data.get(field)
            
            # 检查必填字段
            if rule.get('required', False) and (value is None or value == ''):
                errors.append(f"{data_type}缺少必填字段: {field}")
                continue
            
            if value is None:
                continue
            
            # 检查数据类型
            if rule.get('type') == 'numeric':
                if not isinstance(value, (int, float)):
                    try:
                        value = float(value)
                        data[field] = value  # 自动转换
                    except (ValueError, TypeError):
                        errors.append(f"{data_type}字段 {field} 应为数值类型")
                        continue
                
                # 检查最小值
                if 'min' in rule and value < rule['min']:
                    errors.append(f"{data_type}字段 {field} 值 {value} 小于最小值 {rule['min']}")
                
                # 检查最大值
                if 'max' in rule and value > rule['max']:
                    errors.append(f"{data_type}字段 {field} 值 {value} 大于最大值 {rule['max']}")
            
            elif rule.get('type') == 'string':
                if not isinstance(value, str):
                    errors.append(f"{data_type}字段 {field} 应为字符串类型")
                    continue
                
                # 检查最小长度
                if 'min_length' in rule and len(value) < rule['min_length']:
                    errors.append(f"{data_type}字段 {field} 长度不足")
                
                # 检查正则表达式
                if 'pattern' in rule and not re.match(rule['pattern'], value):
                    errors.append(f"{data_type}字段 {field} 格式不正确")
        
        return len(errors) == 0, errors
    
    def validate_data_consistency(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证数据一致性"""
        errors = []
        
        financial_data = data.get('financial_statements', {})
        
        # 检查资产负债表平衡
        total_assets = financial_data.get('total_assets', 0)
        total_liabilities = financial_data.get('total_liabilities', 0)
        total_equity = financial_data.get('total_equity', 0)
        
        if total_assets > 0 and total_liabilities > 0 and total_equity > 0:
            balance_diff = abs(total_assets - (total_liabilities + total_equity))
            if balance_diff / total_assets > 0.01:  # 允许1%的误差
                errors.append("资产负债表不平衡")
        
        # 检查收入与利润的合理性
        revenue = financial_data.get('revenue', 0)
        net_income = financial_data.get('net_income', 0)
        
        if revenue > 0 and net_income > revenue:
            errors.append("净利润不能大于营业收入")
        
        # 检查市值与财务数据的一致性
        stock_data = data.get('stock_data', {})
        market_cap = stock_data.get('market_cap', 0)
        current_price = stock_data.get('current_price', 0)
        
        if market_cap > 0 and current_price > 0 and total_equity > 0:
            pb_ratio = market_cap / total_equity
            reported_pb = stock_data.get('pb_ratio', 0)
            
            if reported_pb > 0 and abs(pb_ratio - reported_pb) / reported_pb > 0.1:
                errors.append("市净率计算不一致")
        
        return len(errors) == 0, errors


class DataCleaner:
    """数据清洗器"""
    
    def __init__(self):
        """初始化清洗器"""
        self.logger = logging.getLogger(__name__)
    
    def clean_financial_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """清洗财务数据"""
        cleaned_data = data.copy()
        
        # 处理缺失值
        financial_statements = cleaned_data.get('financial_statements', {})
        
        # 填充关键财务指标的缺失值
        if 'total_liabilities' not in financial_statements and 'total_assets' in financial_statements and 'total_equity' in financial_statements:
            financial_statements['total_liabilities'] = financial_statements['total_assets'] - financial_statements['total_equity']
        
        # 计算衍生指标
        if 'current_ratio' not in financial_statements:
            current_assets = financial_statements.get('current_assets', 0)
            current_liabilities = financial_statements.get('current_liabilities', 0)
            if current_liabilities > 0:
                financial_statements['current_ratio'] = current_assets / current_liabilities
        
        # 异常值处理
        self._handle_outliers(financial_statements)
        
        cleaned_data['financial_statements'] = financial_statements
        return cleaned_data
    
    def _handle_outliers(self, data: Dict[str, Any]):
        """处理异常值"""
        # 检查并修正明显的异常值
        for key, value in data.items():
            if isinstance(value, (int, float)):
                # 处理负数资产
                if key in ['total_assets', 'current_assets', 'cash_and_equivalents'] and value < 0:
                    self.logger.warning(f"发现负数资产 {key}: {value}，设置为0")
                    data[key] = 0
                
                # 处理极大值
                if value > 1e15:  # 超过1000万亿
                    self.logger.warning(f"发现异常大值 {key}: {value}，可能需要检查")
    
    def standardize_data_format(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """标准化数据格式"""
        standardized_data = {}
        
        for key, value in data.items():
            if isinstance(value, dict):
                standardized_data[key] = self.standardize_data_format(value)
            elif isinstance(value, str):
                # 标准化字符串
                standardized_data[key] = value.strip()
            elif isinstance(value, (int, float)):
                # 标准化数值
                if abs(value) < 1e-10:  # 接近0的值设为0
                    standardized_data[key] = 0
                else:
                    standardized_data[key] = round(value, 2) if isinstance(value, float) else value
            else:
                standardized_data[key] = value
        
        return standardized_data


class DataQualityEnhancer:
    """数据质量增强器主类"""
    
    def __init__(self):
        """初始化数据质量增强器"""
        self.logger = logging.getLogger(__name__)
        self.validator = DataValidator()
        self.cleaner = DataCleaner()
        
        # 质量统计
        self.quality_stats = {
            'total_validations': 0,
            'passed_validations': 0,
            'total_cleanings': 0,
            'data_improvements': 0
        }
    
    async def enhance_data_quality(self, data: Dict[str, Any]) -> Tuple[Dict[str, Any], DataQualityMetrics]:
        """
        增强数据质量
        
        Args:
            data: 原始数据
            
        Returns:
            (增强后的数据, 质量指标)
        """
        try:
            self.logger.info("开始数据质量增强")
            
            # 1. 数据验证
            validation_results = await self._validate_all_data(data)
            
            # 2. 数据清洗
            cleaned_data = await self._clean_all_data(data)
            
            # 3. 数据标准化
            standardized_data = self.cleaner.standardize_data_format(cleaned_data)
            
            # 4. 计算质量指标
            quality_metrics = await self._calculate_quality_metrics(
                standardized_data, validation_results
            )
            
            # 5. 更新统计
            self.quality_stats['total_validations'] += 1
            if quality_metrics.overall_score >= 80:
                self.quality_stats['passed_validations'] += 1
            
            self.quality_stats['total_cleanings'] += 1
            if quality_metrics.overall_score > 70:  # 假设原始质量为70
                self.quality_stats['data_improvements'] += 1
            
            self.logger.info(f"数据质量增强完成，总体评分: {quality_metrics.overall_score:.1f}")
            
            return standardized_data, quality_metrics
            
        except Exception as e:
            self.logger.error(f"数据质量增强失败: {e}")
            # 返回原始数据和低质量评分
            poor_metrics = DataQualityMetrics(
                completeness=50, accuracy=50, consistency=50,
                timeliness=50, validity=50, overall_score=50,
                quality_level=DataQualityLevel.POOR
            )
            return data, poor_metrics
    
    async def _validate_all_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证所有数据"""
        validation_results = {
            'financial_data': {'valid': True, 'errors': []},
            'stock_data': {'valid': True, 'errors': []},
            'company_info': {'valid': True, 'errors': []},
            'consistency': {'valid': True, 'errors': []}
        }
        
        # 验证财务数据
        if 'financial_statements' in data:
            valid, errors = self.validator.validate_financial_data(data['financial_statements'])
            validation_results['financial_data'] = {'valid': valid, 'errors': errors}
        
        # 验证股票数据
        if 'stock_data' in data:
            valid, errors = self.validator.validate_stock_data(data['stock_data'])
            validation_results['stock_data'] = {'valid': valid, 'errors': errors}
        
        # 验证公司信息
        if 'company_info' in data:
            valid, errors = self.validator.validate_company_info(data['company_info'])
            validation_results['company_info'] = {'valid': valid, 'errors': errors}
        
        # 验证数据一致性
        valid, errors = self.validator.validate_data_consistency(data)
        validation_results['consistency'] = {'valid': valid, 'errors': errors}
        
        return validation_results
    
    async def _clean_all_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """清洗所有数据"""
        cleaned_data = self.cleaner.clean_financial_data(data)
        return cleaned_data
    
    async def _calculate_quality_metrics(self, data: Dict[str, Any], 
                                       validation_results: Dict[str, Any]) -> DataQualityMetrics:
        """计算数据质量指标"""
        # 完整性评分
        completeness = self._calculate_completeness(data)
        
        # 准确性评分
        accuracy = self._calculate_accuracy(validation_results)
        
        # 一致性评分
        consistency = self._calculate_consistency(validation_results)
        
        # 时效性评分
        timeliness = self._calculate_timeliness(data)
        
        # 有效性评分
        validity = self._calculate_validity(validation_results)
        
        # 总体评分
        overall_score = (completeness + accuracy + consistency + timeliness + validity) / 5
        
        # 确定质量等级
        if overall_score >= 90:
            quality_level = DataQualityLevel.EXCELLENT
        elif overall_score >= 80:
            quality_level = DataQualityLevel.GOOD
        elif overall_score >= 70:
            quality_level = DataQualityLevel.FAIR
        elif overall_score >= 60:
            quality_level = DataQualityLevel.POOR
        else:
            quality_level = DataQualityLevel.CRITICAL
        
        return DataQualityMetrics(
            completeness=completeness,
            accuracy=accuracy,
            consistency=consistency,
            timeliness=timeliness,
            validity=validity,
            overall_score=overall_score,
            quality_level=quality_level
        )
    
    def _calculate_completeness(self, data: Dict[str, Any]) -> float:
        """计算完整性评分"""
        required_fields = {
            'company_info': ['name', 'symbol', 'industry'],
            'financial_statements': ['revenue', 'net_income', 'total_assets', 'total_equity'],
            'stock_data': ['current_price']
        }
        
        total_fields = 0
        present_fields = 0
        
        for section, fields in required_fields.items():
            if section in data:
                section_data = data[section]
                for field in fields:
                    total_fields += 1
                    if field in section_data and section_data[field] is not None:
                        present_fields += 1
        
        return (present_fields / total_fields * 100) if total_fields > 0 else 0
    
    def _calculate_accuracy(self, validation_results: Dict[str, Any]) -> float:
        """计算准确性评分"""
        total_validations = len(validation_results)
        passed_validations = sum(1 for result in validation_results.values() if result['valid'])
        
        return (passed_validations / total_validations * 100) if total_validations > 0 else 100
    
    def _calculate_consistency(self, validation_results: Dict[str, Any]) -> float:
        """计算一致性评分"""
        consistency_result = validation_results.get('consistency', {'valid': True})
        return 100 if consistency_result['valid'] else 60
    
    def _calculate_timeliness(self, data: Dict[str, Any]) -> float:
        """计算时效性评分"""
        # 检查数据时间戳
        stock_data = data.get('stock_data', {})
        if 'timestamp' in stock_data:
            try:
                timestamp = datetime.fromisoformat(stock_data['timestamp'].replace('Z', '+00:00'))
                age_hours = (datetime.now() - timestamp.replace(tzinfo=None)).total_seconds() / 3600
                
                if age_hours <= 1:
                    return 100
                elif age_hours <= 24:
                    return 90
                elif age_hours <= 168:  # 1周
                    return 80
                else:
                    return 60
            except:
                return 70
        
        return 70  # 默认评分
    
    def _calculate_validity(self, validation_results: Dict[str, Any]) -> float:
        """计算有效性评分"""
        total_errors = sum(len(result['errors']) for result in validation_results.values())
        
        if total_errors == 0:
            return 100
        elif total_errors <= 2:
            return 85
        elif total_errors <= 5:
            return 70
        else:
            return 50
    
    def get_quality_report(self) -> Dict[str, Any]:
        """获取质量报告"""
        total_validations = self.quality_stats['total_validations']
        success_rate = (self.quality_stats['passed_validations'] / max(total_validations, 1)) * 100
        improvement_rate = (self.quality_stats['data_improvements'] / max(self.quality_stats['total_cleanings'], 1)) * 100
        
        return {
            'total_validations': total_validations,
            'validation_success_rate': success_rate,
            'total_cleanings': self.quality_stats['total_cleanings'],
            'improvement_rate': improvement_rate,
            'recommendations': self._get_quality_recommendations()
        }
    
    def _get_quality_recommendations(self) -> List[str]:
        """获取质量改进建议"""
        recommendations = []
        
        success_rate = (self.quality_stats['passed_validations'] / max(self.quality_stats['total_validations'], 1)) * 100
        
        if success_rate < 80:
            recommendations.append("建议加强数据源验证和清洗流程")
        
        if self.quality_stats['data_improvements'] < self.quality_stats['total_cleanings'] * 0.8:
            recommendations.append("建议优化数据清洗算法")
        
        recommendations.extend([
            "定期更新数据验证规则",
            "建立数据质量监控仪表板",
            "实施数据质量SLA标准"
        ])
        
        return recommendations
