#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AFAC系统性能优化器
实现模型量化、缓存、并发优化等性能提升功能
"""

import asyncio
import logging
import json
import hashlib
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from pathlib import Path
import threading
from concurrent.futures import ThreadPoolExecutor


@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    timestamp: datetime
    ttl: int  # 生存时间(秒)
    access_count: int = 0
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        return datetime.now() > self.timestamp + timedelta(seconds=self.ttl)


class IntelligentCache:
    """智能缓存系统"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        """
        初始化缓存
        
        Args:
            max_size: 最大缓存条目数
            default_ttl: 默认生存时间(秒)
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: Dict[str, CacheEntry] = {}
        self.lock = threading.RLock()
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'total_requests': 0
        }
    
    def _generate_key(self, *args, **kwargs) -> str:
        """生成缓存键"""
        key_data = {
            'args': args,
            'kwargs': sorted(kwargs.items())
        }
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            self.stats['total_requests'] += 1
            
            if key in self.cache:
                entry = self.cache[key]
                
                if entry.is_expired():
                    del self.cache[key]
                    self.stats['misses'] += 1
                    return None
                
                entry.access_count += 1
                self.stats['hits'] += 1
                return entry.value
            
            self.stats['misses'] += 1
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        with self.lock:
            if len(self.cache) >= self.max_size:
                self._evict_lru()
            
            ttl = ttl or self.default_ttl
            entry = CacheEntry(
                key=key,
                value=value,
                timestamp=datetime.now(),
                ttl=ttl
            )
            self.cache[key] = entry
    
    def _evict_lru(self) -> None:
        """淘汰最少使用的条目"""
        if not self.cache:
            return
        
        # 找到访问次数最少的条目
        lru_key = min(self.cache.keys(), key=lambda k: self.cache[k].access_count)
        del self.cache[lru_key]
        self.stats['evictions'] += 1
    
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self.lock:
            hit_rate = self.stats['hits'] / max(self.stats['total_requests'], 1) * 100
            return {
                **self.stats,
                'hit_rate': hit_rate,
                'cache_size': len(self.cache),
                'max_size': self.max_size
            }


class ModelQuantizer:
    """模型量化器"""
    
    def __init__(self):
        """初始化量化器"""
        self.logger = logging.getLogger(__name__)
        self.quantization_configs = {
            'int8': {
                'bits': 8,
                'memory_reduction': 0.5,
                'speed_improvement': 1.2
            },
            'int4': {
                'bits': 4,
                'memory_reduction': 0.25,
                'speed_improvement': 1.5
            },
            'fp16': {
                'bits': 16,
                'memory_reduction': 0.5,
                'speed_improvement': 1.1
            }
        }
    
    def quantize_model(self, model_name: str, quantization_type: str = 'int8') -> Dict[str, Any]:
        """
        量化模型
        
        Args:
            model_name: 模型名称
            quantization_type: 量化类型
            
        Returns:
            量化结果
        """
        try:
            if quantization_type not in self.quantization_configs:
                raise ValueError(f"不支持的量化类型: {quantization_type}")
            
            config = self.quantization_configs[quantization_type]
            
            # 模拟量化过程
            self.logger.info(f"开始量化模型 {model_name} 到 {quantization_type}")
            
            # 这里应该是真实的量化代码
            # 例如使用 torch.quantization 或 optimum
            
            result = {
                'model_name': model_name,
                'quantization_type': quantization_type,
                'original_size': 13.5,  # GB
                'quantized_size': 13.5 * config['memory_reduction'],
                'memory_reduction': config['memory_reduction'],
                'expected_speedup': config['speed_improvement'],
                'quantization_time': 2.5,  # 模拟量化时间
                'success': True
            }
            
            self.logger.info(f"模型量化完成: {result}")
            return result
            
        except Exception as e:
            self.logger.error(f"模型量化失败: {e}")
            return {
                'model_name': model_name,
                'success': False,
                'error': str(e)
            }
    
    def get_quantization_recommendations(self, available_memory: float, 
                                       performance_target: str) -> List[str]:
        """
        获取量化建议
        
        Args:
            available_memory: 可用内存(GB)
            performance_target: 性能目标('speed', 'memory', 'balanced')
            
        Returns:
            量化建议列表
        """
        recommendations = []
        
        if available_memory < 8:
            recommendations.append("建议使用INT4量化以减少内存使用")
        elif available_memory < 16:
            recommendations.append("建议使用INT8量化平衡性能和内存")
        else:
            recommendations.append("可以使用FP16量化保持精度")
        
        if performance_target == 'speed':
            recommendations.append("优先选择INT4量化获得最大加速")
        elif performance_target == 'memory':
            recommendations.append("使用INT4量化最小化内存占用")
        else:
            recommendations.append("使用INT8量化平衡性能和精度")
        
        return recommendations


class ConcurrencyOptimizer:
    """并发优化器"""
    
    def __init__(self, max_workers: int = 4):
        """
        初始化并发优化器
        
        Args:
            max_workers: 最大工作线程数
        """
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.semaphore = asyncio.Semaphore(max_workers)
        self.logger = logging.getLogger(__name__)
        
        # 并发统计
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'average_wait_time': 0.0,
            'average_execution_time': 0.0
        }
    
    async def execute_concurrent_tasks(self, tasks: List[Tuple[callable, tuple, dict]]) -> List[Any]:
        """
        并发执行任务
        
        Args:
            tasks: 任务列表，每个任务为(函数, 参数, 关键字参数)
            
        Returns:
            执行结果列表
        """
        async def execute_single_task(task_info):
            func, args, kwargs = task_info
            
            async with self.semaphore:
                start_time = time.time()
                
                try:
                    if asyncio.iscoroutinefunction(func):
                        result = await func(*args, **kwargs)
                    else:
                        loop = asyncio.get_event_loop()
                        result = await loop.run_in_executor(self.executor, func, *args, **kwargs)
                    
                    execution_time = time.time() - start_time
                    self._update_stats(execution_time, True)
                    
                    return result
                    
                except Exception as e:
                    execution_time = time.time() - start_time
                    self._update_stats(execution_time, False)
                    self.logger.error(f"任务执行失败: {e}")
                    return {'error': str(e)}
        
        # 并发执行所有任务
        self.stats['total_tasks'] += len(tasks)
        results = await asyncio.gather(*[execute_single_task(task) for task in tasks])
        
        return results
    
    def _update_stats(self, execution_time: float, success: bool):
        """更新统计信息"""
        if success:
            self.stats['completed_tasks'] += 1
        else:
            self.stats['failed_tasks'] += 1
        
        # 更新平均执行时间
        total_completed = self.stats['completed_tasks']
        if total_completed > 0:
            current_avg = self.stats['average_execution_time']
            new_avg = (current_avg * (total_completed - 1) + execution_time) / total_completed
            self.stats['average_execution_time'] = new_avg
    
    def get_stats(self) -> Dict[str, Any]:
        """获取并发统计"""
        total_tasks = self.stats['total_tasks']
        success_rate = self.stats['completed_tasks'] / max(total_tasks, 1) * 100
        
        return {
            **self.stats,
            'success_rate': success_rate,
            'max_workers': self.max_workers
        }
    
    def cleanup(self):
        """清理资源"""
        self.executor.shutdown(wait=True)


class PerformanceOptimizer:
    """性能优化器主类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化性能优化器
        
        Args:
            config: 配置参数
        """
        self.logger = logging.getLogger(__name__)
        self.config = config or {}
        
        # 初始化组件
        self.cache = IntelligentCache(
            max_size=self.config.get('cache_max_size', 1000),
            default_ttl=self.config.get('cache_ttl', 3600)
        )
        
        self.quantizer = ModelQuantizer()
        
        self.concurrency_optimizer = ConcurrencyOptimizer(
            max_workers=self.config.get('max_workers', 4)
        )
        
        # 性能监控
        self.performance_metrics = {
            'optimization_start_time': datetime.now(),
            'total_optimizations': 0,
            'memory_savings': 0.0,
            'speed_improvements': 0.0
        }
    
    async def optimize_model_inference(self, model_name: str, 
                                     optimization_level: str = 'balanced') -> Dict[str, Any]:
        """
        优化模型推理
        
        Args:
            model_name: 模型名称
            optimization_level: 优化级别('speed', 'memory', 'balanced')
            
        Returns:
            优化结果
        """
        try:
            self.logger.info(f"开始优化模型推理: {model_name}")
            
            optimization_result = {
                'model_name': model_name,
                'optimization_level': optimization_level,
                'optimizations_applied': [],
                'performance_improvement': {},
                'success': True
            }
            
            # 1. 模型量化
            if optimization_level in ['memory', 'balanced']:
                quantization_type = 'int8' if optimization_level == 'balanced' else 'int4'
                quant_result = self.quantizer.quantize_model(model_name, quantization_type)
                
                if quant_result['success']:
                    optimization_result['optimizations_applied'].append('模型量化')
                    optimization_result['performance_improvement']['memory_reduction'] = quant_result['memory_reduction']
                    optimization_result['performance_improvement']['speed_improvement'] = quant_result['expected_speedup']
            
            # 2. 缓存优化
            cache_config = {
                'enabled': True,
                'ttl': 1800 if optimization_level == 'speed' else 3600,
                'max_size': 2000 if optimization_level == 'speed' else 1000
            }
            optimization_result['optimizations_applied'].append('智能缓存')
            optimization_result['cache_config'] = cache_config
            
            # 3. 并发优化
            if optimization_level == 'speed':
                optimization_result['optimizations_applied'].append('并发优化')
                optimization_result['concurrency_config'] = {
                    'max_workers': 6,
                    'batch_size': 8
                }
            
            self.performance_metrics['total_optimizations'] += 1
            self.logger.info(f"模型推理优化完成: {optimization_result}")
            
            return optimization_result
            
        except Exception as e:
            self.logger.error(f"模型推理优化失败: {e}")
            return {
                'model_name': model_name,
                'success': False,
                'error': str(e)
            }
    
    def cache_decorator(self, ttl: Optional[int] = None):
        """缓存装饰器"""
        def decorator(func):
            async def wrapper(*args, **kwargs):
                # 生成缓存键
                cache_key = self.cache._generate_key(func.__name__, *args, **kwargs)
                
                # 尝试从缓存获取
                cached_result = self.cache.get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # 执行函数并缓存结果
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                
                self.cache.set(cache_key, result, ttl)
                return result
            
            return wrapper
        return decorator
    
    async def optimize_batch_processing(self, tasks: List[Any], 
                                      batch_size: int = 4) -> List[Any]:
        """
        优化批处理
        
        Args:
            tasks: 任务列表
            batch_size: 批处理大小
            
        Returns:
            处理结果
        """
        try:
            # 将任务分批
            batches = [tasks[i:i + batch_size] for i in range(0, len(tasks), batch_size)]
            all_results = []
            
            for batch in batches:
                # 并发处理批次
                batch_results = await self.concurrency_optimizer.execute_concurrent_tasks(batch)
                all_results.extend(batch_results)
            
            return all_results
            
        except Exception as e:
            self.logger.error(f"批处理优化失败: {e}")
            return []
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        uptime = datetime.now() - self.performance_metrics['optimization_start_time']
        
        return {
            'uptime_seconds': uptime.total_seconds(),
            'total_optimizations': self.performance_metrics['total_optimizations'],
            'cache_stats': self.cache.get_stats(),
            'concurrency_stats': self.concurrency_optimizer.get_stats(),
            'memory_usage': self._get_memory_usage(),
            'optimization_recommendations': self._get_optimization_recommendations()
        }
    
    def _get_memory_usage(self) -> Dict[str, float]:
        """获取内存使用情况"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            return {
                'total_gb': memory.total / (1024**3),
                'available_gb': memory.available / (1024**3),
                'used_percent': memory.percent
            }
        except ImportError:
            return {'error': 'psutil not available'}
    
    def _get_optimization_recommendations(self) -> List[str]:
        """获取优化建议"""
        recommendations = []
        
        cache_stats = self.cache.get_stats()
        if cache_stats['hit_rate'] < 50:
            recommendations.append("缓存命中率较低，建议增加缓存大小或调整TTL")
        
        concurrency_stats = self.concurrency_optimizer.get_stats()
        if concurrency_stats['success_rate'] < 95:
            recommendations.append("并发任务失败率较高，建议检查任务逻辑")
        
        try:
            import psutil
            memory = psutil.virtual_memory()
            if memory.percent > 80:
                recommendations.append("内存使用率过高，建议启用模型量化")
        except ImportError:
            pass
        
        return recommendations
    
    async def cleanup(self):
        """清理资源"""
        try:
            self.cache.clear()
            self.concurrency_optimizer.cleanup()
            self.logger.info("性能优化器资源已清理")
        except Exception as e:
            self.logger.error(f"清理资源失败: {e}")
