#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统稳定性增强器
增强错误处理、容错机制、监控告警等系统稳定性
"""

import asyncio
import logging
import json
import time
import psutil
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import threading
from pathlib import Path


class HealthStatus(Enum):
    """健康状态"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    DOWN = "down"


class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class HealthCheck:
    """健康检查"""
    component: str
    status: HealthStatus
    message: str
    timestamp: datetime
    metrics: Dict[str, Any]
    response_time: float


@dataclass
class Alert:
    """告警"""
    alert_id: str
    level: AlertLevel
    component: str
    message: str
    timestamp: datetime
    resolved: bool = False
    resolution_time: Optional[datetime] = None


class CircuitBreaker:
    """熔断器"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        """
        初始化熔断器
        
        Args:
            failure_threshold: 失败阈值
            recovery_timeout: 恢复超时时间(秒)
        """
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        self.lock = threading.Lock()
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """调用函数（带熔断保护）"""
        with self.lock:
            if self.state == "OPEN":
                if self._should_attempt_reset():
                    self.state = "HALF_OPEN"
                else:
                    raise Exception("熔断器开启，服务不可用")
            
            try:
                result = func(*args, **kwargs)
                self._on_success()
                return result
            except Exception as e:
                self._on_failure()
                raise e
    
    def _should_attempt_reset(self) -> bool:
        """是否应该尝试重置"""
        if self.last_failure_time is None:
            return False
        return time.time() - self.last_failure_time >= self.recovery_timeout
    
    def _on_success(self) -> None:
        """成功回调"""
        self.failure_count = 0
        self.state = "CLOSED"
    
    def _on_failure(self) -> None:
        """失败回调"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"


class RetryManager:
    """重试管理器"""
    
    def __init__(self, max_retries: int = 3, base_delay: float = 1.0, 
                 max_delay: float = 60.0, backoff_factor: float = 2.0):
        """
        初始化重试管理器
        
        Args:
            max_retries: 最大重试次数
            base_delay: 基础延迟时间
            max_delay: 最大延迟时间
            backoff_factor: 退避因子
        """
        self.max_retries = max_retries
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor
        self.logger = logging.getLogger(__name__)
    
    async def retry_async(self, func: Callable, *args, **kwargs) -> Any:
        """异步重试"""
        last_exception = None
        
        for attempt in range(self.max_retries + 1):
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                
                if attempt < self.max_retries:
                    delay = min(self.base_delay * (self.backoff_factor ** attempt), self.max_delay)
                    self.logger.warning(f"第{attempt + 1}次尝试失败，{delay:.1f}秒后重试: {e}")
                    await asyncio.sleep(delay)
                else:
                    self.logger.error(f"重试{self.max_retries}次后仍然失败: {e}")
        
        raise last_exception


class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, check_interval: int = 30):
        """
        初始化系统监控器
        
        Args:
            check_interval: 检查间隔(秒)
        """
        self.check_interval = check_interval
        self.logger = logging.getLogger(__name__)
        self.health_checks: Dict[str, HealthCheck] = {}
        self.alerts: List[Alert] = []
        self.monitoring = False
        self.monitor_task = None
        
        # 监控阈值
        self.thresholds = {
            'cpu_usage': 80.0,
            'memory_usage': 85.0,
            'disk_usage': 90.0,
            'response_time': 5.0
        }
    
    async def start_monitoring(self) -> None:
        """开始监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        self.logger.info("系统监控已启动")
    
    async def stop_monitoring(self) -> None:
        """停止监控"""
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        self.logger.info("系统监控已停止")
    
    async def _monitor_loop(self) -> None:
        """监控循环"""
        while self.monitoring:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"监控循环错误: {e}")
                await asyncio.sleep(self.check_interval)
    
    async def _perform_health_checks(self) -> None:
        """执行健康检查"""
        # 系统资源检查
        await self._check_system_resources()
        
        # 应用程序检查
        await self._check_application_health()
        
        # 数据库连接检查
        await self._check_database_connection()
        
        # 外部服务检查
        await self._check_external_services()
    
    async def _check_system_resources(self) -> None:
        """检查系统资源"""
        start_time = time.time()
        
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存使用率
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # 磁盘使用率
            disk = psutil.disk_usage('/')
            disk_percent = disk.percent
            
            # 确定健康状态
            status = HealthStatus.HEALTHY
            messages = []
            
            if cpu_percent > self.thresholds['cpu_usage']:
                status = HealthStatus.WARNING if cpu_percent < 90 else HealthStatus.CRITICAL
                messages.append(f"CPU使用率过高: {cpu_percent:.1f}%")
            
            if memory_percent > self.thresholds['memory_usage']:
                status = HealthStatus.WARNING if memory_percent < 95 else HealthStatus.CRITICAL
                messages.append(f"内存使用率过高: {memory_percent:.1f}%")
            
            if disk_percent > self.thresholds['disk_usage']:
                status = HealthStatus.WARNING if disk_percent < 95 else HealthStatus.CRITICAL
                messages.append(f"磁盘使用率过高: {disk_percent:.1f}%")
            
            message = "; ".join(messages) if messages else "系统资源正常"
            response_time = time.time() - start_time
            
            health_check = HealthCheck(
                component="system_resources",
                status=status,
                message=message,
                timestamp=datetime.now(),
                metrics={
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory_percent,
                    'disk_percent': disk_percent
                },
                response_time=response_time
            )
            
            self.health_checks["system_resources"] = health_check
            
            # 生成告警
            if status in [HealthStatus.WARNING, HealthStatus.CRITICAL]:
                await self._generate_alert(
                    AlertLevel.WARNING if status == HealthStatus.WARNING else AlertLevel.CRITICAL,
                    "system_resources",
                    message
                )
            
        except Exception as e:
            self.logger.error(f"系统资源检查失败: {e}")
            self.health_checks["system_resources"] = HealthCheck(
                component="system_resources",
                status=HealthStatus.DOWN,
                message=f"检查失败: {str(e)}",
                timestamp=datetime.now(),
                metrics={},
                response_time=time.time() - start_time
            )
    
    async def _check_application_health(self) -> None:
        """检查应用程序健康状态"""
        start_time = time.time()
        
        try:
            # 检查关键组件
            components_status = {
                'model_manager': True,  # 模拟检查
                'data_collector': True,
                'analysis_engine': True,
                'report_generator': True
            }
            
            failed_components = [comp for comp, status in components_status.items() if not status]
            
            if failed_components:
                status = HealthStatus.CRITICAL
                message = f"组件故障: {', '.join(failed_components)}"
            else:
                status = HealthStatus.HEALTHY
                message = "所有应用组件正常"
            
            response_time = time.time() - start_time
            
            health_check = HealthCheck(
                component="application",
                status=status,
                message=message,
                timestamp=datetime.now(),
                metrics=components_status,
                response_time=response_time
            )
            
            self.health_checks["application"] = health_check
            
        except Exception as e:
            self.logger.error(f"应用程序健康检查失败: {e}")
            self.health_checks["application"] = HealthCheck(
                component="application",
                status=HealthStatus.DOWN,
                message=f"检查失败: {str(e)}",
                timestamp=datetime.now(),
                metrics={},
                response_time=time.time() - start_time
            )
    
    async def _check_database_connection(self) -> None:
        """检查数据库连接"""
        start_time = time.time()
        
        try:
            # 模拟数据库连接检查
            connection_ok = True  # 实际应用中应该真正测试数据库连接
            
            if connection_ok:
                status = HealthStatus.HEALTHY
                message = "数据库连接正常"
            else:
                status = HealthStatus.CRITICAL
                message = "数据库连接失败"
            
            response_time = time.time() - start_time
            
            health_check = HealthCheck(
                component="database",
                status=status,
                message=message,
                timestamp=datetime.now(),
                metrics={'connection_time': response_time},
                response_time=response_time
            )
            
            self.health_checks["database"] = health_check
            
        except Exception as e:
            self.logger.error(f"数据库连接检查失败: {e}")
            self.health_checks["database"] = HealthCheck(
                component="database",
                status=HealthStatus.DOWN,
                message=f"检查失败: {str(e)}",
                timestamp=datetime.now(),
                metrics={},
                response_time=time.time() - start_time
            )
    
    async def _check_external_services(self) -> None:
        """检查外部服务"""
        start_time = time.time()
        
        try:
            # 模拟外部服务检查
            services_status = {
                'data_api': True,
                'model_service': True,
                'notification_service': True
            }
            
            failed_services = [svc for svc, status in services_status.items() if not status]
            
            if failed_services:
                status = HealthStatus.WARNING
                message = f"外部服务异常: {', '.join(failed_services)}"
            else:
                status = HealthStatus.HEALTHY
                message = "外部服务正常"
            
            response_time = time.time() - start_time
            
            health_check = HealthCheck(
                component="external_services",
                status=status,
                message=message,
                timestamp=datetime.now(),
                metrics=services_status,
                response_time=response_time
            )
            
            self.health_checks["external_services"] = health_check
            
        except Exception as e:
            self.logger.error(f"外部服务检查失败: {e}")
            self.health_checks["external_services"] = HealthCheck(
                component="external_services",
                status=HealthStatus.DOWN,
                message=f"检查失败: {str(e)}",
                timestamp=datetime.now(),
                metrics={},
                response_time=time.time() - start_time
            )
    
    async def _generate_alert(self, level: AlertLevel, component: str, message: str) -> None:
        """生成告警"""
        alert_id = f"alert_{int(time.time() * 1000)}"
        
        alert = Alert(
            alert_id=alert_id,
            level=level,
            component=component,
            message=message,
            timestamp=datetime.now()
        )
        
        self.alerts.append(alert)
        self.logger.warning(f"告警生成: [{level.value}] {component} - {message}")
        
        # 这里可以添加告警通知逻辑
        await self._send_alert_notification(alert)
    
    async def _send_alert_notification(self, alert: Alert) -> None:
        """发送告警通知"""
        # 模拟发送通知
        self.logger.info(f"发送告警通知: {alert.alert_id}")
    
    def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        if not self.health_checks:
            return {
                'overall_status': HealthStatus.DOWN.value,
                'message': '未进行健康检查',
                'components': {}
            }
        
        # 计算总体状态
        statuses = [check.status for check in self.health_checks.values()]
        
        if any(status == HealthStatus.DOWN for status in statuses):
            overall_status = HealthStatus.DOWN
        elif any(status == HealthStatus.CRITICAL for status in statuses):
            overall_status = HealthStatus.CRITICAL
        elif any(status == HealthStatus.WARNING for status in statuses):
            overall_status = HealthStatus.WARNING
        else:
            overall_status = HealthStatus.HEALTHY
        
        return {
            'overall_status': overall_status.value,
            'last_check': max(check.timestamp for check in self.health_checks.values()).isoformat(),
            'components': {
                name: {
                    'status': check.status.value,
                    'message': check.message,
                    'response_time': check.response_time,
                    'metrics': check.metrics
                }
                for name, check in self.health_checks.items()
            },
            'recent_alerts': [
                {
                    'alert_id': alert.alert_id,
                    'level': alert.level.value,
                    'component': alert.component,
                    'message': alert.message,
                    'timestamp': alert.timestamp.isoformat(),
                    'resolved': alert.resolved
                }
                for alert in self.alerts[-10:]  # 最近10个告警
            ]
        }


class SystemStabilityEnhancer:
    """系统稳定性增强器主类"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化系统稳定性增强器
        
        Args:
            config: 配置参数
        """
        self.logger = logging.getLogger(__name__)
        self.config = config or {}
        
        # 初始化组件
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=self.config.get('circuit_breaker_threshold', 5),
            recovery_timeout=self.config.get('circuit_breaker_timeout', 60)
        )
        
        self.retry_manager = RetryManager(
            max_retries=self.config.get('max_retries', 3),
            base_delay=self.config.get('base_delay', 1.0)
        )
        
        self.system_monitor = SystemMonitor(
            check_interval=self.config.get('monitor_interval', 30)
        )
        
        # 稳定性统计
        self.stability_stats = {
            'uptime_start': datetime.now(),
            'total_requests': 0,
            'failed_requests': 0,
            'circuit_breaker_trips': 0,
            'successful_retries': 0
        }
    
    async def start_stability_monitoring(self) -> None:
        """启动稳定性监控"""
        await self.system_monitor.start_monitoring()
        self.logger.info("系统稳定性监控已启动")
    
    async def stop_stability_monitoring(self) -> None:
        """停止稳定性监控"""
        await self.system_monitor.stop_monitoring()
        self.logger.info("系统稳定性监控已停止")
    
    async def execute_with_stability(self, func: Callable, *args, **kwargs) -> Any:
        """带稳定性保护的执行"""
        self.stability_stats['total_requests'] += 1
        
        try:
            # 使用熔断器和重试机制
            result = await self.retry_manager.retry_async(
                lambda: self.circuit_breaker.call(func, *args, **kwargs)
            )
            return result
            
        except Exception as e:
            self.stability_stats['failed_requests'] += 1
            
            if self.circuit_breaker.state == "OPEN":
                self.stability_stats['circuit_breaker_trips'] += 1
            
            self.logger.error(f"稳定性保护执行失败: {e}")
            raise e
    
    def get_stability_report(self) -> Dict[str, Any]:
        """获取稳定性报告"""
        uptime = datetime.now() - self.stability_stats['uptime_start']
        total_requests = self.stability_stats['total_requests']
        success_rate = ((total_requests - self.stability_stats['failed_requests']) / max(total_requests, 1)) * 100
        
        return {
            'uptime_hours': uptime.total_seconds() / 3600,
            'total_requests': total_requests,
            'success_rate': success_rate,
            'circuit_breaker_state': self.circuit_breaker.state,
            'circuit_breaker_trips': self.stability_stats['circuit_breaker_trips'],
            'system_health': self.system_monitor.get_system_health(),
            'stability_recommendations': self._get_stability_recommendations()
        }
    
    def _get_stability_recommendations(self) -> List[str]:
        """获取稳定性改进建议"""
        recommendations = []
        
        success_rate = ((self.stability_stats['total_requests'] - self.stability_stats['failed_requests']) / 
                       max(self.stability_stats['total_requests'], 1)) * 100
        
        if success_rate < 95:
            recommendations.append("系统成功率较低，建议检查错误原因")
        
        if self.stability_stats['circuit_breaker_trips'] > 5:
            recommendations.append("熔断器频繁触发，建议优化服务稳定性")
        
        system_health = self.system_monitor.get_system_health()
        if system_health['overall_status'] != 'healthy':
            recommendations.append("系统健康状态异常，建议立即检查")
        
        recommendations.extend([
            "定期备份重要数据",
            "建立灾难恢复计划",
            "实施负载均衡",
            "增加系统冗余"
        ])
        
        return recommendations
    
    async def perform_self_healing(self) -> Dict[str, Any]:
        """执行自愈操作"""
        healing_actions = []
        
        try:
            # 检查系统健康状态
            health = self.system_monitor.get_system_health()
            
            # 如果内存使用率过高，尝试清理
            for component_name, component in health['components'].items():
                if component_name == 'system_resources':
                    metrics = component.get('metrics', {})
                    memory_percent = metrics.get('memory_percent', 0)
                    
                    if memory_percent > 90:
                        # 执行内存清理
                        import gc
                        gc.collect()
                        healing_actions.append("执行垃圾回收清理内存")
            
            # 重置熔断器（如果适当）
            if self.circuit_breaker.state == "OPEN" and self.circuit_breaker._should_attempt_reset():
                self.circuit_breaker.state = "HALF_OPEN"
                healing_actions.append("重置熔断器状态")
            
            return {
                'success': True,
                'actions_taken': healing_actions,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"自愈操作失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
