#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户体验优化器
改进界面交互、错误处理、进度反馈等用户体验
"""

import asyncio
import logging
import json
import time
from typing import Dict, List, Any, Optional, Callable, AsyncGenerator, Tuple
from datetime import datetime
from dataclasses import dataclass
from enum import Enum
import traceback


class ProgressStatus(Enum):
    """进度状态"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class ProgressUpdate:
    """进度更新"""
    task_id: str
    task_name: str
    status: ProgressStatus
    progress: float  # 0-100
    message: str
    timestamp: datetime
    details: Optional[Dict[str, Any]] = None


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self):
        """初始化进度跟踪器"""
        self.logger = logging.getLogger(__name__)
        self.tasks: Dict[str, ProgressUpdate] = {}
        self.callbacks: List[Callable[[ProgressUpdate], None]] = []
    
    def create_task(self, task_id: str, task_name: str) -> None:
        """创建任务"""
        update = ProgressUpdate(
            task_id=task_id,
            task_name=task_name,
            status=ProgressStatus.NOT_STARTED,
            progress=0.0,
            message="任务已创建",
            timestamp=datetime.now()
        )
        self.tasks[task_id] = update
        self._notify_callbacks(update)
    
    def update_progress(self, task_id: str, progress: float, message: str, 
                       details: Optional[Dict[str, Any]] = None) -> None:
        """更新进度"""
        if task_id not in self.tasks:
            self.logger.warning(f"任务 {task_id} 不存在")
            return
        
        update = ProgressUpdate(
            task_id=task_id,
            task_name=self.tasks[task_id].task_name,
            status=ProgressStatus.IN_PROGRESS,
            progress=min(100.0, max(0.0, progress)),
            message=message,
            timestamp=datetime.now(),
            details=details
        )
        self.tasks[task_id] = update
        self._notify_callbacks(update)
    
    def complete_task(self, task_id: str, message: str = "任务完成") -> None:
        """完成任务"""
        if task_id not in self.tasks:
            self.logger.warning(f"任务 {task_id} 不存在")
            return
        
        update = ProgressUpdate(
            task_id=task_id,
            task_name=self.tasks[task_id].task_name,
            status=ProgressStatus.COMPLETED,
            progress=100.0,
            message=message,
            timestamp=datetime.now()
        )
        self.tasks[task_id] = update
        self._notify_callbacks(update)
    
    def fail_task(self, task_id: str, error_message: str) -> None:
        """任务失败"""
        if task_id not in self.tasks:
            self.logger.warning(f"任务 {task_id} 不存在")
            return
        
        update = ProgressUpdate(
            task_id=task_id,
            task_name=self.tasks[task_id].task_name,
            status=ProgressStatus.FAILED,
            progress=self.tasks[task_id].progress,
            message=f"任务失败: {error_message}",
            timestamp=datetime.now()
        )
        self.tasks[task_id] = update
        self._notify_callbacks(update)
    
    def add_callback(self, callback: Callable[[ProgressUpdate], None]) -> None:
        """添加回调函数"""
        self.callbacks.append(callback)
    
    def _notify_callbacks(self, update: ProgressUpdate) -> None:
        """通知回调函数"""
        for callback in self.callbacks:
            try:
                callback(update)
            except Exception as e:
                self.logger.error(f"回调函数执行失败: {e}")
    
    def get_task_status(self, task_id: str) -> Optional[ProgressUpdate]:
        """获取任务状态"""
        return self.tasks.get(task_id)
    
    def get_all_tasks(self) -> Dict[str, ProgressUpdate]:
        """获取所有任务"""
        return self.tasks.copy()


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self):
        """初始化错误处理器"""
        self.logger = logging.getLogger(__name__)
        self.error_history: List[Dict[str, Any]] = []
        self.error_patterns: Dict[str, str] = {
            'ConnectionError': '网络连接错误，请检查网络设置',
            'TimeoutError': '请求超时，请稍后重试',
            'ValueError': '数据格式错误，请检查输入数据',
            'KeyError': '缺少必要的数据字段',
            'ImportError': '缺少必要的依赖包',
            'MemoryError': '内存不足，请关闭其他程序或减少数据量',
            'FileNotFoundError': '文件未找到，请检查文件路径'
        }
    
    def handle_error(self, error: Exception, context: str = "") -> Dict[str, Any]:
        """处理错误"""
        error_type = type(error).__name__
        error_message = str(error)
        
        # 记录错误
        error_record = {
            'timestamp': datetime.now().isoformat(),
            'error_type': error_type,
            'error_message': error_message,
            'context': context,
            'traceback': traceback.format_exc()
        }
        self.error_history.append(error_record)
        
        # 生成用户友好的错误信息
        user_message = self.error_patterns.get(error_type, f"发生未知错误: {error_message}")
        
        # 提供解决建议
        suggestions = self._get_error_suggestions(error_type, error_message)
        
        return {
            'error_type': error_type,
            'user_message': user_message,
            'suggestions': suggestions,
            'error_id': len(self.error_history),
            'timestamp': error_record['timestamp'],
            'recoverable': self._is_recoverable_error(error_type)
        }
    
    def _get_error_suggestions(self, error_type: str, error_message: str) -> List[str]:
        """获取错误解决建议"""
        suggestions = []
        
        if error_type == 'ConnectionError':
            suggestions.extend([
                "检查网络连接是否正常",
                "尝试重新启动网络适配器",
                "检查防火墙设置"
            ])
        elif error_type == 'TimeoutError':
            suggestions.extend([
                "稍后重试",
                "检查网络速度",
                "减少请求数据量"
            ])
        elif error_type == 'ValueError':
            suggestions.extend([
                "检查输入数据格式",
                "确保数值在有效范围内",
                "检查数据类型是否正确"
            ])
        elif error_type == 'MemoryError':
            suggestions.extend([
                "关闭其他程序释放内存",
                "减少处理的数据量",
                "重启应用程序"
            ])
        else:
            suggestions.extend([
                "重试操作",
                "检查输入参数",
                "联系技术支持"
            ])
        
        return suggestions
    
    def _is_recoverable_error(self, error_type: str) -> bool:
        """判断错误是否可恢复"""
        recoverable_errors = {
            'ConnectionError', 'TimeoutError', 'ValueError', 
            'KeyError', 'FileNotFoundError'
        }
        return error_type in recoverable_errors
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计"""
        if not self.error_history:
            return {'total_errors': 0}
        
        error_types = {}
        for error in self.error_history:
            error_type = error['error_type']
            error_types[error_type] = error_types.get(error_type, 0) + 1
        
        return {
            'total_errors': len(self.error_history),
            'error_types': error_types,
            'most_common_error': max(error_types.items(), key=lambda x: x[1])[0] if error_types else None,
            'recent_errors': self.error_history[-5:]  # 最近5个错误
        }


class UserFeedbackCollector:
    """用户反馈收集器"""
    
    def __init__(self):
        """初始化反馈收集器"""
        self.logger = logging.getLogger(__name__)
        self.feedback_history: List[Dict[str, Any]] = []
    
    def collect_feedback(self, user_id: str, feedback_type: str, 
                        content: str, rating: Optional[int] = None) -> str:
        """收集用户反馈"""
        feedback_id = f"feedback_{len(self.feedback_history) + 1}"
        
        feedback = {
            'feedback_id': feedback_id,
            'user_id': user_id,
            'feedback_type': feedback_type,  # 'bug', 'suggestion', 'compliment', 'complaint'
            'content': content,
            'rating': rating,  # 1-5星评分
            'timestamp': datetime.now().isoformat(),
            'status': 'new'
        }
        
        self.feedback_history.append(feedback)
        self.logger.info(f"收到用户反馈: {feedback_id}")
        
        return feedback_id
    
    def get_feedback_summary(self) -> Dict[str, Any]:
        """获取反馈摘要"""
        if not self.feedback_history:
            return {'total_feedback': 0}
        
        feedback_types = {}
        ratings = []
        
        for feedback in self.feedback_history:
            feedback_type = feedback['feedback_type']
            feedback_types[feedback_type] = feedback_types.get(feedback_type, 0) + 1
            
            if feedback['rating']:
                ratings.append(feedback['rating'])
        
        average_rating = sum(ratings) / len(ratings) if ratings else 0
        
        return {
            'total_feedback': len(self.feedback_history),
            'feedback_types': feedback_types,
            'average_rating': round(average_rating, 2),
            'recent_feedback': self.feedback_history[-3:]  # 最近3个反馈
        }


class UserExperienceOptimizer:
    """用户体验优化器主类"""
    
    def __init__(self):
        """初始化用户体验优化器"""
        self.logger = logging.getLogger(__name__)
        self.progress_tracker = ProgressTracker()
        self.error_handler = ErrorHandler()
        self.feedback_collector = UserFeedbackCollector()
        
        # 设置进度回调
        self.progress_tracker.add_callback(self._log_progress_update)
        
        # UX统计
        self.ux_stats = {
            'total_operations': 0,
            'successful_operations': 0,
            'user_satisfaction_score': 0.0,
            'average_response_time': 0.0
        }
    
    def _log_progress_update(self, update: ProgressUpdate) -> None:
        """记录进度更新"""
        self.logger.info(f"任务 {update.task_name}: {update.progress:.1f}% - {update.message}")
    
    async def execute_with_progress(self, task_name: str, task_func: Callable, 
                                  *args, **kwargs) -> Any:
        """带进度跟踪的任务执行"""
        task_id = f"task_{int(time.time() * 1000)}"
        start_time = time.time()
        
        try:
            # 创建任务
            self.progress_tracker.create_task(task_id, task_name)
            self.ux_stats['total_operations'] += 1
            
            # 执行任务
            if asyncio.iscoroutinefunction(task_func):
                result = await task_func(*args, **kwargs)
            else:
                result = task_func(*args, **kwargs)
            
            # 完成任务
            self.progress_tracker.complete_task(task_id, "任务成功完成")
            self.ux_stats['successful_operations'] += 1
            
            # 更新响应时间统计
            response_time = time.time() - start_time
            self._update_response_time_stats(response_time)
            
            return result
            
        except Exception as e:
            # 处理错误
            error_info = self.error_handler.handle_error(e, f"执行任务: {task_name}")
            self.progress_tracker.fail_task(task_id, error_info['user_message'])
            
            # 如果错误可恢复，提供重试选项
            if error_info['recoverable']:
                self.logger.info(f"任务 {task_name} 失败但可重试")
            
            raise e
    
    async def execute_with_streaming_progress(self, task_name: str, 
                                            progress_generator: AsyncGenerator[Tuple[float, str], None]) -> AsyncGenerator[Dict[str, Any], None]:
        """流式进度执行"""
        task_id = f"stream_task_{int(time.time() * 1000)}"
        
        try:
            self.progress_tracker.create_task(task_id, task_name)
            
            async for progress, message in progress_generator:
                self.progress_tracker.update_progress(task_id, progress, message)
                
                yield {
                    'task_id': task_id,
                    'progress': progress,
                    'message': message,
                    'timestamp': datetime.now().isoformat()
                }
            
            self.progress_tracker.complete_task(task_id)
            
        except Exception as e:
            error_info = self.error_handler.handle_error(e, f"流式任务: {task_name}")
            self.progress_tracker.fail_task(task_id, error_info['user_message'])
            
            yield {
                'task_id': task_id,
                'error': error_info,
                'timestamp': datetime.now().isoformat()
            }
    
    def create_user_friendly_response(self, data: Any, success: bool = True, 
                                    message: str = "") -> Dict[str, Any]:
        """创建用户友好的响应"""
        response = {
            'success': success,
            'timestamp': datetime.now().isoformat(),
            'data': data if success else None,
            'message': message
        }
        
        if not success and isinstance(data, Exception):
            error_info = self.error_handler.handle_error(data)
            response.update({
                'error': error_info,
                'suggestions': error_info['suggestions']
            })
        
        return response
    
    def validate_user_input(self, input_data: Dict[str, Any], 
                          required_fields: List[str]) -> Tuple[bool, List[str]]:
        """验证用户输入"""
        errors = []
        
        # 检查必填字段
        for field in required_fields:
            if field not in input_data or input_data[field] is None:
                errors.append(f"缺少必填字段: {field}")
        
        # 检查数据类型
        type_checks = {
            'symbol': str,
            'amount': (int, float),
            'date': str,
            'enabled': bool
        }
        
        for field, expected_type in type_checks.items():
            if field in input_data and not isinstance(input_data[field], expected_type):
                errors.append(f"字段 {field} 类型错误，期望 {expected_type.__name__}")
        
        # 检查股票代码格式
        if 'symbol' in input_data:
            symbol = input_data['symbol']
            if not isinstance(symbol, str) or len(symbol) != 6 or not symbol.isdigit():
                errors.append("股票代码格式错误，应为6位数字")
        
        return len(errors) == 0, errors
    
    def _update_response_time_stats(self, response_time: float) -> None:
        """更新响应时间统计"""
        total_ops = self.ux_stats['total_operations']
        current_avg = self.ux_stats['average_response_time']
        new_avg = (current_avg * (total_ops - 1) + response_time) / total_ops
        self.ux_stats['average_response_time'] = new_avg
    
    def get_user_experience_metrics(self) -> Dict[str, Any]:
        """获取用户体验指标"""
        total_ops = self.ux_stats['total_operations']
        success_rate = (self.ux_stats['successful_operations'] / max(total_ops, 1)) * 100
        
        # 计算用户满意度评分
        feedback_summary = self.feedback_collector.get_feedback_summary()
        satisfaction_score = feedback_summary.get('average_rating', 3.0) * 20  # 转换为100分制
        
        return {
            'total_operations': total_ops,
            'success_rate': success_rate,
            'average_response_time': self.ux_stats['average_response_time'],
            'user_satisfaction_score': satisfaction_score,
            'error_statistics': self.error_handler.get_error_statistics(),
            'feedback_summary': feedback_summary,
            'active_tasks': len([t for t in self.progress_tracker.get_all_tasks().values() 
                               if t.status == ProgressStatus.IN_PROGRESS]),
            'ux_recommendations': self._get_ux_recommendations()
        }
    
    def _get_ux_recommendations(self) -> List[str]:
        """获取用户体验改进建议"""
        recommendations = []
        
        success_rate = (self.ux_stats['successful_operations'] / max(self.ux_stats['total_operations'], 1)) * 100
        
        if success_rate < 90:
            recommendations.append("提高系统稳定性，减少错误发生")
        
        if self.ux_stats['average_response_time'] > 5.0:
            recommendations.append("优化系统性能，减少响应时间")
        
        error_stats = self.error_handler.get_error_statistics()
        if error_stats.get('total_errors', 0) > 10:
            recommendations.append("加强错误处理和用户指导")
        
        feedback_summary = self.feedback_collector.get_feedback_summary()
        if feedback_summary.get('average_rating', 3.0) < 4.0:
            recommendations.append("改进用户界面和交互体验")
        
        recommendations.extend([
            "增加更多用户帮助和文档",
            "实现更智能的错误恢复机制",
            "提供更详细的操作反馈",
            "优化移动端体验"
        ])
        
        return recommendations
    
    async def simulate_progress_task(self, task_name: str, duration: float = 5.0) -> str:
        """模拟进度任务（用于演示）"""
        task_id = f"demo_task_{int(time.time() * 1000)}"
        
        try:
            self.progress_tracker.create_task(task_id, task_name)
            
            steps = 10
            for i in range(steps + 1):
                progress = (i / steps) * 100
                message = f"正在执行步骤 {i}/{steps}"
                
                self.progress_tracker.update_progress(task_id, progress, message)
                await asyncio.sleep(duration / steps)
            
            self.progress_tracker.complete_task(task_id, "演示任务完成")
            return f"任务 {task_name} 成功完成"
            
        except Exception as e:
            error_info = self.error_handler.handle_error(e, task_name)
            self.progress_tracker.fail_task(task_id, error_info['user_message'])
            raise e
