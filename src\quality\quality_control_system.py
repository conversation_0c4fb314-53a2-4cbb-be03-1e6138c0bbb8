#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
质量控制系统
负责报告质量检查和评分
"""

import logging
import re
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path


class QualityControlSystem:
    """质量控制系统"""
    
    def __init__(self):
        """初始化质量控制系统"""
        self.logger = logging.getLogger(__name__)
        
        # 质量评分权重
        self.quality_weights = {
            'data_quality': 0.25,      # 数据质量
            'content_completeness': 0.25,  # 内容完整性
            'structure_compliance': 0.20,  # 结构规范性
            'professional_level': 0.15,    # 专业水平
            'chart_quality': 0.15      # 图表质量
        }
        
        # 质量标准
        self.quality_standards = {
            'min_word_count': 5000,
            'min_sections': 5,
            'required_charts': 3,
            'data_freshness_days': 30
        }
    
    def audit_report(self, report_data: Dict[str, Any], report_type: str) -> Dict[str, Any]:
        """
        审核报告质量
        
        Args:
            report_data: 报告数据
            report_type: 报告类型 (company/industry/macro)
            
        Returns:
            质量审核结果
        """
        try:
            self.logger.info(f"开始审核 {report_type} 报告质量...")
            
            audit_result = {
                'report_type': report_type,
                'audit_time': datetime.now().isoformat(),
                'overall_score': 0,
                'dimension_scores': {},
                'quality_issues': [],
                'improvement_suggestions': [],
                'compliance_check': {},
                'passed': False
            }
            
            # 各维度质量检查
            audit_result['dimension_scores']['data_quality'] = self._check_data_quality(report_data)
            audit_result['dimension_scores']['content_completeness'] = self._check_content_completeness(
                report_data, report_type
            )
            audit_result['dimension_scores']['structure_compliance'] = self._check_structure_compliance(
                report_data, report_type
            )
            audit_result['dimension_scores']['professional_level'] = self._check_professional_level(report_data)
            audit_result['dimension_scores']['chart_quality'] = self._check_chart_quality(report_data)
            
            # 计算总分
            audit_result['overall_score'] = self._calculate_overall_score(
                audit_result['dimension_scores']
            )
            
            # 识别质量问题
            audit_result['quality_issues'] = self._identify_quality_issues(
                report_data, audit_result['dimension_scores']
            )
            
            # 生成改进建议
            audit_result['improvement_suggestions'] = self._generate_improvement_suggestions(
                audit_result['quality_issues']
            )
            
            # 合规性检查
            audit_result['compliance_check'] = self._check_compliance(report_data, report_type)
            
            # 判断是否通过
            audit_result['passed'] = audit_result['overall_score'] >= 80
            
            self.logger.info(f"{report_type} 报告质量审核完成，总分: {audit_result['overall_score']:.1f}")
            return audit_result
            
        except Exception as e:
            self.logger.error(f"审核 {report_type} 报告质量失败: {e}")
            return {
                'error': str(e),
                'overall_score': 0,
                'passed': False
            }
    
    def batch_audit(self, reports: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量审核报告
        
        Args:
            reports: 报告列表
            
        Returns:
            批量审核结果
        """
        try:
            self.logger.info(f"开始批量审核 {len(reports)} 份报告...")
            
            batch_result = {
                'total_reports': len(reports),
                'audit_time': datetime.now().isoformat(),
                'individual_results': [],
                'summary_statistics': {},
                'overall_quality_level': 'unknown'
            }
            
            scores = []
            passed_count = 0
            
            # 逐个审核
            for i, report in enumerate(reports):
                report_type = report.get('report_type', 'unknown')
                audit_result = self.audit_report(report, report_type)
                
                batch_result['individual_results'].append(audit_result)
                
                if not audit_result.get('error'):
                    scores.append(audit_result['overall_score'])
                    if audit_result['passed']:
                        passed_count += 1
            
            # 统计信息
            if scores:
                batch_result['summary_statistics'] = {
                    'average_score': sum(scores) / len(scores),
                    'max_score': max(scores),
                    'min_score': min(scores),
                    'pass_rate': passed_count / len(reports) * 100,
                    'passed_reports': passed_count,
                    'failed_reports': len(reports) - passed_count
                }
                
                # 整体质量水平
                avg_score = batch_result['summary_statistics']['average_score']
                if avg_score >= 90:
                    batch_result['overall_quality_level'] = 'excellent'
                elif avg_score >= 80:
                    batch_result['overall_quality_level'] = 'good'
                elif avg_score >= 70:
                    batch_result['overall_quality_level'] = 'fair'
                else:
                    batch_result['overall_quality_level'] = 'poor'
            
            self.logger.info(f"批量审核完成，平均分: {batch_result['summary_statistics'].get('average_score', 0):.1f}")
            return batch_result
            
        except Exception as e:
            self.logger.error(f"批量审核失败: {e}")
            return {'error': str(e)}
    
    def _check_data_quality(self, report_data: Dict[str, Any]) -> float:
        """检查数据质量"""
        try:
            score = 100.0
            
            # 检查数据完整性
            if not report_data.get('sections'):
                score -= 30
            
            # 检查数据时效性
            generation_time = report_data.get('generation_time')
            if generation_time:
                try:
                    gen_time = datetime.fromisoformat(generation_time.replace('Z', '+00:00'))
                    days_old = (datetime.now() - gen_time).days
                    if days_old > self.quality_standards['data_freshness_days']:
                        score -= 20
                except:
                    score -= 10
            
            # 检查数据来源
            charts = report_data.get('charts', [])
            if not charts:
                score -= 20
            
            # 检查错误数据
            if report_data.get('error'):
                score -= 50
            
            return max(0, score)
            
        except Exception as e:
            self.logger.warning(f"检查数据质量失败: {e}")
            return 0
    
    def _check_content_completeness(self, report_data: Dict[str, Any], report_type: str) -> float:
        """检查内容完整性"""
        try:
            score = 100.0
            sections = report_data.get('sections', {})
            
            # 必需章节检查
            required_sections = {
                'company': ['executive_summary', 'company_overview', 'financial_analysis', 
                           'valuation_analysis', 'investment_recommendation'],
                'industry': ['executive_summary', 'industry_overview', 'market_structure',
                            'competitive_landscape', 'investment_opportunities'],
                'macro': ['executive_summary', 'economic_overview', 'gdp_analysis',
                         'inflation_analysis', 'market_outlook']
            }
            
            required = required_sections.get(report_type, [])
            missing_sections = []
            
            for section in required:
                if not sections.get(section):
                    missing_sections.append(section)
                    score -= 100 / len(required)
            
            # 内容长度检查
            total_content_length = 0
            for section_content in sections.values():
                if isinstance(section_content, str):
                    total_content_length += len(section_content)
            
            if total_content_length < self.quality_standards['min_word_count']:
                score -= 20
            
            return max(0, score)
            
        except Exception as e:
            self.logger.warning(f"检查内容完整性失败: {e}")
            return 0
    
    def _check_structure_compliance(self, report_data: Dict[str, Any], report_type: str) -> float:
        """检查结构规范性"""
        try:
            score = 100.0
            
            # 检查标题
            if not report_data.get('title'):
                score -= 20
            
            # 检查元数据
            metadata = report_data.get('metadata', {})
            if not metadata.get('analyst'):
                score -= 10
            if not metadata.get('report_date'):
                score -= 10
            
            # 检查章节结构
            sections = report_data.get('sections', {})
            if len(sections) < self.quality_standards['min_sections']:
                score -= 30
            
            # 检查报告类型一致性
            if report_data.get('report_type') != report_type:
                score -= 15
            
            return max(0, score)
            
        except Exception as e:
            self.logger.warning(f"检查结构规范性失败: {e}")
            return 0
    
    def _check_professional_level(self, report_data: Dict[str, Any]) -> float:
        """检查专业水平"""
        try:
            score = 100.0
            sections = report_data.get('sections', {})
            
            # 检查专业术语使用
            professional_terms = [
                'ROE', 'ROA', 'PE', 'PB', 'DCF', '净利润率', '资产负债率',
                '现金流', '估值', '投资', '风险', '收益'
            ]
            
            total_content = ' '.join([str(content) for content in sections.values()])
            term_count = sum(1 for term in professional_terms if term in total_content)
            
            if term_count < 5:
                score -= 30
            
            # 检查分析深度
            analysis_keywords = ['分析', '评估', '预测', '建议', '风险', '机会']
            analysis_count = sum(total_content.count(keyword) for keyword in analysis_keywords)
            
            if analysis_count < 10:
                score -= 20
            
            # 检查数据支撑
            if '数据' not in total_content and '指标' not in total_content:
                score -= 25
            
            return max(0, score)
            
        except Exception as e:
            self.logger.warning(f"检查专业水平失败: {e}")
            return 0
    
    def _check_chart_quality(self, report_data: Dict[str, Any]) -> float:
        """检查图表质量"""
        try:
            score = 100.0
            charts = report_data.get('charts', [])
            
            # 图表数量检查
            if len(charts) < self.quality_standards['required_charts']:
                score -= 40
            
            # 图表类型多样性
            chart_types = set()
            for chart in charts:
                chart_type = chart.get('type')
                if chart_type:
                    chart_types.add(chart_type)
            
            if len(chart_types) < 2:
                score -= 20
            
            # 图表标题检查
            charts_without_title = [c for c in charts if not c.get('title')]
            if charts_without_title:
                score -= len(charts_without_title) * 10
            
            return max(0, score)
            
        except Exception as e:
            self.logger.warning(f"检查图表质量失败: {e}")
            return 0
    
    def _calculate_overall_score(self, dimension_scores: Dict[str, float]) -> float:
        """计算总体评分"""
        try:
            total_score = 0
            for dimension, score in dimension_scores.items():
                weight = self.quality_weights.get(dimension, 0)
                total_score += score * weight
            
            return round(total_score, 1)
            
        except Exception as e:
            self.logger.warning(f"计算总体评分失败: {e}")
            return 0
    
    def _identify_quality_issues(self, report_data: Dict[str, Any], 
                               dimension_scores: Dict[str, float]) -> List[str]:
        """识别质量问题"""
        issues = []
        
        # 基于评分识别问题
        for dimension, score in dimension_scores.items():
            if score < 70:
                issue_map = {
                    'data_quality': '数据质量不达标',
                    'content_completeness': '内容不够完整',
                    'structure_compliance': '结构不够规范',
                    'professional_level': '专业水平有待提高',
                    'chart_quality': '图表质量需要改善'
                }
                issues.append(issue_map.get(dimension, f'{dimension}评分偏低'))
        
        # 具体问题检查
        if not report_data.get('charts'):
            issues.append('缺少图表支撑')
        
        if not report_data.get('metadata', {}).get('analyst'):
            issues.append('缺少分析师信息')
        
        sections = report_data.get('sections', {})
        if len(sections) < 5:
            issues.append('报告章节过少')
        
        return issues
    
    def _generate_improvement_suggestions(self, quality_issues: List[str]) -> List[str]:
        """生成改进建议"""
        suggestions = []
        
        suggestion_map = {
            '数据质量不达标': '建议使用更权威的数据源，确保数据准确性和时效性',
            '内容不够完整': '建议补充缺失的章节内容，增加分析深度',
            '结构不够规范': '建议按照标准报告格式组织内容结构',
            '专业水平有待提高': '建议增加专业术语使用，提升分析深度',
            '图表质量需要改善': '建议增加图表数量，丰富图表类型',
            '缺少图表支撑': '建议添加相关图表以增强报告可读性',
            '缺少分析师信息': '建议完善报告元数据信息',
            '报告章节过少': '建议按照标准格式增加必要章节'
        }
        
        for issue in quality_issues:
            suggestion = suggestion_map.get(issue, f'建议改进: {issue}')
            suggestions.append(suggestion)
        
        return suggestions
    
    def _check_compliance(self, report_data: Dict[str, Any], report_type: str) -> Dict[str, Any]:
        """合规性检查"""
        try:
            compliance = {
                'format_compliance': True,
                'content_compliance': True,
                'structure_compliance': True,
                'issues': []
            }
            
            # 格式合规性
            if not report_data.get('title'):
                compliance['format_compliance'] = False
                compliance['issues'].append('缺少报告标题')
            
            # 内容合规性
            sections = report_data.get('sections', {})
            if not sections.get('executive_summary'):
                compliance['content_compliance'] = False
                compliance['issues'].append('缺少执行摘要')
            
            # 结构合规性
            if len(sections) < 3:
                compliance['structure_compliance'] = False
                compliance['issues'].append('报告结构不完整')
            
            return compliance
            
        except Exception as e:
            self.logger.warning(f"合规性检查失败: {e}")
            return {'error': str(e)}
