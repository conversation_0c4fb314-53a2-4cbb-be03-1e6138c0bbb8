#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强向量存储系统
专门针对金融多模态数据优化，支持文本、图表、财务数据的统一向量化存储和检索
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple, Union
from pathlib import Path
import json
import pickle
import sqlite3
from dataclasses import dataclass, asdict
from enum import Enum
import hashlib

class DataModalityType(Enum):
    """数据模态类型"""
    TEXT = "text"
    FINANCIAL_DATA = "financial_data"
    CHART_DATA = "chart_data"
    TABLE_DATA = "table_data"
    TIME_SERIES = "time_series"
    METADATA = "metadata"

@dataclass
class VectorDocument:
    """向量文档结构"""
    doc_id: str
    title: str
    content: str
    modality_type: DataModalityType
    embedding: Optional[np.ndarray]
    metadata: Dict[str, Any]
    financial_metrics: Dict[str, Any]
    timestamp: datetime
    source: str
    quality_score: float
    access_count: int = 0
    relevance_score: float = 0.0

@dataclass
class SearchResult:
    """搜索结果结构"""
    document: VectorDocument
    similarity_score: float
    relevance_factors: Dict[str, float]
    retrieval_context: Dict[str, Any]

class EnhancedVectorStore:
    """增强向量存储系统"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化增强向量存储
        
        Args:
            config: 配置参数
        """
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # 存储配置
        self.dimension = config.get('vector_dimension', 768)
        self.storage_path = Path(config.get('storage_path', './storage/vectorized_data'))
        self.storage_path.mkdir(parents=True, exist_ok=True)
        
        # 内存存储
        self.documents: Dict[str, VectorDocument] = {}
        self.vectors: Dict[str, np.ndarray] = {}
        self.modality_indices: Dict[DataModalityType, List[str]] = {
            modality: [] for modality in DataModalityType
        }
        
        # 持久化存储
        self.db_path = self.storage_path / "vector_store.db"
        self._init_database()
        
        # 索引和缓存
        self.similarity_cache: Dict[str, List[Tuple[str, float]]] = {}
        self.cache_ttl = config.get('cache_ttl', 3600)  # 1小时缓存
        
        # 统计信息
        self.stats = {
            'total_documents': 0,
            'total_searches': 0,
            'cache_hits': 0,
            'modality_distribution': {modality.value: 0 for modality in DataModalityType},
            'average_quality_score': 0.0,
            'last_updated': datetime.now()
        }
        
        # 加载已有数据
        self._load_from_storage()

    def _init_database(self):
        """初始化数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 创建文档表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS documents (
                        doc_id TEXT PRIMARY KEY,
                        title TEXT NOT NULL,
                        content TEXT NOT NULL,
                        modality_type TEXT NOT NULL,
                        metadata TEXT,
                        financial_metrics TEXT,
                        timestamp TEXT NOT NULL,
                        source TEXT NOT NULL,
                        quality_score REAL NOT NULL,
                        access_count INTEGER DEFAULT 0,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建向量表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS vectors (
                        doc_id TEXT PRIMARY KEY,
                        vector_data BLOB NOT NULL,
                        dimension INTEGER NOT NULL,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (doc_id) REFERENCES documents (doc_id)
                    )
                ''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_modality ON documents(modality_type)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_timestamp ON documents(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_quality ON documents(quality_score)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_source ON documents(source)')
                
                conn.commit()
                self.logger.info("数据库初始化完成")
                
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")

    def add_document(self, document: VectorDocument) -> bool:
        """
        添加文档到向量存储
        
        Args:
            document: 向量文档
            
        Returns:
            是否添加成功
        """
        try:
            if document.embedding is None:
                self.logger.warning(f"文档 {document.doc_id} 缺少向量嵌入")
                return False
            
            # 添加到内存存储
            self.documents[document.doc_id] = document
            self.vectors[document.doc_id] = document.embedding
            
            # 更新模态索引
            self.modality_indices[document.modality_type].append(document.doc_id)
            
            # 持久化到数据库
            self._save_document_to_db(document)
            
            # 更新统计信息
            self.stats['total_documents'] += 1
            self.stats['modality_distribution'][document.modality_type.value] += 1
            self._update_average_quality_score()
            self.stats['last_updated'] = datetime.now()
            
            # 清除相关缓存
            self._clear_similarity_cache()
            
            self.logger.info(f"文档已添加: {document.doc_id} ({document.modality_type.value})")
            return True
            
        except Exception as e:
            self.logger.error(f"添加文档失败 {document.doc_id}: {e}")
            return False

    def search_similar(self, query_vector: np.ndarray, top_k: int = 10,
                      modality_filter: Optional[DataModalityType] = None,
                      quality_threshold: float = 0.0,
                      time_range: Optional[Tuple[datetime, datetime]] = None) -> List[SearchResult]:
        """
        搜索相似文档
        
        Args:
            query_vector: 查询向量
            top_k: 返回文档数量
            modality_filter: 模态类型过滤
            quality_threshold: 质量阈值
            time_range: 时间范围过滤
            
        Returns:
            搜索结果列表
        """
        try:
            self.stats['total_searches'] += 1
            
            # 生成缓存键
            cache_key = self._generate_cache_key(query_vector, top_k, modality_filter, quality_threshold, time_range)
            
            # 检查缓存
            if cache_key in self.similarity_cache:
                cached_results = self.similarity_cache[cache_key]
                if self._is_cache_valid(cache_key):
                    self.stats['cache_hits'] += 1
                    return self._build_search_results(cached_results)
            
            # 执行搜索
            similarities = []
            
            # 确定搜索范围
            search_doc_ids = self._get_search_candidates(modality_filter, quality_threshold, time_range)
            
            # 计算相似度
            for doc_id in search_doc_ids:
                if doc_id in self.vectors:
                    vector = self.vectors[doc_id]
                    similarity = self._calculate_similarity(query_vector, vector)
                    similarities.append((doc_id, similarity))
            
            # 排序并取前k个
            similarities.sort(key=lambda x: x[1], reverse=True)
            top_similarities = similarities[:top_k]
            
            # 缓存结果
            self.similarity_cache[cache_key] = {
                'results': top_similarities,
                'timestamp': datetime.now()
            }
            
            return self._build_search_results(top_similarities)
            
        except Exception as e:
            self.logger.error(f"搜索失败: {e}")
            return []

    def search_by_financial_metrics(self, metric_filters: Dict[str, Any], top_k: int = 10) -> List[SearchResult]:
        """
        基于财务指标搜索文档
        
        Args:
            metric_filters: 财务指标过滤条件
            top_k: 返回文档数量
            
        Returns:
            搜索结果列表
        """
        try:
            matching_docs = []
            
            for doc_id, document in self.documents.items():
                if document.modality_type != DataModalityType.FINANCIAL_DATA:
                    continue
                
                # 检查财务指标匹配
                match_score = self._calculate_metric_match_score(document.financial_metrics, metric_filters)
                if match_score > 0:
                    matching_docs.append((doc_id, match_score))
            
            # 排序并返回
            matching_docs.sort(key=lambda x: x[1], reverse=True)
            top_matches = matching_docs[:top_k]
            
            return self._build_search_results(top_matches)
            
        except Exception as e:
            self.logger.error(f"财务指标搜索失败: {e}")
            return []

    def search_multimodal(self, query_vector: np.ndarray, text_query: str = "",
                         financial_filters: Dict[str, Any] = None,
                         top_k: int = 10) -> List[SearchResult]:
        """
        多模态搜索
        
        Args:
            query_vector: 查询向量
            text_query: 文本查询
            financial_filters: 财务过滤条件
            top_k: 返回文档数量
            
        Returns:
            搜索结果列表
        """
        try:
            # 向量相似度搜索
            vector_results = self.search_similar(query_vector, top_k * 2)
            
            # 文本匹配搜索
            text_results = self._search_by_text(text_query, top_k * 2) if text_query else []
            
            # 财务指标搜索
            financial_results = self.search_by_financial_metrics(financial_filters, top_k * 2) if financial_filters else []
            
            # 合并和重排序
            combined_results = self._combine_multimodal_results(vector_results, text_results, financial_results)
            
            return combined_results[:top_k]
            
        except Exception as e:
            self.logger.error(f"多模态搜索失败: {e}")
            return []

    def get_document(self, doc_id: str) -> Optional[VectorDocument]:
        """获取文档"""
        document = self.documents.get(doc_id)
        if document:
            document.access_count += 1
            self._update_document_access_count(doc_id)
        return document

    def remove_document(self, doc_id: str) -> bool:
        """移除文档"""
        try:
            if doc_id not in self.documents:
                return False
            
            document = self.documents[doc_id]
            
            # 从内存移除
            del self.documents[doc_id]
            del self.vectors[doc_id]
            
            # 从模态索引移除
            if doc_id in self.modality_indices[document.modality_type]:
                self.modality_indices[document.modality_type].remove(doc_id)
            
            # 从数据库移除
            self._remove_document_from_db(doc_id)
            
            # 更新统计信息
            self.stats['total_documents'] -= 1
            self.stats['modality_distribution'][document.modality_type.value] -= 1
            self._update_average_quality_score()
            
            # 清除缓存
            self._clear_similarity_cache()
            
            self.logger.info(f"文档已移除: {doc_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"移除文档失败 {doc_id}: {e}")
            return False

    def _get_search_candidates(self, modality_filter: Optional[DataModalityType],
                              quality_threshold: float, time_range: Optional[Tuple[datetime, datetime]]) -> List[str]:
        """获取搜索候选文档"""
        candidates = []

        # 模态过滤
        if modality_filter:
            candidates = self.modality_indices[modality_filter].copy()
        else:
            candidates = list(self.documents.keys())

        # 质量过滤
        if quality_threshold > 0:
            candidates = [doc_id for doc_id in candidates
                         if self.documents[doc_id].quality_score >= quality_threshold]

        # 时间过滤
        if time_range:
            start_time, end_time = time_range
            candidates = [doc_id for doc_id in candidates
                         if start_time <= self.documents[doc_id].timestamp <= end_time]

        return candidates

    def _calculate_similarity(self, vector1: np.ndarray, vector2: np.ndarray) -> float:
        """计算向量相似度"""
        try:
            # 余弦相似度
            dot_product = np.dot(vector1, vector2)
            norm1 = np.linalg.norm(vector1)
            norm2 = np.linalg.norm(vector2)

            if norm1 == 0 or norm2 == 0:
                return 0.0

            return float(dot_product / (norm1 * norm2))
        except Exception as e:
            self.logger.warning(f"相似度计算失败: {e}")
            return 0.0

    def _calculate_metric_match_score(self, doc_metrics: Dict[str, Any], filters: Dict[str, Any]) -> float:
        """计算财务指标匹配分数"""
        if not doc_metrics or not filters:
            return 0.0

        total_score = 0.0
        matched_metrics = 0

        for metric_name, filter_value in filters.items():
            if metric_name in doc_metrics:
                doc_value = doc_metrics[metric_name]

                # 数值范围匹配
                if isinstance(filter_value, dict) and 'min' in filter_value and 'max' in filter_value:
                    if filter_value['min'] <= doc_value <= filter_value['max']:
                        total_score += 1.0
                        matched_metrics += 1
                # 精确匹配
                elif doc_value == filter_value:
                    total_score += 1.0
                    matched_metrics += 1
                # 数值相似度匹配
                elif isinstance(doc_value, (int, float)) and isinstance(filter_value, (int, float)):
                    similarity = 1.0 - abs(doc_value - filter_value) / max(abs(doc_value), abs(filter_value), 1)
                    total_score += max(0, similarity)
                    matched_metrics += 1

        return total_score / len(filters) if filters else 0.0

    def _search_by_text(self, text_query: str, top_k: int) -> List[Tuple[str, float]]:
        """基于文本搜索"""
        query_words = set(text_query.lower().split())
        scored_docs = []

        for doc_id, document in self.documents.items():
            # 标题匹配
            title_words = set(document.title.lower().split())
            title_matches = len(query_words & title_words)

            # 内容匹配
            content_words = set(document.content.lower().split())
            content_matches = len(query_words & content_words)

            # 计算总分
            total_score = title_matches * 2 + content_matches  # 标题权重更高
            if total_score > 0:
                normalized_score = total_score / len(query_words)
                scored_docs.append((doc_id, normalized_score))

        scored_docs.sort(key=lambda x: x[1], reverse=True)
        return scored_docs[:top_k]

    def _combine_multimodal_results(self, vector_results: List[SearchResult],
                                   text_results: List[Tuple[str, float]],
                                   financial_results: List[SearchResult]) -> List[SearchResult]:
        """合并多模态搜索结果"""
        combined_scores = {}

        # 向量搜索结果 (权重: 0.5)
        for result in vector_results:
            doc_id = result.document.doc_id
            combined_scores[doc_id] = combined_scores.get(doc_id, 0) + result.similarity_score * 0.5

        # 文本搜索结果 (权重: 0.3)
        for doc_id, score in text_results:
            combined_scores[doc_id] = combined_scores.get(doc_id, 0) + score * 0.3

        # 财务搜索结果 (权重: 0.2)
        for result in financial_results:
            doc_id = result.document.doc_id
            combined_scores[doc_id] = combined_scores.get(doc_id, 0) + result.similarity_score * 0.2

        # 构建最终结果
        final_results = []
        for doc_id, score in combined_scores.items():
            if doc_id in self.documents:
                document = self.documents[doc_id]
                search_result = SearchResult(
                    document=document,
                    similarity_score=score,
                    relevance_factors={
                        'vector_similarity': 0.5,
                        'text_match': 0.3,
                        'financial_match': 0.2
                    },
                    retrieval_context={'multimodal_search': True}
                )
                final_results.append(search_result)

        final_results.sort(key=lambda x: x.similarity_score, reverse=True)
        return final_results

    def _build_search_results(self, similarities: List[Tuple[str, float]]) -> List[SearchResult]:
        """构建搜索结果"""
        results = []
        for doc_id, similarity in similarities:
            if doc_id in self.documents:
                document = self.documents[doc_id]
                search_result = SearchResult(
                    document=document,
                    similarity_score=similarity,
                    relevance_factors={'vector_similarity': 1.0},
                    retrieval_context={'search_timestamp': datetime.now().isoformat()}
                )
                results.append(search_result)
        return results

    def _generate_cache_key(self, query_vector: np.ndarray, top_k: int,
                           modality_filter: Optional[DataModalityType],
                           quality_threshold: float,
                           time_range: Optional[Tuple[datetime, datetime]]) -> str:
        """生成缓存键"""
        # 向量哈希
        vector_hash = hashlib.md5(query_vector.tobytes()).hexdigest()[:16]

        # 参数哈希
        params = f"{top_k}_{modality_filter}_{quality_threshold}_{time_range}"
        param_hash = hashlib.md5(params.encode()).hexdigest()[:16]

        return f"{vector_hash}_{param_hash}"

    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.similarity_cache:
            return False

        cache_entry = self.similarity_cache[cache_key]
        cache_time = cache_entry.get('timestamp', datetime.min)

        return (datetime.now() - cache_time).total_seconds() < self.cache_ttl

    def _clear_similarity_cache(self):
        """清除相似度缓存"""
        self.similarity_cache.clear()

    def _save_document_to_db(self, document: VectorDocument):
        """保存文档到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 保存文档信息
                cursor.execute('''
                    INSERT OR REPLACE INTO documents
                    (doc_id, title, content, modality_type, metadata, financial_metrics,
                     timestamp, source, quality_score, access_count)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    document.doc_id,
                    document.title,
                    document.content,
                    document.modality_type.value,
                    json.dumps(document.metadata),
                    json.dumps(document.financial_metrics),
                    document.timestamp.isoformat(),
                    document.source,
                    document.quality_score,
                    document.access_count
                ))

                # 保存向量数据
                vector_bytes = pickle.dumps(document.embedding)
                cursor.execute('''
                    INSERT OR REPLACE INTO vectors (doc_id, vector_data, dimension)
                    VALUES (?, ?, ?)
                ''', (document.doc_id, vector_bytes, len(document.embedding)))

                conn.commit()

        except Exception as e:
            self.logger.error(f"保存文档到数据库失败 {document.doc_id}: {e}")

    def _load_from_storage(self):
        """从存储加载数据"""
        try:
            if not self.db_path.exists():
                return

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # 加载文档
                cursor.execute('''
                    SELECT d.*, v.vector_data FROM documents d
                    LEFT JOIN vectors v ON d.doc_id = v.doc_id
                ''')

                for row in cursor.fetchall():
                    try:
                        doc_id = row[0]

                        # 构建文档对象
                        document = VectorDocument(
                            doc_id=doc_id,
                            title=row[1],
                            content=row[2],
                            modality_type=DataModalityType(row[3]),
                            embedding=pickle.loads(row[11]) if row[11] else None,
                            metadata=json.loads(row[4]) if row[4] else {},
                            financial_metrics=json.loads(row[5]) if row[5] else {},
                            timestamp=datetime.fromisoformat(row[6]),
                            source=row[7],
                            quality_score=row[8],
                            access_count=row[9]
                        )

                        # 添加到内存存储
                        self.documents[doc_id] = document
                        if document.embedding is not None:
                            self.vectors[doc_id] = document.embedding

                        # 更新模态索引
                        self.modality_indices[document.modality_type].append(doc_id)

                    except Exception as e:
                        self.logger.warning(f"加载文档失败 {doc_id}: {e}")

                # 更新统计信息
                self.stats['total_documents'] = len(self.documents)
                for modality in DataModalityType:
                    self.stats['modality_distribution'][modality.value] = len(self.modality_indices[modality])
                self._update_average_quality_score()

                self.logger.info(f"从存储加载了 {len(self.documents)} 个文档")

        except Exception as e:
            self.logger.error(f"从存储加载数据失败: {e}")

    def _update_average_quality_score(self):
        """更新平均质量分数"""
        if self.documents:
            total_score = sum(doc.quality_score for doc in self.documents.values())
            self.stats['average_quality_score'] = total_score / len(self.documents)
        else:
            self.stats['average_quality_score'] = 0.0

    def _update_document_access_count(self, doc_id: str):
        """更新文档访问次数"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    'UPDATE documents SET access_count = access_count + 1 WHERE doc_id = ?',
                    (doc_id,)
                )
                conn.commit()
        except Exception as e:
            self.logger.warning(f"更新访问次数失败 {doc_id}: {e}")

    def _remove_document_from_db(self, doc_id: str):
        """从数据库移除文档"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM vectors WHERE doc_id = ?', (doc_id,))
                cursor.execute('DELETE FROM documents WHERE doc_id = ?', (doc_id,))
                conn.commit()
        except Exception as e:
            self.logger.error(f"从数据库移除文档失败 {doc_id}: {e}")

    def get_statistics(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        return {
            **self.stats,
            'cache_size': len(self.similarity_cache),
            'memory_usage_mb': self._estimate_memory_usage(),
            'storage_size_mb': self._get_storage_size()
        }

    def _estimate_memory_usage(self) -> float:
        """估算内存使用量"""
        try:
            total_size = 0
            for vector in self.vectors.values():
                total_size += vector.nbytes
            return total_size / (1024 * 1024)  # MB
        except:
            return 0.0

    def _get_storage_size(self) -> float:
        """获取存储大小"""
        try:
            return self.db_path.stat().st_size / (1024 * 1024)  # MB
        except:
            return 0.0

    def optimize_storage(self):
        """优化存储"""
        try:
            # 清理过期缓存
            current_time = datetime.now()
            expired_keys = []
            for key, cache_entry in self.similarity_cache.items():
                cache_time = cache_entry.get('timestamp', datetime.min)
                if (current_time - cache_time).total_seconds() > self.cache_ttl:
                    expired_keys.append(key)

            for key in expired_keys:
                del self.similarity_cache[key]

            # 数据库优化
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('VACUUM')
                conn.execute('ANALYZE')

            self.logger.info(f"存储优化完成，清理了 {len(expired_keys)} 个过期缓存")

        except Exception as e:
            self.logger.error(f"存储优化失败: {e}")

    def backup_storage(self, backup_path: str):
        """备份存储"""
        try:
            backup_file = Path(backup_path)
            backup_file.parent.mkdir(parents=True, exist_ok=True)

            # 备份数据库
            with sqlite3.connect(self.db_path) as source:
                with sqlite3.connect(backup_file) as backup:
                    source.backup(backup)

            self.logger.info(f"存储备份完成: {backup_path}")

        except Exception as e:
            self.logger.error(f"存储备份失败: {e}")

    def restore_storage(self, backup_path: str):
        """恢复存储"""
        try:
            backup_file = Path(backup_path)
            if not backup_file.exists():
                raise FileNotFoundError(f"备份文件不存在: {backup_path}")

            # 清空当前数据
            self.documents.clear()
            self.vectors.clear()
            for modality in DataModalityType:
                self.modality_indices[modality].clear()
            self._clear_similarity_cache()

            # 恢复数据库
            if self.db_path.exists():
                self.db_path.unlink()

            with sqlite3.connect(backup_file) as source:
                with sqlite3.connect(self.db_path) as target:
                    source.backup(target)

            # 重新加载数据
            self._load_from_storage()

            self.logger.info(f"存储恢复完成: {backup_path}")

        except Exception as e:
            self.logger.error(f"存储恢复失败: {e}")
