#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
金融RAG增强系统
提供基于专业知识库的检索增强生成功能
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import json

from ..knowledge.financial_knowledge_base import FinancialKnowledgeBase


class FinancialRAGSystem:
    """金融RAG增强系统"""
    
    def __init__(self):
        """初始化RAG系统"""
        self.logger = logging.getLogger(__name__)
        self.knowledge_base = FinancialKnowledgeBase()
        
        # 专业术语映射
        self.term_mapping = {
            "roe": "净资产收益率",
            "roa": "总资产回报率", 
            "pe": "市盈率",
            "pb": "市净率",
            "dupont": "杜邦分析",
            "macd": "平滑异同移动平均线",
            "rsi": "相对强弱指数",
            "dcf": "现金流折现",
            "ebitda": "息税折旧摊销前利润"
        }
    
    def enhance_analysis(self, analysis_type: str, data: Dict[str, Any], 
                        query: Optional[str] = None) -> Dict[str, Any]:
        """
        增强分析结果
        
        Args:
            analysis_type: 分析类型 (financial/industry/macro)
            data: 原始分析数据
            query: 特定查询
            
        Returns:
            增强后的分析结果
        """
        try:
            self.logger.info(f"开始RAG增强分析: {analysis_type}")
            
            enhanced_result = {
                'original_analysis': data,
                'knowledge_enhancement': {},
                'professional_insights': [],
                'methodology_explanation': {},
                'benchmarks': {},
                'recommendations': []
            }
            
            # 根据分析类型获取相关知识
            if analysis_type == 'financial':
                enhanced_result.update(self._enhance_financial_analysis(data, query))
            elif analysis_type == 'industry':
                enhanced_result.update(self._enhance_industry_analysis(data, query))
            elif analysis_type == 'macro':
                enhanced_result.update(self._enhance_macro_analysis(data, query))
            
            return enhanced_result
            
        except Exception as e:
            self.logger.error(f"RAG增强分析失败: {e}")
            return {'error': str(e), 'original_analysis': data}
    
    def _enhance_financial_analysis(self, data: Dict[str, Any], 
                                  query: Optional[str] = None) -> Dict[str, Any]:
        """增强财务分析"""
        enhancement = {}
        
        # 杜邦分析增强
        if 'financial_ratios' in data:
            ratios = data['financial_ratios']
            dupont_knowledge = self.knowledge_base.get_knowledge('dupont_analysis')
            
            enhancement['dupont_analysis'] = self._perform_dupont_analysis(ratios, dupont_knowledge)
        
        # 财务比率基准对比
        if 'financial_ratios' in data:
            ratios_knowledge = self.knowledge_base.get_knowledge('financial_ratios')
            enhancement['ratio_benchmarks'] = self._compare_with_benchmarks(
                data['financial_ratios'], ratios_knowledge
            )
        
        # 专业解读
        enhancement['professional_insights'] = self._generate_financial_insights(data)
        
        # 方法论说明
        enhancement['methodology_explanation'] = self._explain_financial_methodology()
        
        return enhancement
    
    def _enhance_industry_analysis(self, data: Dict[str, Any], 
                                 query: Optional[str] = None) -> Dict[str, Any]:
        """增强行业分析"""
        enhancement = {}
        
        # 行业分析框架
        industry_knowledge = self.knowledge_base.get_knowledge('industry_analysis')
        enhancement['analysis_framework'] = industry_knowledge.get('分析框架', {})
        
        # 关键指标解读
        enhancement['key_indicators'] = industry_knowledge.get('关键指标', {})
        
        # 专业建议
        enhancement['professional_insights'] = self._generate_industry_insights(data)
        
        return enhancement
    
    def _enhance_macro_analysis(self, data: Dict[str, Any], 
                              query: Optional[str] = None) -> Dict[str, Any]:
        """增强宏观分析"""
        enhancement = {}
        
        # 宏观指标解读
        macro_knowledge = self.knowledge_base.get_knowledge('macro_analysis')
        enhancement['indicator_interpretation'] = macro_knowledge.get('核心指标', {})
        
        # 分析框架
        enhancement['analysis_framework'] = macro_knowledge.get('分析框架', {})
        
        # 专业见解
        enhancement['professional_insights'] = self._generate_macro_insights(data)
        
        return enhancement
    
    def _perform_dupont_analysis(self, ratios: Dict[str, float], 
                               dupont_knowledge: Dict[str, Any]) -> Dict[str, Any]:
        """执行杜邦分析"""
        try:
            # 提取三个核心比率
            net_profit_margin = ratios.get('net_profit_margin', 0) / 100  # 转换为小数
            asset_turnover = ratios.get('asset_turnover', 0)
            equity_multiplier = 1 + ratios.get('debt_to_equity', 0)
            
            # 计算ROE
            calculated_roe = net_profit_margin * asset_turnover * equity_multiplier * 100
            
            # 分析各组成部分
            components_analysis = {}
            
            # 净利润率分析
            if net_profit_margin > 0.15:
                components_analysis['净利润率'] = "优秀 - 公司盈利能力强"
            elif net_profit_margin > 0.10:
                components_analysis['净利润率'] = "良好 - 盈利能力较强"
            elif net_profit_margin > 0.05:
                components_analysis['净利润率'] = "一般 - 盈利能力中等"
            else:
                components_analysis['净利润率'] = "较差 - 盈利能力有待提升"
            
            # 资产周转率分析
            if asset_turnover > 1.5:
                components_analysis['总资产周转率'] = "优秀 - 资产运营效率高"
            elif asset_turnover > 1.0:
                components_analysis['总资产周转率'] = "良好 - 资产运营效率较好"
            elif asset_turnover > 0.5:
                components_analysis['总资产周转率'] = "一般 - 资产运营效率中等"
            else:
                components_analysis['总资产周转率'] = "较差 - 资产运营效率低"
            
            # 权益乘数分析
            if equity_multiplier < 1.5:
                components_analysis['权益乘数'] = "保守 - 财务杠杆较低，风险小"
            elif equity_multiplier < 2.5:
                components_analysis['权益乘数'] = "适中 - 财务杠杆合理"
            elif equity_multiplier < 4.0:
                components_analysis['权益乘数'] = "较高 - 财务杠杆偏高，需关注风险"
            else:
                components_analysis['权益乘数'] = "过高 - 财务杠杆过高，风险较大"
            
            return {
                'formula': dupont_knowledge.get('formula', ''),
                'components': {
                    '净利润率': f"{net_profit_margin:.2%}",
                    '总资产周转率': f"{asset_turnover:.2f}",
                    '权益乘数': f"{equity_multiplier:.2f}"
                },
                'calculated_roe': f"{calculated_roe:.2f}%",
                'components_analysis': components_analysis,
                'improvement_suggestions': self._generate_dupont_suggestions(
                    net_profit_margin, asset_turnover, equity_multiplier
                )
            }
            
        except Exception as e:
            self.logger.error(f"杜邦分析失败: {e}")
            return {}
    
    def _compare_with_benchmarks(self, ratios: Dict[str, float], 
                               ratios_knowledge: Dict[str, Any]) -> Dict[str, Any]:
        """与基准对比"""
        benchmarks = {}
        
        for category, category_ratios in ratios_knowledge.items():
            if not isinstance(category_ratios, dict):
                continue
                
            benchmarks[category] = {}
            
            for ratio_name, ratio_info in category_ratios.items():
                if ratio_name in ['roe', 'roa', 'net_profit_margin']:
                    ratio_value = ratios.get(ratio_name, 0)
                elif ratio_name == '销售净利率':
                    ratio_value = ratios.get('net_profit_margin', 0)
                elif ratio_name == '流动比率':
                    ratio_value = ratios.get('current_ratio', 0)
                elif ratio_name == '资产负债率':
                    ratio_value = ratios.get('debt_to_assets', 0) * 100
                else:
                    continue
                
                benchmark_info = ratio_info.get('benchmark', {})
                assessment = self._assess_ratio_performance(ratio_value, benchmark_info)
                
                benchmarks[category][ratio_name] = {
                    'value': ratio_value,
                    'assessment': assessment,
                    'interpretation': ratio_info.get('interpretation', '')
                }
        
        return benchmarks
    
    def _assess_ratio_performance(self, value: float, benchmark: Dict[str, str]) -> str:
        """评估比率表现"""
        try:
            # 简化的评估逻辑
            if 'excellent' in benchmark:
                excellent_threshold = self._parse_threshold(benchmark['excellent'])
                if self._meets_threshold(value, excellent_threshold):
                    return "优秀"
            
            if 'good' in benchmark:
                good_threshold = self._parse_threshold(benchmark['good'])
                if self._meets_threshold(value, good_threshold):
                    return "良好"
            
            if 'average' in benchmark:
                average_threshold = self._parse_threshold(benchmark['average'])
                if self._meets_threshold(value, average_threshold):
                    return "一般"
            
            return "较差"
            
        except:
            return "无法评估"
    
    def _parse_threshold(self, threshold_str: str) -> Tuple[str, float]:
        """解析阈值字符串"""
        threshold_str = threshold_str.replace('%', '')
        
        if '>' in threshold_str:
            return ('>', float(threshold_str.replace('>', '')))
        elif '<' in threshold_str:
            return ('<', float(threshold_str.replace('<', '')))
        elif '-' in threshold_str:
            parts = threshold_str.split('-')
            return ('range', (float(parts[0]), float(parts[1])))
        else:
            return ('=', float(threshold_str))
    
    def _meets_threshold(self, value: float, threshold: Tuple[str, Any]) -> bool:
        """检查是否满足阈值"""
        operator, threshold_value = threshold
        
        if operator == '>':
            return value > threshold_value
        elif operator == '<':
            return value < threshold_value
        elif operator == 'range':
            return threshold_value[0] <= value <= threshold_value[1]
        elif operator == '=':
            return abs(value - threshold_value) < 0.01
        
        return False
    
    def _generate_financial_insights(self, data: Dict[str, Any]) -> List[str]:
        """生成财务专业见解"""
        insights = []
        
        # 基于财务比率的见解
        if 'financial_ratios' in data:
            ratios = data['financial_ratios']
            
            # ROE分析
            roe = ratios.get('roe', 0)
            if roe > 20:
                insights.append("ROE超过20%，显示出优秀的股东回报能力，在同行业中具有竞争优势")
            elif roe > 15:
                insights.append("ROE处于15-20%区间，股东回报能力良好，但仍有提升空间")
            elif roe < 10:
                insights.append("ROE低于10%，股东回报能力较弱，需要关注盈利能力和资产效率")
            
            # 资产负债率分析
            debt_ratio = ratios.get('debt_to_equity', 0)
            if debt_ratio > 2:
                insights.append("负债权益比超过2，财务杠杆较高，需要密切关注偿债风险")
            elif debt_ratio < 0.5:
                insights.append("负债权益比较低，财务结构保守，可考虑适度提高杠杆以提升ROE")
        
        # 基于盈利能力的见解
        if 'profitability_analysis' in data:
            profitability = data['profitability_analysis']
            score = profitability.get('profitability_score', 0)
            
            if score > 85:
                insights.append("盈利能力评分优秀，公司具有强劲的盈利能力和成长潜力")
            elif score < 60:
                insights.append("盈利能力评分偏低，建议关注成本控制和收入增长策略")
        
        return insights
    
    def _generate_industry_insights(self, data: Dict[str, Any]) -> List[str]:
        """生成行业专业见解"""
        insights = []
        
        insights.append("建议运用波特五力模型深入分析行业竞争格局")
        insights.append("关注行业生命周期阶段，判断投资时机和策略")
        insights.append("分析产业链上下游关系，识别价值创造环节")
        
        return insights
    
    def _generate_macro_insights(self, data: Dict[str, Any]) -> List[str]:
        """生成宏观专业见解"""
        insights = []
        
        insights.append("关注货币政策传导机制对不同行业的差异化影响")
        insights.append("分析财政政策的乘数效应和挤出效应")
        insights.append("监控经济周期位置，调整资产配置策略")
        
        return insights
    
    def _explain_financial_methodology(self) -> Dict[str, str]:
        """解释财务分析方法论"""
        return {
            "杜邦分析法": "通过分解ROE为净利润率、资产周转率和权益乘数，识别盈利能力的驱动因素",
            "财务比率分析": "运用盈利能力、偿债能力、运营效率和市场价值四大类比率进行综合评估",
            "趋势分析": "通过历史数据对比分析财务指标的变化趋势和发展规律",
            "同业对比": "与行业平均水平和标杆企业进行横向比较，识别相对优势和劣势"
        }
    
    def _generate_dupont_suggestions(self, net_profit_margin: float, 
                                   asset_turnover: float, equity_multiplier: float) -> List[str]:
        """生成杜邦分析改进建议"""
        suggestions = []
        
        if net_profit_margin < 0.05:
            suggestions.append("提升净利润率：优化产品结构，提高产品附加值，加强成本控制")
        
        if asset_turnover < 0.5:
            suggestions.append("提高资产周转率：优化资产配置，提升资产使用效率，加快存货和应收账款周转")
        
        if equity_multiplier < 1.5:
            suggestions.append("适度提高财务杠杆：在风险可控前提下，合理利用债务融资提升ROE")
        elif equity_multiplier > 3:
            suggestions.append("降低财务杠杆：优化资本结构，降低财务风险，提升财务稳健性")
        
        return suggestions
    
    def get_professional_terminology(self, term: str) -> Optional[str]:
        """获取专业术语解释"""
        term_lower = term.lower()
        if term_lower in self.term_mapping:
            return self.term_mapping[term_lower]
        
        # 从知识库搜索
        results = self.knowledge_base.search_knowledge(term)
        if results:
            return results[0]['content']
        
        return None
