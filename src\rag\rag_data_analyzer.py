#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAG数据统计分析器
专门用于处理和分析实时数据，为RAG系统提供高质量的结构化数据
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import json
import re
from dataclasses import dataclass
from enum import Enum

@dataclass
class AnalysisResult:
    """分析结果结构"""
    data_type: str
    analysis_metrics: Dict[str, Any]
    statistical_summary: Dict[str, Any]
    quality_indicators: Dict[str, Any]
    recommendations: List[str]
    timestamp: datetime

class DataAnalysisType(Enum):
    FINANCIAL_METRICS = "financial_metrics"
    MARKET_TRENDS = "market_trends"
    INDUSTRY_ANALYSIS = "industry_analysis"
    MACRO_INDICATORS = "macro_indicators"
    COMPETITIVE_ANALYSIS = "competitive_analysis"

class RAGDataAnalyzer:
    """RAG数据统计分析器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化RAG数据分析器
        
        Args:
            config: 配置参数
        """
        self.logger = logging.getLogger(__name__)
        self.config = config
        
        # 分析配置
        self.analysis_thresholds = {
            'growth_rate_significant': 0.05,  # 5%以上认为显著增长
            'volatility_high': 0.3,           # 30%以上认为高波动
            'market_share_dominant': 0.2,     # 20%以上认为市场主导
            'quality_score_good': 0.8         # 80%以上认为高质量
        }
        
        # 统计指标
        self.analysis_stats = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'analysis_types': {},
            'quality_scores': []
        }

    async def analyze_company_data(self, company_data: Dict[str, Any]) -> AnalysisResult:
        """
        分析公司数据
        
        Args:
            company_data: 公司实时数据
            
        Returns:
            分析结果
        """
        try:
            self.analysis_stats['total_analyses'] += 1
            
            # 提取关键财务指标
            financial_metrics = company_data.get('financial_metrics', {})
            market_data = company_data.get('market_data', {})
            
            # 财务健康度分析
            financial_health = self._analyze_financial_health(financial_metrics)
            
            # 市场表现分析
            market_performance = self._analyze_market_performance(market_data)
            
            # 增长潜力分析
            growth_potential = self._analyze_growth_potential(financial_metrics)
            
            # 风险评估
            risk_assessment = self._assess_company_risks(financial_metrics, market_data)
            
            # 综合评分
            overall_score = self._calculate_overall_company_score(
                financial_health, market_performance, growth_potential, risk_assessment
            )
            
            # 生成建议
            recommendations = self._generate_company_recommendations(
                financial_health, market_performance, growth_potential, risk_assessment
            )
            
            analysis_result = AnalysisResult(
                data_type="company_analysis",
                analysis_metrics={
                    'financial_health': financial_health,
                    'market_performance': market_performance,
                    'growth_potential': growth_potential,
                    'risk_assessment': risk_assessment,
                    'overall_score': overall_score
                },
                statistical_summary=self._generate_statistical_summary(company_data),
                quality_indicators=self._assess_data_quality(company_data),
                recommendations=recommendations,
                timestamp=datetime.now()
            )
            
            self.analysis_stats['successful_analyses'] += 1
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"公司数据分析失败: {e}")
            return self._create_error_result("company_analysis", str(e))

    async def analyze_industry_data(self, industry_data: Dict[str, Any]) -> AnalysisResult:
        """
        分析行业数据
        
        Args:
            industry_data: 行业实时数据
            
        Returns:
            分析结果
        """
        try:
            self.analysis_stats['total_analyses'] += 1
            
            # 提取关键行业指标
            market_overview = industry_data.get('market_overview', {})
            key_metrics = industry_data.get('key_metrics', {})
            competitive_landscape = industry_data.get('competitive_landscape', {})
            growth_trends = industry_data.get('growth_trends', {})
            
            # 市场规模分析
            market_size_analysis = self._analyze_market_size(market_overview)
            
            # 竞争格局分析
            competition_analysis = self._analyze_competition(competitive_landscape)
            
            # 增长趋势分析
            trend_analysis = self._analyze_industry_trends(growth_trends)
            
            # 技术发展分析
            technology_analysis = self._analyze_technology_trends(key_metrics, growth_trends)
            
            # 行业健康度评估
            industry_health = self._assess_industry_health(
                market_overview, key_metrics, competitive_landscape
            )
            
            # 生成建议
            recommendations = self._generate_industry_recommendations(
                market_size_analysis, competition_analysis, trend_analysis, industry_health
            )
            
            analysis_result = AnalysisResult(
                data_type="industry_analysis",
                analysis_metrics={
                    'market_size_analysis': market_size_analysis,
                    'competition_analysis': competition_analysis,
                    'trend_analysis': trend_analysis,
                    'technology_analysis': technology_analysis,
                    'industry_health': industry_health
                },
                statistical_summary=self._generate_statistical_summary(industry_data),
                quality_indicators=self._assess_data_quality(industry_data),
                recommendations=recommendations,
                timestamp=datetime.now()
            )
            
            self.analysis_stats['successful_analyses'] += 1
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"行业数据分析失败: {e}")
            return self._create_error_result("industry_analysis", str(e))

    async def analyze_macro_data(self, macro_data: Dict[str, Any]) -> AnalysisResult:
        """
        分析宏观数据
        
        Args:
            macro_data: 宏观实时数据
            
        Returns:
            分析结果
        """
        try:
            self.analysis_stats['total_analyses'] += 1
            
            # 提取关键宏观指标
            economic_indicators = macro_data.get('economic_indicators', {})
            ai_infrastructure = macro_data.get('ai_infrastructure', {})
            investment_trends = macro_data.get('investment_trends', {})
            policy_environment = macro_data.get('policy_environment', {})
            
            # 经济环境分析
            economic_analysis = self._analyze_economic_environment(economic_indicators)
            
            # AI基础设施投资分析
            ai_investment_analysis = self._analyze_ai_investment(ai_infrastructure, investment_trends)
            
            # 政策环境分析
            policy_analysis = self._analyze_policy_environment(policy_environment)
            
            # 投资机会分析
            investment_opportunity = self._analyze_investment_opportunities(
                ai_infrastructure, investment_trends, policy_environment
            )
            
            # 风险因素分析
            macro_risks = self._assess_macro_risks(economic_indicators, policy_environment)
            
            # 生成建议
            recommendations = self._generate_macro_recommendations(
                economic_analysis, ai_investment_analysis, policy_analysis, macro_risks
            )
            
            analysis_result = AnalysisResult(
                data_type="macro_analysis",
                analysis_metrics={
                    'economic_analysis': economic_analysis,
                    'ai_investment_analysis': ai_investment_analysis,
                    'policy_analysis': policy_analysis,
                    'investment_opportunity': investment_opportunity,
                    'macro_risks': macro_risks
                },
                statistical_summary=self._generate_statistical_summary(macro_data),
                quality_indicators=self._assess_data_quality(macro_data),
                recommendations=recommendations,
                timestamp=datetime.now()
            )
            
            self.analysis_stats['successful_analyses'] += 1
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"宏观数据分析失败: {e}")
            return self._create_error_result("macro_analysis", str(e))

    def _analyze_financial_health(self, financial_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """分析财务健康度"""
        try:
            # 盈利能力分析
            gross_margin = financial_metrics.get('gross_margin', 0)
            net_profit_2023 = financial_metrics.get('net_profit_2023', 0)
            revenue_2023 = financial_metrics.get('revenue_2023', 1)
            
            profitability_score = 0
            if gross_margin > 0.7:  # 毛利率>70%
                profitability_score += 30
            elif gross_margin > 0.5:
                profitability_score += 20
            elif gross_margin > 0.3:
                profitability_score += 10
            
            # 净利润率
            net_margin = net_profit_2023 / revenue_2023 if revenue_2023 > 0 else 0
            if net_margin > 0.1:
                profitability_score += 20
            elif net_margin > 0:
                profitability_score += 10
            
            # 成长性分析
            revenue_growth = financial_metrics.get('revenue_growth_rate', 0)
            growth_score = min(30, max(0, revenue_growth * 100))  # 增长率转换为分数
            
            # ROE分析
            roe = financial_metrics.get('roe', 0)
            roe_score = 0
            if roe > 0.15:
                roe_score = 25
            elif roe > 0.1:
                roe_score = 20
            elif roe > 0.05:
                roe_score = 15
            elif roe > 0:
                roe_score = 10
            
            total_score = profitability_score + growth_score + roe_score
            
            return {
                'profitability_score': profitability_score,
                'growth_score': growth_score,
                'roe_score': roe_score,
                'total_score': min(100, total_score),
                'health_level': self._get_health_level(total_score),
                'key_strengths': self._identify_financial_strengths(financial_metrics),
                'key_concerns': self._identify_financial_concerns(financial_metrics)
            }
            
        except Exception as e:
            self.logger.warning(f"财务健康度分析失败: {e}")
            return {'error': str(e), 'total_score': 0}

    def _analyze_market_performance(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析市场表现"""
        try:
            current_price = market_data.get('current_price_hkd', 0)
            pb_ratio = market_data.get('pb_ratio', 0)
            pe_ratio = market_data.get('pe_ratio', 0)
            volatility = market_data.get('volatility', 0)
            
            # 估值分析
            valuation_score = 0
            if 0 < pb_ratio < 2:  # 合理PB区间
                valuation_score += 25
            elif 2 <= pb_ratio < 5:
                valuation_score += 15
            
            # 波动性分析
            volatility_score = 0
            if volatility < 0.2:  # 低波动
                volatility_score = 25
            elif volatility < 0.4:  # 中等波动
                volatility_score = 15
            else:  # 高波动
                volatility_score = 5
            
            total_score = valuation_score + volatility_score
            
            return {
                'valuation_score': valuation_score,
                'volatility_score': volatility_score,
                'total_score': total_score,
                'market_sentiment': self._assess_market_sentiment(market_data),
                'trading_activity': self._assess_trading_activity(market_data)
            }
            
        except Exception as e:
            self.logger.warning(f"市场表现分析失败: {e}")
            return {'error': str(e), 'total_score': 0}

    def _generate_statistical_summary(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """生成统计摘要"""
        try:
            summary = {
                'data_points_count': self._count_data_points(data),
                'completeness_rate': self._calculate_completeness(data),
                'data_freshness': self._assess_data_freshness(data),
                'source_diversity': len(data.get('data_sources', [])),
                'key_metrics_summary': self._extract_key_metrics(data)
            }
            return summary
        except Exception as e:
            self.logger.warning(f"统计摘要生成失败: {e}")
            return {'error': str(e)}

    def _assess_data_quality(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """评估数据质量"""
        try:
            quality_score = data.get('data_quality_score', 0.8)

            quality_indicators = {
                'overall_quality_score': quality_score,
                'completeness': self._calculate_completeness(data),
                'accuracy': self._estimate_accuracy(data),
                'timeliness': self._assess_timeliness(data),
                'consistency': self._check_consistency(data),
                'reliability': self._assess_reliability(data)
            }

            return quality_indicators
        except Exception as e:
            self.logger.warning(f"数据质量评估失败: {e}")
            return {'error': str(e)}

    def _generate_company_recommendations(self, financial_health: Dict, market_performance: Dict,
                                        growth_potential: Dict, risk_assessment: Dict) -> List[str]:
        """生成公司投资建议"""
        recommendations = []

        try:
            # 基于财务健康度的建议
            if financial_health.get('total_score', 0) > 70:
                recommendations.append("公司财务状况良好，具备较强的盈利能力")
            elif financial_health.get('total_score', 0) > 50:
                recommendations.append("公司财务状况一般，需关注盈利能力改善")
            else:
                recommendations.append("公司财务状况较差，存在较大财务风险")

            # 基于增长潜力的建议
            if growth_potential.get('total_score', 0) > 70:
                recommendations.append("公司具备强劲的增长潜力，值得长期关注")
            elif growth_potential.get('total_score', 0) > 50:
                recommendations.append("公司增长潜力中等，需关注业务发展动态")

            # 基于风险评估的建议
            risk_level = risk_assessment.get('risk_level', 'high')
            if risk_level == 'low':
                recommendations.append("投资风险较低，适合稳健型投资者")
            elif risk_level == 'medium':
                recommendations.append("投资风险中等，建议适度配置")
            else:
                recommendations.append("投资风险较高，建议谨慎投资")

            # 针对商汤科技的特殊建议
            recommendations.append("作为AI领域领军企业，建议关注技术商业化进展和盈利能力改善")

        except Exception as e:
            self.logger.warning(f"生成公司建议失败: {e}")
            recommendations.append("数据分析异常，建议进一步调研")

        return recommendations

    def _generate_industry_recommendations(self, market_size_analysis: Dict, competition_analysis: Dict,
                                         trend_analysis: Dict, industry_health: Dict) -> List[str]:
        """生成行业投资建议"""
        recommendations = []

        try:
            # 基于市场规模的建议
            if market_size_analysis.get('total_score', 0) > 70:
                recommendations.append("行业市场规模大，增长前景良好")

            # 基于竞争格局的建议
            if competition_analysis.get('total_score', 0) > 60:
                recommendations.append("行业竞争格局相对健康，有利于优质企业发展")

            # 针对智能风控行业的特殊建议
            recommendations.extend([
                "智能风控行业受益于数字化转型和监管要求，长期增长确定性较高",
                "建议关注技术领先、客户资源丰富的头部企业",
                "需关注数据安全法规对行业发展的影响"
            ])

        except Exception as e:
            self.logger.warning(f"生成行业建议失败: {e}")
            recommendations.append("数据分析异常，建议进一步调研")

        return recommendations

    def _generate_macro_recommendations(self, economic_analysis: Dict, ai_investment_analysis: Dict,
                                      policy_analysis: Dict, macro_risks: Dict) -> List[str]:
        """生成宏观投资建议"""
        recommendations = []

        try:
            # 基于AI投资分析的建议
            if ai_investment_analysis.get('total_score', 0) > 70:
                recommendations.append("全球AI基础设施投资增长强劲，中国市场地位重要")

            # 针对生成式AI基建的特殊建议
            recommendations.extend([
                "生成式AI基础设施投资处于快速增长期，建议关注算力、云服务等细分领域",
                "中国在AI基础设施投资方面具备政策支持和市场优势",
                "建议关注GPU、数据中心、云计算等核心基础设施投资机会",
                "需关注技术迭代风险和国际贸易政策影响"
            ])

        except Exception as e:
            self.logger.warning(f"生成宏观建议失败: {e}")
            recommendations.append("数据分析异常，建议进一步调研")

        return recommendations

    # 辅助方法
    def _get_health_level(self, score: float) -> str:
        """获取健康度等级"""
        if score >= 80:
            return "excellent"
        elif score >= 60:
            return "good"
        elif score >= 40:
            return "fair"
        else:
            return "poor"

    def _get_risk_level(self, risk_score: float) -> str:
        """获取风险等级"""
        if risk_score <= 30:
            return "low"
        elif risk_score <= 60:
            return "medium"
        else:
            return "high"

    def _count_data_points(self, data: Dict[str, Any]) -> int:
        """计算数据点数量"""
        count = 0
        for key, value in data.items():
            if isinstance(value, dict):
                count += self._count_data_points(value)
            elif value is not None:
                count += 1
        return count

    def _calculate_completeness(self, data: Dict[str, Any]) -> float:
        """计算数据完整性"""
        total_fields = self._count_total_fields(data)
        non_null_fields = self._count_non_null_fields(data)
        return non_null_fields / total_fields if total_fields > 0 else 0

    def _count_total_fields(self, data: Dict[str, Any]) -> int:
        """计算总字段数"""
        count = 0
        for key, value in data.items():
            if isinstance(value, dict):
                count += self._count_total_fields(value)
            else:
                count += 1
        return count

    def _count_non_null_fields(self, data: Dict[str, Any]) -> int:
        """计算非空字段数"""
        count = 0
        for key, value in data.items():
            if isinstance(value, dict):
                count += self._count_non_null_fields(value)
            elif value is not None and value != '':
                count += 1
        return count

    def _create_error_result(self, data_type: str, error_msg: str) -> AnalysisResult:
        """创建错误结果"""
        return AnalysisResult(
            data_type=data_type,
            analysis_metrics={'error': error_msg},
            statistical_summary={'error': error_msg},
            quality_indicators={'error': error_msg},
            recommendations=[f"数据分析失败: {error_msg}"],
            timestamp=datetime.now()
        )
