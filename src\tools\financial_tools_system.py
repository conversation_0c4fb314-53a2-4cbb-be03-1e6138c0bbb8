#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专业金融工具调用系统
实现技术指标计算、风险评估、估值模型等专业金融工具
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Callable
from abc import ABC, abstractmethod
import math


class FinancialTool(ABC):
    """金融工具基类"""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.logger = logging.getLogger(f"{__name__}.{name}")
    
    @abstractmethod
    def execute(self, **kwargs) -> Dict[str, Any]:
        """执行工具"""
        pass
    
    def validate_inputs(self, required_params: List[str], **kwargs) -> bool:
        """验证输入参数"""
        for param in required_params:
            if param not in kwargs or kwargs[param] is None:
                raise ValueError(f"缺少必需参数: {param}")
        return True


class TechnicalIndicatorTools(FinancialTool):
    """技术指标计算工具"""
    
    def __init__(self):
        super().__init__("技术指标计算器", "计算各种技术分析指标")
    
    def execute(self, indicator_type: str, data: List[float], **kwargs) -> Dict[str, Any]:
        """执行技术指标计算"""
        try:
            if indicator_type == "SMA":
                return self.calculate_sma(data, kwargs.get('period', 20))
            elif indicator_type == "EMA":
                return self.calculate_ema(data, kwargs.get('period', 20))
            elif indicator_type == "MACD":
                return self.calculate_macd(data, kwargs.get('fast', 12), kwargs.get('slow', 26), kwargs.get('signal', 9))
            elif indicator_type == "RSI":
                return self.calculate_rsi(data, kwargs.get('period', 14))
            elif indicator_type == "KDJ":
                high_data = kwargs.get('high_data', data)
                low_data = kwargs.get('low_data', data)
                return self.calculate_kdj(data, high_data, low_data, kwargs.get('period', 9))
            elif indicator_type == "BOLL":
                return self.calculate_bollinger_bands(data, kwargs.get('period', 20), kwargs.get('std_dev', 2))
            else:
                raise ValueError(f"不支持的技术指标: {indicator_type}")
                
        except Exception as e:
            self.logger.error(f"计算技术指标失败: {e}")
            return {'error': str(e)}
    
    def calculate_sma(self, data: List[float], period: int) -> Dict[str, Any]:
        """计算简单移动平均线"""
        if len(data) < period:
            return {'error': '数据长度不足'}
        
        sma_values = []
        for i in range(period - 1, len(data)):
            sma = sum(data[i - period + 1:i + 1]) / period
            sma_values.append(sma)
        
        return {
            'indicator': 'SMA',
            'period': period,
            'values': sma_values,
            'current_value': sma_values[-1] if sma_values else None,
            'trend': self._determine_trend(sma_values[-5:] if len(sma_values) >= 5 else sma_values)
        }
    
    def calculate_ema(self, data: List[float], period: int) -> Dict[str, Any]:
        """计算指数移动平均线"""
        if len(data) < period:
            return {'error': '数据长度不足'}
        
        multiplier = 2 / (period + 1)
        ema_values = [data[0]]  # 第一个值作为初始EMA
        
        for i in range(1, len(data)):
            ema = (data[i] * multiplier) + (ema_values[-1] * (1 - multiplier))
            ema_values.append(ema)
        
        return {
            'indicator': 'EMA',
            'period': period,
            'values': ema_values,
            'current_value': ema_values[-1],
            'trend': self._determine_trend(ema_values[-5:] if len(ema_values) >= 5 else ema_values)
        }
    
    def calculate_macd(self, data: List[float], fast: int = 12, slow: int = 26, signal: int = 9) -> Dict[str, Any]:
        """计算MACD指标"""
        if len(data) < slow:
            return {'error': '数据长度不足'}
        
        # 计算快线和慢线EMA
        ema_fast = self.calculate_ema(data, fast)['values']
        ema_slow = self.calculate_ema(data, slow)['values']
        
        # 计算DIF线
        dif = [ema_fast[i] - ema_slow[i] for i in range(len(ema_slow))]
        
        # 计算DEA线（信号线）
        dea = self.calculate_ema(dif, signal)['values']
        
        # 计算MACD柱状图
        macd_histogram = [2 * (dif[i] - dea[i]) for i in range(len(dea))]
        
        # 判断信号
        signals = []
        for i in range(1, len(dif)):
            if dif[i] > dea[i] and dif[i-1] <= dea[i-1]:
                signals.append(('golden_cross', i))
            elif dif[i] < dea[i] and dif[i-1] >= dea[i-1]:
                signals.append(('death_cross', i))
        
        return {
            'indicator': 'MACD',
            'dif': dif,
            'dea': dea,
            'macd': macd_histogram,
            'current_dif': dif[-1],
            'current_dea': dea[-1],
            'current_macd': macd_histogram[-1],
            'signals': signals,
            'trend': 'bullish' if dif[-1] > dea[-1] else 'bearish'
        }
    
    def calculate_rsi(self, data: List[float], period: int = 14) -> Dict[str, Any]:
        """计算RSI相对强弱指数"""
        if len(data) < period + 1:
            return {'error': '数据长度不足'}
        
        # 计算价格变化
        price_changes = [data[i] - data[i-1] for i in range(1, len(data))]
        
        # 分离上涨和下跌
        gains = [max(change, 0) for change in price_changes]
        losses = [abs(min(change, 0)) for change in price_changes]
        
        # 计算平均收益和损失
        avg_gain = sum(gains[:period]) / period
        avg_loss = sum(losses[:period]) / period
        
        rsi_values = []
        
        for i in range(period, len(gains)):
            # 使用Wilder's smoothing
            avg_gain = (avg_gain * (period - 1) + gains[i]) / period
            avg_loss = (avg_loss * (period - 1) + losses[i]) / period
            
            if avg_loss == 0:
                rsi = 100
            else:
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
            
            rsi_values.append(rsi)
        
        current_rsi = rsi_values[-1] if rsi_values else 50
        
        # 判断超买超卖
        if current_rsi > 80:
            condition = 'overbought'
        elif current_rsi < 20:
            condition = 'oversold'
        elif current_rsi > 70:
            condition = 'strong'
        elif current_rsi < 30:
            condition = 'weak'
        else:
            condition = 'neutral'
        
        return {
            'indicator': 'RSI',
            'period': period,
            'values': rsi_values,
            'current_value': current_rsi,
            'condition': condition,
            'overbought_threshold': 80,
            'oversold_threshold': 20
        }
    
    def calculate_kdj(self, close_data: List[float], high_data: List[float], 
                     low_data: List[float], period: int = 9) -> Dict[str, Any]:
        """计算KDJ随机指标"""
        if len(close_data) < period:
            return {'error': '数据长度不足'}
        
        k_values = []
        d_values = []
        j_values = []
        
        # 初始值
        k = 50
        d = 50
        
        for i in range(period - 1, len(close_data)):
            # 计算最高价和最低价
            highest_high = max(high_data[i - period + 1:i + 1])
            lowest_low = min(low_data[i - period + 1:i + 1])
            
            # 计算RSV
            if highest_high == lowest_low:
                rsv = 50
            else:
                rsv = (close_data[i] - lowest_low) / (highest_high - lowest_low) * 100
            
            # 计算K值
            k = (2 * k + rsv) / 3
            k_values.append(k)
            
            # 计算D值
            d = (2 * d + k) / 3
            d_values.append(d)
            
            # 计算J值
            j = 3 * k - 2 * d
            j_values.append(j)
        
        # 判断信号
        signals = []
        for i in range(1, len(k_values)):
            if k_values[i] > d_values[i] and k_values[i-1] <= d_values[i-1]:
                signals.append(('golden_cross', i))
            elif k_values[i] < d_values[i] and k_values[i-1] >= d_values[i-1]:
                signals.append(('death_cross', i))
        
        return {
            'indicator': 'KDJ',
            'period': period,
            'k_values': k_values,
            'd_values': d_values,
            'j_values': j_values,
            'current_k': k_values[-1],
            'current_d': d_values[-1],
            'current_j': j_values[-1],
            'signals': signals,
            'condition': self._assess_kdj_condition(k_values[-1], d_values[-1])
        }
    
    def calculate_bollinger_bands(self, data: List[float], period: int = 20, std_dev: float = 2) -> Dict[str, Any]:
        """计算布林带"""
        if len(data) < period:
            return {'error': '数据长度不足'}
        
        middle_band = []  # 中轨（SMA）
        upper_band = []   # 上轨
        lower_band = []   # 下轨
        
        for i in range(period - 1, len(data)):
            # 计算中轨（简单移动平均）
            sma = sum(data[i - period + 1:i + 1]) / period
            middle_band.append(sma)
            
            # 计算标准差
            variance = sum([(x - sma) ** 2 for x in data[i - period + 1:i + 1]]) / period
            std = math.sqrt(variance)
            
            # 计算上下轨
            upper_band.append(sma + std_dev * std)
            lower_band.append(sma - std_dev * std)
        
        # 分析当前位置
        current_price = data[-1]
        current_upper = upper_band[-1]
        current_middle = middle_band[-1]
        current_lower = lower_band[-1]
        
        # 判断位置
        if current_price > current_upper:
            position = 'above_upper'
        elif current_price < current_lower:
            position = 'below_lower'
        elif current_price > current_middle:
            position = 'upper_half'
        else:
            position = 'lower_half'
        
        # 计算带宽
        bandwidth = (current_upper - current_lower) / current_middle * 100
        
        return {
            'indicator': 'BOLL',
            'period': period,
            'std_dev': std_dev,
            'upper_band': upper_band,
            'middle_band': middle_band,
            'lower_band': lower_band,
            'current_upper': current_upper,
            'current_middle': current_middle,
            'current_lower': current_lower,
            'current_position': position,
            'bandwidth': bandwidth,
            'squeeze': bandwidth < 10  # 带宽收窄
        }
    
    def _determine_trend(self, values: List[float]) -> str:
        """判断趋势方向"""
        if len(values) < 2:
            return 'neutral'
        
        if values[-1] > values[0]:
            return 'upward'
        elif values[-1] < values[0]:
            return 'downward'
        else:
            return 'neutral'
    
    def _assess_kdj_condition(self, k: float, d: float) -> str:
        """评估KDJ状态"""
        if k > 80 and d > 80:
            return 'overbought'
        elif k < 20 and d < 20:
            return 'oversold'
        elif k > d:
            return 'bullish'
        else:
            return 'bearish'


class RiskAssessmentTools(FinancialTool):
    """风险评估工具"""
    
    def __init__(self):
        super().__init__("风险评估工具", "计算各种风险指标和评估")
    
    def execute(self, assessment_type: str, **kwargs) -> Dict[str, Any]:
        """执行风险评估"""
        try:
            if assessment_type == "VaR":
                return self.calculate_var(kwargs.get('returns'), kwargs.get('confidence', 0.95))
            elif assessment_type == "volatility":
                return self.calculate_volatility(kwargs.get('prices'))
            elif assessment_type == "beta":
                return self.calculate_beta(kwargs.get('stock_returns'), kwargs.get('market_returns'))
            elif assessment_type == "sharpe_ratio":
                return self.calculate_sharpe_ratio(kwargs.get('returns'), kwargs.get('risk_free_rate', 0.03))
            elif assessment_type == "max_drawdown":
                return self.calculate_max_drawdown(kwargs.get('prices'))
            else:
                raise ValueError(f"不支持的风险评估类型: {assessment_type}")
                
        except Exception as e:
            self.logger.error(f"风险评估失败: {e}")
            return {'error': str(e)}
    
    def calculate_var(self, returns: List[float], confidence: float = 0.95) -> Dict[str, Any]:
        """计算风险价值（VaR）"""
        if not returns:
            return {'error': '收益率数据为空'}
        
        returns_array = np.array(returns)
        
        # 历史模拟法
        var_historical = np.percentile(returns_array, (1 - confidence) * 100)
        
        # 参数法（假设正态分布）
        mean_return = np.mean(returns_array)
        std_return = np.std(returns_array)
        var_parametric = mean_return - 1.645 * std_return  # 95%置信度
        
        return {
            'assessment_type': 'VaR',
            'confidence_level': confidence,
            'var_historical': var_historical,
            'var_parametric': var_parametric,
            'interpretation': f"在{confidence:.1%}的置信度下，最大预期损失为{abs(var_historical):.2%}"
        }
    
    def calculate_volatility(self, prices: List[float]) -> Dict[str, Any]:
        """计算波动率"""
        if len(prices) < 2:
            return {'error': '价格数据不足'}
        
        # 计算收益率
        returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
        
        # 计算波动率
        daily_volatility = np.std(returns)
        annual_volatility = daily_volatility * np.sqrt(252)  # 年化波动率
        
        return {
            'assessment_type': 'volatility',
            'daily_volatility': daily_volatility,
            'annual_volatility': annual_volatility,
            'volatility_level': self._assess_volatility_level(annual_volatility)
        }
    
    def calculate_beta(self, stock_returns: List[float], market_returns: List[float]) -> Dict[str, Any]:
        """计算Beta系数"""
        if len(stock_returns) != len(market_returns) or len(stock_returns) < 2:
            return {'error': '数据长度不匹配或数据不足'}
        
        # 计算协方差和方差
        stock_array = np.array(stock_returns)
        market_array = np.array(market_returns)
        
        covariance = np.cov(stock_array, market_array)[0, 1]
        market_variance = np.var(market_array)
        
        if market_variance == 0:
            return {'error': '市场收益率方差为零'}
        
        beta = covariance / market_variance
        
        return {
            'assessment_type': 'beta',
            'beta_value': beta,
            'interpretation': self._interpret_beta(beta),
            'systematic_risk': 'high' if abs(beta) > 1.2 else 'moderate' if abs(beta) > 0.8 else 'low'
        }
    
    def calculate_sharpe_ratio(self, returns: List[float], risk_free_rate: float = 0.03) -> Dict[str, Any]:
        """计算夏普比率"""
        if not returns:
            return {'error': '收益率数据为空'}
        
        returns_array = np.array(returns)
        
        # 计算超额收益
        excess_returns = returns_array - risk_free_rate / 252  # 日度无风险利率
        
        # 计算夏普比率
        if np.std(excess_returns) == 0:
            sharpe_ratio = 0
        else:
            sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252)
        
        return {
            'assessment_type': 'sharpe_ratio',
            'sharpe_ratio': sharpe_ratio,
            'risk_free_rate': risk_free_rate,
            'performance_level': self._assess_sharpe_performance(sharpe_ratio)
        }
    
    def calculate_max_drawdown(self, prices: List[float]) -> Dict[str, Any]:
        """计算最大回撤"""
        if len(prices) < 2:
            return {'error': '价格数据不足'}
        
        prices_array = np.array(prices)
        
        # 计算累计最高点
        peak = np.maximum.accumulate(prices_array)
        
        # 计算回撤
        drawdown = (prices_array - peak) / peak
        
        # 找到最大回撤
        max_drawdown = np.min(drawdown)
        max_drawdown_index = np.argmin(drawdown)
        
        return {
            'assessment_type': 'max_drawdown',
            'max_drawdown': max_drawdown,
            'max_drawdown_percentage': max_drawdown * 100,
            'max_drawdown_date_index': max_drawdown_index,
            'risk_level': self._assess_drawdown_risk(abs(max_drawdown))
        }
    
    def _assess_volatility_level(self, annual_volatility: float) -> str:
        """评估波动率水平"""
        if annual_volatility > 0.4:
            return 'very_high'
        elif annual_volatility > 0.25:
            return 'high'
        elif annual_volatility > 0.15:
            return 'moderate'
        else:
            return 'low'
    
    def _interpret_beta(self, beta: float) -> str:
        """解释Beta值"""
        if beta > 1.2:
            return '高系统性风险，股价波动大于市场'
        elif beta > 0.8:
            return '中等系统性风险，股价与市场同步波动'
        elif beta > 0:
            return '低系统性风险，股价波动小于市场'
        else:
            return '负相关，股价与市场反向波动'
    
    def _assess_sharpe_performance(self, sharpe_ratio: float) -> str:
        """评估夏普比率表现"""
        if sharpe_ratio > 2:
            return 'excellent'
        elif sharpe_ratio > 1:
            return 'good'
        elif sharpe_ratio > 0:
            return 'acceptable'
        else:
            return 'poor'
    
    def _assess_drawdown_risk(self, max_drawdown: float) -> str:
        """评估回撤风险"""
        if max_drawdown > 0.5:
            return 'very_high'
        elif max_drawdown > 0.3:
            return 'high'
        elif max_drawdown > 0.15:
            return 'moderate'
        else:
            return 'low'


class ValuationTools(FinancialTool):
    """估值工具"""

    def __init__(self):
        super().__init__("估值工具", "执行各种估值计算")

    def execute(self, valuation_type: str, **kwargs) -> Dict[str, Any]:
        """执行估值计算"""
        try:
            if valuation_type == "DCF":
                return self.calculate_dcf(**kwargs)
            elif valuation_type == "PE_valuation":
                return self.calculate_pe_valuation(**kwargs)
            elif valuation_type == "PB_valuation":
                return self.calculate_pb_valuation(**kwargs)
            elif valuation_type == "dividend_discount":
                return self.calculate_dividend_discount_model(**kwargs)
            elif valuation_type == "asset_valuation":
                return self.calculate_asset_valuation(**kwargs)
            else:
                raise ValueError(f"不支持的估值类型: {valuation_type}")

        except Exception as e:
            self.logger.error(f"估值计算失败: {e}")
            return {'error': str(e)}

    def calculate_dcf(self, cash_flows: List[float], discount_rate: float,
                     terminal_growth_rate: float = 0.03) -> Dict[str, Any]:
        """计算DCF估值"""
        if not cash_flows:
            return {'error': '现金流数据为空'}

        # 计算现值
        present_values = []
        for i, cf in enumerate(cash_flows):
            pv = cf / ((1 + discount_rate) ** (i + 1))
            present_values.append(pv)

        # 计算终值
        terminal_value = cash_flows[-1] * (1 + terminal_growth_rate) / (discount_rate - terminal_growth_rate)
        terminal_pv = terminal_value / ((1 + discount_rate) ** len(cash_flows))

        # 总价值
        total_value = sum(present_values) + terminal_pv

        return {
            'valuation_type': 'DCF',
            'cash_flows': cash_flows,
            'discount_rate': discount_rate,
            'terminal_growth_rate': terminal_growth_rate,
            'present_values': present_values,
            'terminal_value': terminal_value,
            'terminal_present_value': terminal_pv,
            'total_enterprise_value': total_value,
            'sensitivity_analysis': self._dcf_sensitivity_analysis(cash_flows, discount_rate, terminal_growth_rate)
        }

    def calculate_pe_valuation(self, earnings: float, industry_pe: float,
                              growth_adjustment: float = 1.0) -> Dict[str, Any]:
        """计算PE估值"""
        if earnings <= 0:
            return {'error': '收益必须为正数'}

        # 基础PE估值
        base_valuation = earnings * industry_pe

        # 成长性调整
        adjusted_pe = industry_pe * growth_adjustment
        adjusted_valuation = earnings * adjusted_pe

        return {
            'valuation_type': 'PE',
            'earnings': earnings,
            'industry_pe': industry_pe,
            'growth_adjustment': growth_adjustment,
            'base_valuation': base_valuation,
            'adjusted_pe': adjusted_pe,
            'adjusted_valuation': adjusted_valuation,
            'valuation_range': {
                'low': adjusted_valuation * 0.8,
                'high': adjusted_valuation * 1.2
            }
        }

    def calculate_pb_valuation(self, book_value: float, industry_pb: float,
                              roe_adjustment: float = 1.0) -> Dict[str, Any]:
        """计算PB估值"""
        if book_value <= 0:
            return {'error': '账面价值必须为正数'}

        # 基础PB估值
        base_valuation = book_value * industry_pb

        # ROE调整
        adjusted_pb = industry_pb * roe_adjustment
        adjusted_valuation = book_value * adjusted_pb

        return {
            'valuation_type': 'PB',
            'book_value': book_value,
            'industry_pb': industry_pb,
            'roe_adjustment': roe_adjustment,
            'base_valuation': base_valuation,
            'adjusted_pb': adjusted_pb,
            'adjusted_valuation': adjusted_valuation,
            'valuation_range': {
                'low': adjusted_valuation * 0.85,
                'high': adjusted_valuation * 1.15
            }
        }

    def calculate_dividend_discount_model(self, dividend: float, growth_rate: float,
                                        required_return: float) -> Dict[str, Any]:
        """计算股利折现模型"""
        if required_return <= growth_rate:
            return {'error': '要求回报率必须大于增长率'}

        # Gordon增长模型
        intrinsic_value = dividend * (1 + growth_rate) / (required_return - growth_rate)

        return {
            'valuation_type': 'dividend_discount',
            'current_dividend': dividend,
            'growth_rate': growth_rate,
            'required_return': required_return,
            'intrinsic_value': intrinsic_value,
            'model': 'Gordon Growth Model'
        }

    def calculate_asset_valuation(self, total_assets: float, total_liabilities: float,
                                 asset_adjustment: float = 1.0) -> Dict[str, Any]:
        """计算资产估值"""
        # 净资产价值
        net_asset_value = total_assets - total_liabilities

        # 调整后净资产价值
        adjusted_nav = net_asset_value * asset_adjustment

        return {
            'valuation_type': 'asset_valuation',
            'total_assets': total_assets,
            'total_liabilities': total_liabilities,
            'net_asset_value': net_asset_value,
            'asset_adjustment': asset_adjustment,
            'adjusted_nav': adjusted_nav,
            'asset_coverage_ratio': total_assets / total_liabilities if total_liabilities > 0 else float('inf')
        }

    def _dcf_sensitivity_analysis(self, cash_flows: List[float], base_discount_rate: float,
                                 base_growth_rate: float) -> Dict[str, Any]:
        """DCF敏感性分析"""
        sensitivity_results = {}

        # 折现率敏感性
        discount_rates = [base_discount_rate - 0.01, base_discount_rate, base_discount_rate + 0.01]
        growth_rates = [base_growth_rate - 0.005, base_growth_rate, base_growth_rate + 0.005]

        for dr in discount_rates:
            for gr in growth_rates:
                if dr > gr:
                    dcf_result = self.calculate_dcf(cash_flows, dr, gr)
                    key = f"dr_{dr:.1%}_gr_{gr:.1%}"
                    sensitivity_results[key] = dcf_result.get('total_enterprise_value', 0)

        return sensitivity_results


class FinancialToolsManager:
    """金融工具管理器"""

    def __init__(self):
        """初始化工具管理器"""
        self.logger = logging.getLogger(__name__)

        # 注册工具
        self.tools = {
            'technical_indicators': TechnicalIndicatorTools(),
            'risk_assessment': RiskAssessmentTools(),
            'valuation': ValuationTools()
        }

        # 工具使用统计
        self.usage_stats = {tool_name: 0 for tool_name in self.tools.keys()}

    def get_available_tools(self) -> Dict[str, str]:
        """获取可用工具列表"""
        return {name: tool.description for name, tool in self.tools.items()}

    def execute_tool(self, tool_name: str, **kwargs) -> Dict[str, Any]:
        """执行指定工具"""
        try:
            if tool_name not in self.tools:
                return {'error': f'工具 {tool_name} 不存在'}

            # 更新使用统计
            self.usage_stats[tool_name] += 1

            # 执行工具
            result = self.tools[tool_name].execute(**kwargs)

            # 添加执行信息
            result['tool_name'] = tool_name
            result['execution_time'] = datetime.now().isoformat()

            return result

        except Exception as e:
            self.logger.error(f"执行工具 {tool_name} 失败: {e}")
            return {'error': str(e), 'tool_name': tool_name}

    def get_tool_usage_stats(self) -> Dict[str, Any]:
        """获取工具使用统计"""
        total_usage = sum(self.usage_stats.values())

        return {
            'total_executions': total_usage,
            'tool_usage': self.usage_stats.copy(),
            'usage_percentage': {
                tool: (count / total_usage * 100) if total_usage > 0 else 0
                for tool, count in self.usage_stats.items()
            }
        }

    def calculate_comprehensive_analysis(self, stock_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行综合分析"""
        try:
            comprehensive_result = {
                'analysis_time': datetime.now().isoformat(),
                'technical_analysis': {},
                'risk_analysis': {},
                'valuation_analysis': {},
                'overall_assessment': {}
            }

            # 技术分析
            if 'price_data' in stock_data:
                prices = stock_data['price_data']

                # 计算多个技术指标
                comprehensive_result['technical_analysis'] = {
                    'sma_20': self.execute_tool('technical_indicators', indicator_type='SMA', data=prices, period=20),
                    'ema_12': self.execute_tool('technical_indicators', indicator_type='EMA', data=prices, period=12),
                    'rsi_14': self.execute_tool('technical_indicators', indicator_type='RSI', data=prices, period=14),
                    'macd': self.execute_tool('technical_indicators', indicator_type='MACD', data=prices),
                    'bollinger': self.execute_tool('technical_indicators', indicator_type='BOLL', data=prices)
                }

            # 风险分析
            if 'returns' in stock_data:
                returns = stock_data['returns']

                comprehensive_result['risk_analysis'] = {
                    'var_95': self.execute_tool('risk_assessment', assessment_type='VaR', returns=returns, confidence=0.95),
                    'volatility': self.execute_tool('risk_assessment', assessment_type='volatility', prices=stock_data.get('price_data', [])),
                    'sharpe_ratio': self.execute_tool('risk_assessment', assessment_type='sharpe_ratio', returns=returns),
                    'max_drawdown': self.execute_tool('risk_assessment', assessment_type='max_drawdown', prices=stock_data.get('price_data', []))
                }

            # 估值分析
            if 'financial_data' in stock_data:
                financial = stock_data['financial_data']

                comprehensive_result['valuation_analysis'] = {
                    'pe_valuation': self.execute_tool('valuation', valuation_type='PE_valuation',
                                                    earnings=financial.get('earnings', 0),
                                                    industry_pe=financial.get('industry_pe', 15)),
                    'pb_valuation': self.execute_tool('valuation', valuation_type='PB_valuation',
                                                    book_value=financial.get('book_value', 0),
                                                    industry_pb=financial.get('industry_pb', 1.5)),
                    'asset_valuation': self.execute_tool('valuation', valuation_type='asset_valuation',
                                                       total_assets=financial.get('total_assets', 0),
                                                       total_liabilities=financial.get('total_liabilities', 0))
                }

            # 综合评估
            comprehensive_result['overall_assessment'] = self._generate_overall_assessment(comprehensive_result)

            return comprehensive_result

        except Exception as e:
            self.logger.error(f"综合分析失败: {e}")
            return {'error': str(e)}

    def _generate_overall_assessment(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成综合评估"""
        try:
            assessment = {
                'technical_score': 0,
                'risk_score': 0,
                'valuation_score': 0,
                'overall_score': 0,
                'recommendation': 'hold',
                'key_insights': []
            }

            # 技术分析评分
            technical = analysis_result.get('technical_analysis', {})
            if technical:
                tech_score = 0

                # RSI评分
                rsi_result = technical.get('rsi_14', {})
                if not rsi_result.get('error'):
                    rsi_value = rsi_result.get('current_value', 50)
                    if 30 <= rsi_value <= 70:
                        tech_score += 25
                    elif 20 <= rsi_value <= 80:
                        tech_score += 15

                # MACD评分
                macd_result = technical.get('macd', {})
                if not macd_result.get('error'):
                    if macd_result.get('trend') == 'bullish':
                        tech_score += 25

                assessment['technical_score'] = min(tech_score, 100)

            # 风险分析评分
            risk = analysis_result.get('risk_analysis', {})
            if risk:
                risk_score = 100  # 从满分开始扣分

                # 波动率评分
                vol_result = risk.get('volatility', {})
                if not vol_result.get('error'):
                    vol_level = vol_result.get('volatility_level', 'moderate')
                    if vol_level == 'very_high':
                        risk_score -= 40
                    elif vol_level == 'high':
                        risk_score -= 25
                    elif vol_level == 'moderate':
                        risk_score -= 10

                assessment['risk_score'] = max(risk_score, 0)

            # 估值分析评分
            valuation = analysis_result.get('valuation_analysis', {})
            if valuation:
                val_score = 50  # 基础分

                # PE估值评分
                pe_result = valuation.get('pe_valuation', {})
                if not pe_result.get('error'):
                    val_score += 25

                # PB估值评分
                pb_result = valuation.get('pb_valuation', {})
                if not pb_result.get('error'):
                    val_score += 25

                assessment['valuation_score'] = min(val_score, 100)

            # 综合评分
            scores = [assessment['technical_score'], assessment['risk_score'], assessment['valuation_score']]
            valid_scores = [s for s in scores if s > 0]

            if valid_scores:
                assessment['overall_score'] = sum(valid_scores) / len(valid_scores)

            # 投资建议
            overall_score = assessment['overall_score']
            if overall_score >= 80:
                assessment['recommendation'] = 'strong_buy'
            elif overall_score >= 65:
                assessment['recommendation'] = 'buy'
            elif overall_score >= 45:
                assessment['recommendation'] = 'hold'
            elif overall_score >= 30:
                assessment['recommendation'] = 'sell'
            else:
                assessment['recommendation'] = 'strong_sell'

            return assessment

        except Exception as e:
            self.logger.error(f"生成综合评估失败: {e}")
            return {'error': str(e)}
