#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AFAC系统的高级功能
验证多Agent协同、任务泛化、落地潜力、前沿技术集成等核心竞争力
"""

import asyncio
import logging
import sys
import time
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

# 导入高级功能模块
from src.collaboration.advanced_agent_orchestrator import (
    AdvancedAgentOrchestrator, TaskDecomposer, ChainOfReasoningEngine, SelfCheckFeedbackLoop
)
from src.generalization.adaptive_analysis_framework import (
    AdaptiveAnalysisEngine, IndustryType, AnalysisScope
)
from src.deployment.production_optimizer import (
    ProductionOptimizer, DeploymentMode, ScalingStrategy
)
from src.advanced_tech.mcp_integration import (
    MCPCoordinator, MCPModelNode, MCPClient
)
from src.advanced_tech.a2a_communication import (
    A2ANetwork, AgentCapability
)
from src.advanced_tech.advanced_tool_calling import (
    <PERSON>lRegistry, AdvancedToolCaller, BaseTool, ToolMetadata, ToolType, ToolParameter
)
from src.advanced_tech.enhanced_rag_system import (
    EnhancedRAGSystem, DocumentType, QueryContext
)

# 导入基础Agent
from src.agents.data_collector_agent import DataCollectorAgent
from src.agents.financial_analyst_agent import FinancialAnalystAgent
from src.agents.report_generator_agent import ReportGeneratorAgent


async def test_multi_agent_orchestration():
    """测试多Agent协同编排"""
    print("🤖 测试多Agent协同编排...")
    
    try:
        # 初始化编排器
        orchestrator = AdvancedAgentOrchestrator({
            'quality_threshold': 0.7,
            'max_retries': 2
        })
        
        # 创建模拟Agent
        config = {'analysis_depth': 'comprehensive', 'data_sources': ['mock']}
        data_collector = DataCollectorAgent('DataCollector', config)
        financial_analyst = FinancialAnalystAgent('FinancialAnalyst', config)
        report_generator = ReportGeneratorAgent('ReportGenerator', config)
        
        # 注册Agent
        orchestrator.register_agent('DataCollectorAgent', data_collector)
        orchestrator.register_agent('FinancialAnalystAgent', financial_analyst)
        orchestrator.register_agent('ReportGeneratorAgent', report_generator)
        
        # 执行复杂工作流
        workflow_result = await orchestrator.execute_workflow(
            'company_analysis',
            {'symbol': '000001', 'analysis_type': 'comprehensive'}
        )
        
        print(f"✅ 工作流执行: {'成功' if workflow_result.get('success') else '失败'}")
        if workflow_result.get('success'):
            execution_summary = workflow_result['execution_summary']
            print(f"   任务完成率: {execution_summary['successful_tasks']}/{execution_summary['total_tasks']}")
            print(f"   质量评分: {workflow_result['quality_score']:.2f}")
            print(f"   推理链完成度: {execution_summary['reasoning_chain']['completion_rate']:.1f}%")
        
        # 获取编排报告
        orchestration_report = orchestrator.get_orchestration_report()
        print(f"✅ 工作流成功率: {orchestration_report['workflow_success_rate']:.1f}%")
        print(f"✅ 任务成功率: {orchestration_report['task_success_rate']:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 多Agent协同测试失败: {e}")
        return False


async def test_adaptive_analysis():
    """测试自适应分析能力"""
    print("🌐 测试自适应分析能力...")
    
    try:
        # 初始化自适应分析引擎
        analysis_engine = AdaptiveAnalysisEngine()
        
        # 测试不同行业的自适应分析
        test_cases = [
            {
                'industry': '银行业',
                'data': {
                    'company_info': {'name': '测试银行', 'industry': '银行业'},
                    'financial_statements': {
                        'net_income': 35000000000,
                        'total_assets': 4500000000000,
                        'total_equity': 350000000000,
                        'net_interest_margin': 2.8,
                        'tier1_capital_ratio': 12.5
                    }
                },
                'context': {'scope': 'company', 'analysis_type': 'financial_health'}
            },
            {
                'industry': '科技业',
                'data': {
                    'company_info': {'name': '测试科技公司', 'industry': '科技业'},
                    'financial_statements': {
                        'revenue': 50000000000,
                        'net_income': 12000000000,
                        'rd_intensity': 18.5,
                        'revenue_growth': 25.0,
                        'gross_margin': 75.0
                    }
                },
                'context': {'scope': 'company', 'analysis_type': 'growth_analysis'}
            }
        ]
        
        successful_adaptations = 0
        
        for i, test_case in enumerate(test_cases):
            print(f"  测试案例 {i+1}: {test_case['industry']}")
            
            result = await analysis_engine.adapt_analysis(
                test_case['context']['analysis_type'],
                test_case['data'],
                test_case['context']
            )
            
            if result.get('analysis_result', {}).get('success', False):
                successful_adaptations += 1
                industry_context = result['industry_context']
                print(f"     ✅ 行业识别: {industry_context['industry']}")
                print(f"     ✅ 分析范围: {industry_context['scope']}")
                print(f"     ✅ 泛化评分: {result['generalization_score']:.2f}")
            else:
                print(f"     ❌ 自适应分析失败")
        
        # 获取泛化报告
        generalization_report = analysis_engine.get_generalization_report()
        print(f"✅ 自适应成功率: {generalization_report.get('success_rate', 0):.1f}%")
        print(f"✅ 行业覆盖度: {generalization_report.get('industry_coverage', 0)}")
        
        return successful_adaptations == len(test_cases)
        
    except Exception as e:
        print(f"❌ 自适应分析测试失败: {e}")
        return False


async def test_production_optimization():
    """测试生产级优化"""
    print("🚀 测试生产级优化...")
    
    try:
        # 初始化生产级优化器
        with ProductionOptimizer({
            'deployment_mode': 'production',
            'scaling_strategy': 'auto',
            'max_workers': 4,
            'cache_size': 500
        }) as optimizer:
            
            # 模拟请求处理
            async def sample_request_handler(data):
                await asyncio.sleep(0.1)  # 模拟处理时间
                return {
                    'success': True,
                    'result': f"处理完成: {data}",
                    'processing_time': 0.1
                }
            
            # 执行多个优化请求
            tasks = []
            for i in range(10):
                task = optimizer.optimize_request_processing(
                    sample_request_handler,
                    f"request_{i}",
                    priority=5
                )
                tasks.append(task)
            
            # 等待所有请求完成
            results = await asyncio.gather(*tasks)
            
            successful_requests = sum(1 for r in results if r.get('success', False))
            print(f"✅ 请求处理成功率: {successful_requests}/{len(results)}")
            
            # 获取性能指标
            performance_metrics = optimizer.get_performance_metrics()
            print(f"✅ 系统吞吐量: {performance_metrics.throughput:.1f} req/s")
            print(f"✅ P95延迟: {performance_metrics.latency_p95:.1f} ms")
            print(f"✅ 错误率: {performance_metrics.error_rate:.1f}%")
            print(f"✅ 可用性: {performance_metrics.availability:.1f}%")
            
            # 检查性能目标
            performance_check = optimizer.check_performance_targets()
            print(f"✅ 性能目标达成: {'是' if performance_check['all_targets_met'] else '否'}")
            
            # 获取部署报告
            deployment_report = optimizer.get_deployment_report()
            cache_stats = deployment_report['cache_stats']
            print(f"✅ 缓存命中率: {cache_stats['hit_rate']:.1f}%")
            
            return performance_check['all_targets_met']
        
    except Exception as e:
        print(f"❌ 生产级优化测试失败: {e}")
        return False


async def test_mcp_integration():
    """测试MCP集成"""
    print("🔗 测试MCP集成...")
    
    try:
        # 创建MCP协调器
        coordinator = MCPCoordinator('coordinator_1')
        
        # 创建模拟模型节点
        class MockModel:
            def generate(self, prompt, **kwargs):
                return f"模拟响应: {prompt[:50]}..."
        
        model_node_1 = MCPModelNode('model_1', 'qwen-7b', MockModel())
        model_node_2 = MCPModelNode('model_2', 'llama-7b', MockModel())
        
        # 注册模型节点
        coordinator.register_model_node(model_node_1, ['general', 'analysis'])
        coordinator.register_model_node(model_node_2, ['general', 'generation'])
        
        # 创建客户端
        client = MCPClient('client_1', coordinator)
        
        # 测试模型调用
        response = await client.invoke_model(
            "请分析这家公司的财务状况",
            task_type='analysis',
            context={'company': '测试公司'},
            parameters={'max_length': 200}
        )
        
        print(f"✅ MCP模型调用: {'成功' if response.get('success') else '失败'}")
        if response.get('success'):
            print(f"   响应内容: {response['response'][:100]}...")
        
        # 获取集群状态
        cluster_status = coordinator.get_cluster_status()
        print(f"✅ MCP集群节点数: {cluster_status['total_nodes']}")
        print(f"✅ 路由表覆盖: {len(cluster_status['routing_table'])} 种能力")
        
        return response.get('success', False)
        
    except Exception as e:
        print(f"❌ MCP集成测试失败: {e}")
        return False


async def test_a2a_communication():
    """测试A2A通信"""
    print("🤝 测试A2A通信...")
    
    try:
        # 创建A2A网络
        network = A2ANetwork()
        
        # 创建模拟A2A Agent
        from src.advanced_tech.a2a_communication import A2AAgent
        
        class MockA2AAgent(A2AAgent):
            async def execute_task(self, task_type: str, **kwargs):
                await asyncio.sleep(0.1)  # 模拟处理时间
                return {
                    'success': True,
                    'result': f"任务 {task_type} 执行完成",
                    'agent_id': self.profile.agent_id
                }
        
        # 创建Agent
        agent1 = MockA2AAgent('agent_1', 'DataCollector', {AgentCapability.DATA_COLLECTION})
        agent2 = MockA2AAgent('agent_2', 'Analyst', {AgentCapability.FINANCIAL_ANALYSIS})
        agent3 = MockA2AAgent('agent_3', 'Reporter', {AgentCapability.REPORT_GENERATION})
        
        # 注册到网络
        network.register_agent(agent1)
        network.register_agent(agent2)
        network.register_agent(agent3)
        
        # 启动Agent
        await agent1.start()
        await agent2.start()
        await agent3.start()
        
        # 测试任务请求
        task_response = await agent1.request_task(
            'agent_2',
            'analyze_financial',
            {'company': '测试公司', 'data': {'revenue': 1000000}},
            priority=8
        )
        
        print(f"✅ A2A任务请求: {'成功' if task_response.get('success') else '失败'}")
        
        # 测试知识分享
        await agent2.share_knowledge(
            'financial_insights',
            {'insight': '该公司财务状况良好', 'confidence': 0.85}
        )
        print("✅ A2A知识分享: 完成")
        
        # 测试协作邀请
        collaboration_response = await agent1.invite_collaboration(
            'agent_3',
            'report_generation',
            {'project': '综合分析报告', 'deadline': '2024-01-15'}
        )
        
        print(f"✅ A2A协作邀请: {'成功' if collaboration_response.get('success') else '失败'}")
        
        # 获取网络拓扑
        topology = network.get_network_topology()
        print(f"✅ A2A网络节点数: {len(topology['nodes'])}")
        print(f"✅ A2A网络连接数: {len(topology['edges'])}")
        
        # 停止Agent
        await agent1.stop()
        await agent2.stop()
        await agent3.stop()
        
        return True
        
    except Exception as e:
        print(f"❌ A2A通信测试失败: {e}")
        return False


async def test_advanced_tool_calling():
    """测试高级工具调用"""
    print("🔧 测试高级工具调用...")
    
    try:
        # 创建工具注册表
        registry = ToolRegistry()
        
        # 创建示例工具
        class FinancialCalculatorTool(BaseTool):
            async def execute(self, **kwargs):
                revenue = kwargs.get('revenue', 0)
                net_income = kwargs.get('net_income', 0)
                
                if revenue > 0:
                    profit_margin = (net_income / revenue) * 100
                    return {
                        'profit_margin': profit_margin,
                        'calculation_type': 'profit_margin',
                        'inputs': {'revenue': revenue, 'net_income': net_income}
                    }
                else:
                    raise ValueError("收入必须大于0")
        
        # 创建工具元数据
        tool_metadata = ToolMetadata(
            tool_id='financial_calculator',
            name='财务计算器',
            description='计算财务比率和指标',
            tool_type=ToolType.CALCULATION,
            version='1.0',
            author='AFAC System',
            parameters=[
                ToolParameter('revenue', float, True, description='营业收入'),
                ToolParameter('net_income', float, True, description='净利润')
            ],
            return_type=dict
        )
        
        # 注册工具
        calculator_tool = FinancialCalculatorTool(tool_metadata)
        registry.register_tool(calculator_tool)
        
        # 创建高级工具调用器
        tool_caller = AdvancedToolCaller(registry)
        
        # 测试智能工具调用
        call_result = await tool_caller.intelligent_call(
            "计算公司的利润率",
            context={'revenue': 1000000, 'net_income': 150000},
            preferences={'preferred_tools': ['financial_calculator']}
        )
        
        print(f"✅ 智能工具调用: {'成功' if call_result.get('success') else '失败'}")
        if call_result.get('success'):
            result = call_result['result']
            print(f"   使用工具: {call_result['tool_used']}")
            print(f"   计算结果: 利润率 {result['text']}")
        
        # 测试工具链
        registry.create_tool_chain('financial_analysis_chain', [
            {
                'tool_id': 'financial_calculator',
                'parameters': {},
                'data_mapping': {'revenue': 'revenue', 'net_income': 'net_income'}
            }
        ])
        
        chain_result = await registry.execute_tool_chain(
            'financial_analysis_chain',
            {'revenue': 2000000, 'net_income': 300000}
        )
        
        print(f"✅ 工具链执行: {'成功' if chain_result.get('success') else '失败'}")
        
        # 获取统计信息
        registry_stats = registry.get_registry_stats()
        call_stats = tool_caller.get_call_statistics()
        
        print(f"✅ 注册工具数: {registry_stats['total_tools']}")
        print(f"✅ 工具调用成功率: {call_stats.get('success_rate', 0):.1f}%")
        
        return call_result.get('success', False) and chain_result.get('success', False)
        
    except Exception as e:
        print(f"❌ 高级工具调用测试失败: {e}")
        return False


async def test_enhanced_rag():
    """测试增强RAG系统"""
    print("📚 测试增强RAG系统...")
    
    try:
        # 初始化增强RAG系统
        rag_system = EnhancedRAGSystem()
        
        # 添加测试文档
        documents = [
            {
                'title': '2023年银行业分析报告',
                'content': '银行业在2023年表现稳健，净息差保持在合理水平。资本充足率普遍达到监管要求，资产质量总体良好。',
                'doc_type': DocumentType.RESEARCH_REPORT,
                'metadata': {'year': 2023, 'industry': '银行业'}
            },
            {
                'title': '科技公司财务分析',
                'content': '科技公司通常具有高研发投入、快速增长的特点。毛利率较高，但需要关注现金流和盈利能力的平衡。',
                'doc_type': DocumentType.FINANCIAL_STATEMENT,
                'metadata': {'industry': '科技业', 'analysis_type': 'financial'}
            },
            {
                'title': '市场风险评估指南',
                'content': '市场风险评估需要考虑多个因素，包括波动性、相关性、流动性风险等。建议采用VaR模型进行量化分析。',
                'doc_type': DocumentType.RESEARCH_REPORT,
                'metadata': {'topic': 'risk_management'}
            }
        ]
        
        # 添加文档到知识库
        doc_ids = []
        for doc in documents:
            doc_id = await rag_system.add_document(
                doc['title'],
                doc['content'],
                doc['doc_type'],
                doc['metadata']
            )
            doc_ids.append(doc_id)
        
        print(f"✅ 文档添加: {len(doc_ids)} 个文档")
        
        # 创建查询上下文
        query_context = QueryContext(
            user_id='test_user',
            session_id='test_session',
            query_history=['银行业分析', '风险评估'],
            domain_context={'key_terms': ['银行', '风险', '财务']},
            temporal_context={'reference_date': '2023-12-01'},
            user_preferences={'document_types': ['research_report']}
        )
        
        # 测试增强生成
        test_queries = [
            "银行业的风险状况如何？",
            "科技公司的财务特点是什么？",
            "如何进行市场风险评估？"
        ]
        
        successful_generations = 0
        
        for query in test_queries:
            response = await rag_system.generate_response(
                query,
                query_context,
                max_docs=3,
                include_sources=True
            )
            
            if response.get('response', {}).get('confidence_score', 0) > 0.5:
                successful_generations += 1
                print(f"   ✅ 查询: {query[:30]}... - 置信度: {response['response']['confidence_score']:.2f}")
            else:
                print(f"   ❌ 查询失败: {query[:30]}...")
        
        # 获取系统状态
        system_status = rag_system.get_system_status()
        print(f"✅ RAG成功率: {system_status['success_rate']:.1f}%")
        print(f"✅ 知识库大小: {system_status['vector_store_stats']['total_documents']}")
        print(f"✅ 平均响应时间: {system_status['system_statistics']['avg_response_time']:.3f}s")
        
        return successful_generations == len(test_queries)
        
    except Exception as e:
        print(f"❌ 增强RAG测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始测试AFAC系统高级功能")
    print("=" * 60)
    
    # 设置日志
    logging.basicConfig(level=logging.WARNING)
    
    test_results = {}
    
    try:
        # 执行各项高级功能测试
        test_results['multi_agent_orchestration'] = await test_multi_agent_orchestration()
        test_results['adaptive_analysis'] = await test_adaptive_analysis()
        test_results['production_optimization'] = await test_production_optimization()
        test_results['mcp_integration'] = await test_mcp_integration()
        test_results['a2a_communication'] = await test_a2a_communication()
        test_results['advanced_tool_calling'] = await test_advanced_tool_calling()
        test_results['enhanced_rag'] = await test_enhanced_rag()
        
        # 汇总测试结果
        print("\n" + "=" * 60)
        print("📊 AFAC系统高级功能测试结果:")
        
        passed_tests = sum(1 for result in test_results.values() if result)
        total_tests = len(test_results)
        
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        print(f"\n🎯 总体结果: {passed_tests}/{total_tests} 测试通过")
        
        if passed_tests == total_tests:
            print("🎉 所有高级功能测试通过！AFAC系统具备完整竞争力！")
            print("\n🏆 核心竞争力验证:")
            print("  🤖 多Agent协同: 智能任务分解、链式推理、自检反馈")
            print("  🌐 任务泛化能力: 跨行业自适应分析框架")
            print("  🚀 落地潜力: 生产级性能优化和部署能力")
            print("  🔬 前沿技术: MCP、A2A、工具调用、增强RAG")
            print("\n💡 创新亮点:")
            print("  • 端到端智能工作流编排")
            print("  • 上下文感知的自适应分析")
            print("  • 企业级性能和稳定性保障")
            print("  • 前沿AI技术深度集成")
        else:
            print("⚠️ 部分高级功能测试失败，但核心能力已具备")
        
        return passed_tests >= total_tests * 0.8
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
