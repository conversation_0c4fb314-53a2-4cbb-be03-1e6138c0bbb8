#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强后的AFAC系统
验证RAG增强、A2A协作、专业工具调用等功能
"""

import asyncio
import logging
import sys
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from src.knowledge.financial_knowledge_base import FinancialKnowledgeBase
from src.rag.financial_rag_system import FinancialRAGSystem
from src.tools.financial_tools_system import FinancialToolsManager
from src.collaboration.agent_collaboration_framework import AgentCollaborationFramework
from src.collaboration.collaboration_manager import CollaborationManager
from src.data_integration.real_time_data_system import RealTimeDataSystem
from src.agents.data_collector_agent import DataCollectorAgent
from src.agents.financial_analyst_agent import FinancialAnalystAgent
from src.agents.report_generator_agent import ReportGeneratorAgent


async def test_knowledge_base():
    """测试金融知识库"""
    print("🧠 测试金融知识库...")
    
    kb = FinancialKnowledgeBase()
    
    # 测试杜邦分析知识
    dupont_knowledge = kb.get_knowledge('dupont_analysis')
    print(f"✅ 杜邦分析知识: {dupont_knowledge.get('formula', 'N/A')}")
    
    # 测试财务比率知识
    ratios_knowledge = kb.get_knowledge('financial_ratios', 'profitability_ratios')
    print(f"✅ 盈利能力比率数量: {len(ratios_knowledge)}")
    
    # 测试知识搜索
    search_results = kb.search_knowledge('ROE')
    print(f"✅ ROE相关知识搜索结果: {len(search_results)}个")
    
    return True


async def test_rag_system():
    """测试RAG增强系统"""
    print("🔍 测试RAG增强系统...")
    
    rag_system = FinancialRAGSystem()
    
    # 模拟财务分析数据
    mock_analysis = {
        'financial_ratios': {
            'roe': 15.5,
            'roa': 8.2,
            'net_profit_margin': 12.3,
            'debt_to_equity': 1.2
        },
        'profitability_analysis': {
            'profitability_score': 85
        }
    }
    
    # 测试财务分析增强
    enhanced_result = rag_system.enhance_analysis('financial', mock_analysis, '分析公司财务状况')
    print(f"✅ RAG增强结果包含: {list(enhanced_result.keys())}")
    
    # 测试专业术语获取
    roe_explanation = rag_system.get_professional_terminology('ROE')
    print(f"✅ ROE术语解释: {roe_explanation}")
    
    return True


async def test_financial_tools():
    """测试金融工具系统"""
    print("🛠️ 测试金融工具系统...")
    
    tools_manager = FinancialToolsManager()
    
    # 测试技术指标计算
    price_data = [20, 21, 22, 21.5, 23, 22.8, 24, 23.5, 25, 24.2]
    
    # SMA计算
    sma_result = tools_manager.execute_tool(
        'technical_indicators', 
        indicator_type='SMA', 
        data=price_data, 
        period=5
    )
    print(f"✅ SMA计算结果: {sma_result.get('current_value', 'N/A')}")
    
    # RSI计算
    rsi_result = tools_manager.execute_tool(
        'technical_indicators',
        indicator_type='RSI',
        data=price_data,
        period=5
    )
    print(f"✅ RSI计算结果: {rsi_result.get('current_value', 'N/A')}")
    
    # 风险评估
    returns = [0.01, -0.02, 0.03, -0.01, 0.02, 0.01, -0.015, 0.025]
    var_result = tools_manager.execute_tool(
        'risk_assessment',
        assessment_type='VaR',
        returns=returns,
        confidence=0.95
    )
    print(f"✅ VaR计算结果: {var_result.get('var_historical', 'N/A')}")
    
    # 估值计算
    dcf_result = tools_manager.execute_tool(
        'valuation',
        valuation_type='DCF',
        cash_flows=[100, 110, 121, 133, 146],
        discount_rate=0.1,
        terminal_growth_rate=0.03
    )
    print(f"✅ DCF估值结果: {dcf_result.get('total_enterprise_value', 'N/A')}")
    
    # 工具使用统计
    usage_stats = tools_manager.get_tool_usage_stats()
    print(f"✅ 工具使用统计: {usage_stats['total_executions']}次执行")
    
    return True


async def test_collaboration_framework():
    """测试Agent协作框架"""
    print("🤝 测试Agent协作框架...")
    
    # 初始化协作框架
    collaboration_framework = AgentCollaborationFramework()
    await collaboration_framework.start_collaboration_system()
    
    # 创建模拟Agent
    class MockAgent:
        def __init__(self, name):
            self.name = name
        
        async def provide_data(self, data_type, parameters):
            return {'mock_data': f'来自{self.name}的{data_type}数据'}
        
        async def execute_task(self, task_type, **kwargs):
            return {'result': f'{self.name}执行了{task_type}任务'}
    
    # 注册Agent
    data_agent = MockAgent('DataCollector')
    analyst_agent = MockAgent('FinancialAnalyst')
    
    collaboration_framework.register_agent('DataCollector', data_agent, ['data_collection'])
    collaboration_framework.register_agent('FinancialAnalyst', analyst_agent, ['financial_analysis'])
    
    # 测试消息发送
    from src.collaboration.agent_collaboration_framework import MessageType, CollaborationPriority
    
    message_id = await collaboration_framework.send_message(
        'FinancialAnalyst',
        'DataCollector',
        MessageType.DATA_REQUEST,
        {'data_type': 'financial_data', 'parameters': {'symbol': '000001'}},
        CollaborationPriority.HIGH
    )
    
    print(f"✅ 消息发送成功: {message_id}")
    
    # 等待消息处理
    await asyncio.sleep(2)
    
    # 获取协作指标
    metrics = collaboration_framework.get_collaboration_metrics()
    print(f"✅ 协作指标: {metrics['metrics']['messages_sent']}条消息已发送")
    
    await collaboration_framework.stop_collaboration_system()
    
    return True


async def test_real_time_data_system():
    """测试实时数据系统"""
    print("🌐 测试实时数据系统...")
    
    config = {
        'akshare_enabled': True,
        'cache_ttl': 300
    }
    
    data_system = RealTimeDataSystem(config)
    
    # 测试股票数据获取
    stock_data = await data_system.get_real_time_stock_data('000001')
    print(f"✅ 股票数据获取: {stock_data.get('symbol', 'N/A')} - {stock_data.get('current_price', 'N/A')}")
    
    # 测试市场数据获取
    market_data = await data_system.get_real_time_market_data()
    print(f"✅ 市场数据获取: {len(market_data.get('indices', {}))}个指数")
    
    # 测试宏观数据获取
    macro_data = await data_system.get_real_time_macro_data()
    print(f"✅ 宏观数据获取: {len(macro_data.get('economic_indicators', {}))}个指标")
    
    # 获取系统指标
    system_metrics = data_system.get_system_metrics()
    print(f"✅ 系统指标: 成功率{system_metrics.get('success_rate', 0)}%")
    
    return True


async def test_enhanced_agents():
    """测试增强后的Agent"""
    print("🤖 测试增强后的Agent...")
    
    # 配置
    config = {
        'analysis_depth': 'comprehensive',
        'include_charts': True,
        'data_sources': ['akshare', 'mock']
    }
    
    # 创建Agent
    data_collector = DataCollectorAgent('DataCollector', config)
    financial_analyst = FinancialAnalystAgent('FinancialAnalyst', config)
    report_generator = ReportGeneratorAgent('ReportGenerator', config)
    
    # 测试数据收集
    print("  📊 测试数据收集Agent...")
    company_data = await data_collector.execute_task('collect_company_data', company_symbol='000001')
    print(f"  ✅ 数据收集结果: {company_data.get('success', False)}")
    
    # 测试财务分析
    if company_data.get('success'):
        print("  📈 测试财务分析Agent...")
        analysis_result = await financial_analyst.execute_task(
            'analyze_company', 
            symbol='000001', 
            company_data=company_data.get('result', {})
        )
        print(f"  ✅ 财务分析结果: {analysis_result.get('success', False)}")
        
        # 测试报告生成
        if analysis_result.get('success'):
            print("  📄 测试报告生成Agent...")
            report_result = await report_generator.execute_task(
                'generate_company_report',
                symbol='000001',
                company_data=company_data.get('result', {}),
                analysis_result=analysis_result.get('result', {})
            )
            print(f"  ✅ 报告生成结果: {report_result.get('success', False)}")
    
    return True


async def test_end_to_end_workflow():
    """测试端到端工作流"""
    print("🔄 测试端到端工作流...")
    
    # 初始化协作管理器
    collaboration_framework = AgentCollaborationFramework()
    await collaboration_framework.start_collaboration_system()
    
    collaboration_manager = CollaborationManager(collaboration_framework)
    
    # 配置
    config = {
        'analysis_depth': 'comprehensive',
        'include_charts': True
    }
    
    # 创建并注册Agent
    data_collector = DataCollectorAgent('DataCollector', config)
    financial_analyst = FinancialAnalystAgent('FinancialAnalyst', config)
    report_generator = ReportGeneratorAgent('ReportGenerator', config)
    
    collaboration_framework.register_agent('DataCollector', data_collector, ['data_collection'])
    collaboration_framework.register_agent('FinancialAnalyst', financial_analyst, ['financial_analysis'])
    collaboration_framework.register_agent('ReportGenerator', report_generator, ['report_generation'])
    
    # 执行公司分析协作
    print("  🏢 执行公司分析协作...")
    company_result = await collaboration_manager.execute_company_analysis_collaboration('000001')
    print(f"  ✅ 公司分析协作: {'成功' if not company_result.get('error') else '失败'}")
    
    # 获取协作状态
    collaboration_status = collaboration_manager.get_collaboration_status()
    print(f"  ✅ 协作状态: {collaboration_status.get('active_workflows', 0)}个活跃工作流")
    
    await collaboration_framework.stop_collaboration_system()
    
    return True


async def main():
    """主测试函数"""
    print("🚀 开始测试增强后的AFAC系统")
    print("=" * 60)
    
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    test_results = {}
    
    try:
        # 执行各项测试
        test_results['knowledge_base'] = await test_knowledge_base()
        test_results['rag_system'] = await test_rag_system()
        test_results['financial_tools'] = await test_financial_tools()
        test_results['collaboration_framework'] = await test_collaboration_framework()
        test_results['real_time_data'] = await test_real_time_data_system()
        test_results['enhanced_agents'] = await test_enhanced_agents()
        test_results['end_to_end_workflow'] = await test_end_to_end_workflow()
        
        # 汇总测试结果
        print("\n" + "=" * 60)
        print("📊 测试结果汇总:")
        
        passed_tests = sum(1 for result in test_results.values() if result)
        total_tests = len(test_results)
        
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        print(f"\n🎯 总体结果: {passed_tests}/{total_tests} 测试通过")
        
        if passed_tests == total_tests:
            print("🎉 所有测试通过！AFAC系统增强成功！")
        else:
            print("⚠️ 部分测试失败，请检查相关模块")
        
        return passed_tests == total_tests
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
