#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AFAC系统的开源模型集成
验证模型管理、提示词工程、本地推理等功能
"""

import asyncio
import logging
import sys
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from src.models.model_manager import OpenSourceModelManager, ModelConfig
from src.models.prompt_engineering import FinancialPromptEngineer, TaskType, ModelType
from src.models.local_inference_engine import LocalInferenceEngine, InferenceConfig
from src.models.afac_model_integration import AFACModelIntegration


async def test_model_manager():
    """测试模型管理器"""
    print("🤖 测试开源模型管理器...")
    
    try:
        # 初始化模型管理器
        model_manager = OpenSourceModelManager()
        
        # 获取可用模型
        available_models = model_manager.get_available_models()
        print(f"✅ 可用模型: {available_models}")
        
        # 加载默认模型
        success = await model_manager.load_model('qwen-7b-chat')
        print(f"✅ 模型加载: {'成功' if success else '失败'}")
        
        if success:
            # 测试生成响应
            response = await model_manager.generate_response(
                "请分析一家公司的ROE为15%的财务表现",
                'qwen-7b-chat',
                max_length=512,
                temperature=0.7
            )
            print(f"✅ 生成响应: {response[:100]}...")
            
            # 获取推理统计
            stats = model_manager.get_inference_stats()
            print(f"✅ 推理统计: {stats['total_requests']}次请求")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型管理器测试失败: {e}")
        return False


async def test_prompt_engineering():
    """测试提示词工程"""
    print("📝 测试提示词工程...")
    
    try:
        # 初始化提示词工程师
        prompt_engineer = FinancialPromptEngineer()
        
        # 测试财务分析提示词
        financial_prompt = prompt_engineer.get_prompt(
            TaskType.FINANCIAL_ANALYSIS,
            ModelType.QWEN,
            company_name="测试公司",
            symbol="000001",
            industry="金融业",
            financial_data="营业收入: 1000万元\n净利润: 150万元\n总资产: 5000万元"
        )
        print(f"✅ 财务分析提示词长度: {len(financial_prompt)}字符")
        
        # 测试投资建议提示词
        investment_prompt = prompt_engineer.get_prompt(
            TaskType.INVESTMENT_ADVICE,
            ModelType.QWEN,
            company_name="测试公司",
            symbol="000001",
            current_price=25.68,
            analysis_result="ROE: 15%, 盈利能力良好",
            market_context="市场环境稳定"
        )
        print(f"✅ 投资建议提示词长度: {len(investment_prompt)}字符")
        
        # 测试提示词优化
        optimized_prompt = prompt_engineer.optimize_for_model(
            "请分析公司财务状况", ModelType.QWEN
        )
        print(f"✅ 提示词优化: {optimized_prompt[:50]}...")
        
        # 测试提示词验证
        validation = prompt_engineer.validate_prompt(financial_prompt)
        print(f"✅ 提示词验证: 分数{validation['score']}, 有效性{validation['is_valid']}")
        
        # 获取可用模板
        templates = prompt_engineer.get_available_templates()
        print(f"✅ 可用模板数量: {len(templates)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 提示词工程测试失败: {e}")
        return False


async def test_inference_engine():
    """测试推理引擎"""
    print("⚡ 测试本地推理引擎...")
    
    try:
        # 初始化推理引擎
        inference_engine = LocalInferenceEngine(max_workers=2)
        
        # 初始化模型
        success = await inference_engine.initialize_model(
            'qwen-7b-chat', 
            'Qwen/Qwen-7B-Chat',
            framework='transformers'
        )
        print(f"✅ 推理引擎初始化: {'成功' if success else '失败'}")
        
        if success:
            # 测试文本生成
            config = InferenceConfig(
                max_new_tokens=256,
                temperature=0.7,
                top_p=0.9
            )
            
            response = await inference_engine.generate_text(
                "请分析某公司ROE为18%的财务表现",
                config,
                framework='transformers'
            )
            print(f"✅ 文本生成: {response[:100]}...")
            
            # 测试流式生成
            print("✅ 流式生成测试:")
            async for chunk in inference_engine.generate_stream(
                "请提供投资建议", config, framework='transformers'
            ):
                print(f"   {chunk}", end='', flush=True)
            print()  # 换行
            
            # 测试批量生成
            prompts = [
                "分析公司盈利能力",
                "评估投资风险",
                "提供投资建议"
            ]
            batch_results = await inference_engine.batch_generate(prompts, config)
            print(f"✅ 批量生成: {len(batch_results)}个结果")
            
            # 获取推理统计
            stats = inference_engine.get_inference_stats()
            print(f"✅ 推理统计: 成功率{stats['success_rate']:.1f}%")
            
            # 测试性能优化
            optimization = inference_engine.optimize_inference(True)
            print(f"✅ 性能优化: 提升{optimization['performance_improvement']:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 推理引擎测试失败: {e}")
        return False


async def test_afac_integration():
    """测试AFAC模型集成"""
    print("🔗 测试AFAC模型集成...")
    
    try:
        # 初始化集成器
        integration = AFACModelIntegration()
        
        # 初始化默认模型
        success = await integration.initialize_default_model('qwen-7b-chat')
        print(f"✅ 默认模型初始化: {'成功' if success else '失败'}")
        
        if success:
            # 模拟公司数据
            company_data = {
                'symbol': '000001',
                'company_info': {
                    'name': '测试公司',
                    'industry': '金融业',
                    'market_cap': 50000000000
                },
                'financial_statements': {
                    'revenue': 10000000000,
                    'net_income': 1500000000,
                    'total_assets': 100000000000,
                    'total_equity': 30000000000
                },
                'stock_data': {
                    'current_price': 25.68,
                    'pe_ratio': 12.5,
                    'pb_ratio': 1.8
                }
            }
            
            # 测试LLM财务分析
            analysis_result = await integration.financial_analysis_with_llm(
                company_data, "comprehensive"
            )
            print(f"✅ LLM财务分析: {'成功' if analysis_result.get('success') else '失败'}")
            
            if analysis_result.get('success'):
                # 测试LLM投资建议
                advice_result = await integration.investment_advice_with_llm(
                    company_data, analysis_result['analysis_result']
                )
                print(f"✅ LLM投资建议: {'成功' if advice_result.get('success') else '失败'}")
                
                # 测试LLM风险评估
                risk_result = await integration.risk_assessment_with_llm(
                    company_data, analysis_result['analysis_result']
                )
                print(f"✅ LLM风险评估: {'成功' if risk_result.get('success') else '失败'}")
                
                # 测试LLM报告生成
                report_result = await integration.generate_report_with_llm({
                    'title': '测试公司研究报告',
                    'company_info': company_data['company_info'],
                    'analysis_result': analysis_result['analysis_result']
                }, "company_research")
                print(f"✅ LLM报告生成: {'成功' if report_result.get('success') else '失败'}")
            
            # 测试模型切换
            switch_success = await integration.switch_model('chatglm3-6b')
            print(f"✅ 模型切换: {'成功' if switch_success else '失败'}")
            
            # 获取集成状态
            status = integration.get_integration_status()
            print(f"✅ 当前模型: {status['current_model']}")
            print(f"✅ 已加载模型: {len(status['loaded_models'])}个")
            print(f"✅ 总请求数: {status['integration_stats']['total_requests']}")
        
        return True
        
    except Exception as e:
        print(f"❌ AFAC集成测试失败: {e}")
        return False


async def test_enhanced_agents():
    """测试增强后的Agent"""
    print("🤖 测试LLM增强的Agent...")
    
    try:
        from src.agents.financial_analyst_agent import FinancialAnalystAgent
        from src.agents.report_generator_agent import ReportGeneratorAgent
        
        # 配置
        config = {
            'analysis_depth': 'comprehensive',
            'include_charts': True
        }
        
        # 创建Agent
        analyst = FinancialAnalystAgent('FinancialAnalyst', config)
        report_generator = ReportGeneratorAgent('ReportGenerator', config)
        
        # 模拟公司数据
        company_data = {
            'symbol': '000001',
            'company_info': {
                'name': 'LLM测试公司',
                'industry': '科技业'
            },
            'financial_statements': {
                'revenue': 5000000000,
                'net_income': *********,
                'total_assets': 20000000000
            }
        }
        
        # 测试LLM增强的财务分析
        print("  📊 测试LLM增强财务分析...")
        analysis_result = await analyst.execute_task(
            'analyze_company',
            symbol='000001',
            company_data=company_data
        )
        print(f"  ✅ 财务分析: {'成功' if analysis_result.get('success') else '失败'}")
        
        if analysis_result.get('success'):
            analysis_data = analysis_result['result']
            
            # 检查LLM增强内容
            if 'llm_enhancement' in analysis_data:
                print(f"  ✅ LLM增强分析: 已包含")
                print(f"  ✅ LLM见解数量: {len(analysis_data.get('llm_insights', []))}")
            
            if 'llm_investment_advice' in analysis_data:
                print(f"  ✅ LLM投资建议: 已包含")
            
            # 测试LLM增强的报告生成
            print("  📄 测试LLM增强报告生成...")
            report_result = await report_generator.execute_task(
                'generate_company_report',
                symbol='000001',
                company_data=company_data,
                analysis_result=analysis_data
            )
            print(f"  ✅ 报告生成: {'成功' if report_result.get('success') else '失败'}")
            
            if report_result.get('success'):
                report_data = report_result['result']
                
                # 检查LLM增强内容
                if 'llm_enhanced_content' in report_data:
                    print(f"  ✅ LLM增强报告: 已包含")
                
                if report_data.get('metadata', {}).get('llm_enhanced'):
                    print(f"  ✅ LLM增强标记: 已设置")
        
        return True
        
    except Exception as e:
        print(f"❌ 增强Agent测试失败: {e}")
        return False


async def test_model_comparison():
    """测试不同模型的性能对比"""
    print("⚖️ 测试模型性能对比...")
    
    try:
        integration = AFACModelIntegration()
        
        # 测试不同模型
        models_to_test = ['qwen-7b-chat', 'chatglm3-6b', 'baichuan2-7b-chat']
        test_prompt = "请分析一家公司ROE为20%，净利润率为15%的财务表现"
        
        results = {}
        
        for model_name in models_to_test:
            try:
                # 切换模型
                await integration.switch_model(model_name)
                
                # 记录开始时间
                start_time = datetime.now()
                
                # 生成响应
                response = await integration.model_manager.generate_response(
                    test_prompt, model_name
                )
                
                # 计算响应时间
                response_time = (datetime.now() - start_time).total_seconds()
                
                results[model_name] = {
                    'response_time': response_time,
                    'response_length': len(response),
                    'success': True
                }
                
                print(f"  ✅ {model_name}: {response_time:.2f}s, {len(response)}字符")
                
            except Exception as e:
                results[model_name] = {
                    'success': False,
                    'error': str(e)
                }
                print(f"  ❌ {model_name}: 失败 - {e}")
        
        # 性能总结
        successful_models = [name for name, result in results.items() if result.get('success')]
        print(f"✅ 成功测试模型: {len(successful_models)}/{len(models_to_test)}")
        
        if successful_models:
            fastest_model = min(successful_models, 
                              key=lambda x: results[x]['response_time'])
            print(f"✅ 最快模型: {fastest_model} ({results[fastest_model]['response_time']:.2f}s)")
        
        return len(successful_models) > 0
        
    except Exception as e:
        print(f"❌ 模型对比测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始测试AFAC开源模型集成")
    print("=" * 60)
    
    # 设置日志
    logging.basicConfig(level=logging.WARNING)
    
    test_results = {}
    
    try:
        # 执行各项测试
        test_results['model_manager'] = await test_model_manager()
        test_results['prompt_engineering'] = await test_prompt_engineering()
        test_results['inference_engine'] = await test_inference_engine()
        test_results['afac_integration'] = await test_afac_integration()
        test_results['enhanced_agents'] = await test_enhanced_agents()
        test_results['model_comparison'] = await test_model_comparison()
        
        # 汇总测试结果
        print("\n" + "=" * 60)
        print("📊 开源模型集成测试结果:")
        
        passed_tests = sum(1 for result in test_results.values() if result)
        total_tests = len(test_results)
        
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        print(f"\n🎯 总体结果: {passed_tests}/{total_tests} 测试通过")
        
        if passed_tests == total_tests:
            print("🎉 所有测试通过！开源模型集成成功！")
            print("\n🏆 AFAC系统现在支持以下开源模型:")
            print("  • Qwen-7B-Chat (通义千问)")
            print("  • ChatGLM3-6B (智谱清言)")
            print("  • Baichuan2-7B-Chat (百川)")
            print("  • InternLM2-7B-Chat (书生浦语)")
            print("\n✨ 主要功能:")
            print("  • 本地模型推理，无需API调用")
            print("  • 专业金融提示词工程")
            print("  • 多模型性能对比")
            print("  • Agent智能增强")
            print("  • 符合竞赛开源要求")
        else:
            print("⚠️ 部分测试失败，但核心功能正常")
        
        return passed_tests >= total_tests * 0.8
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
