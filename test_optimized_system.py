#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的AFAC系统
验证性能优化、数据质量、分析深度、用户体验、系统稳定性等改进
"""

import asyncio
import logging
import sys
import time
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from src.optimization.performance_optimizer import PerformanceOptimizer
from src.optimization.data_quality_enhancer import DataQualityEnhancer
from src.optimization.analysis_enhancer import AnalysisEnhancer, AnalysisDepth
from src.optimization.user_experience_optimizer import UserExperienceOptimizer
from src.optimization.system_stability_enhancer import SystemStabilityEnhancer


async def test_performance_optimization():
    """测试性能优化"""
    print("⚡ 测试性能优化...")
    
    try:
        # 初始化性能优化器
        optimizer = PerformanceOptimizer({
            'cache_max_size': 1000,
            'max_workers': 4
        })
        
        # 测试模型推理优化
        optimization_result = await optimizer.optimize_model_inference(
            'qwen-7b-chat', 'balanced'
        )
        print(f"✅ 模型推理优化: {'成功' if optimization_result['success'] else '失败'}")
        
        if optimization_result['success']:
            optimizations = optimization_result['optimizations_applied']
            print(f"   应用的优化: {', '.join(optimizations)}")
            
            performance = optimization_result['performance_improvement']
            if 'memory_reduction' in performance:
                print(f"   内存减少: {performance['memory_reduction']*100:.1f}%")
            if 'speed_improvement' in performance:
                print(f"   速度提升: {performance['speed_improvement']:.1f}x")
        
        # 测试缓存功能
        @optimizer.cache_decorator(ttl=300)
        def expensive_calculation(x, y):
            time.sleep(0.1)  # 模拟耗时计算
            return x * y + x ** 2
        
        # 第一次调用（缓存未命中）
        start_time = time.time()
        result1 = expensive_calculation(10, 20)
        first_call_time = time.time() - start_time
        
        # 第二次调用（缓存命中）
        start_time = time.time()
        result2 = expensive_calculation(10, 20)
        second_call_time = time.time() - start_time
        
        print(f"✅ 缓存测试: 第一次{first_call_time:.3f}s, 第二次{second_call_time:.3f}s")
        print(f"   缓存加速: {first_call_time/second_call_time:.1f}x")
        
        # 获取性能报告
        performance_report = optimizer.get_performance_report()
        print(f"✅ 缓存命中率: {performance_report['cache_stats']['hit_rate']:.1f}%")
        
        await optimizer.cleanup()
        return True
        
    except Exception as e:
        print(f"❌ 性能优化测试失败: {e}")
        return False


async def test_data_quality_enhancement():
    """测试数据质量增强"""
    print("📊 测试数据质量增强...")
    
    try:
        # 初始化数据质量增强器
        enhancer = DataQualityEnhancer()
        
        # 模拟原始数据（包含一些问题）
        raw_data = {
            'company_info': {
                'name': '测试公司',
                'symbol': '000001',
                'industry': '金融业',
                'employees': 5000
            },
            'financial_statements': {
                'revenue': 10000000000,
                'net_income': 1500000000,
                'total_assets': 100000000000,
                'total_equity': 30000000000,
                'current_assets': 20000000000,
                'current_liabilities': 15000000000,
                'total_liabilities': 70000000000  # 这会导致资产负债表平衡
            },
            'stock_data': {
                'current_price': 25.68,
                'market_cap': 77040000000,  # 30亿股 * 25.68
                'pe_ratio': 15.2,
                'pb_ratio': 2.57,
                'timestamp': datetime.now().isoformat()
            }
        }
        
        # 执行数据质量增强
        enhanced_data, quality_metrics = await enhancer.enhance_data_quality(raw_data)
        
        print(f"✅ 数据质量增强完成")
        print(f"   总体评分: {quality_metrics.overall_score:.1f}/100")
        print(f"   质量等级: {quality_metrics.quality_level.value}")
        print(f"   完整性: {quality_metrics.completeness:.1f}%")
        print(f"   准确性: {quality_metrics.accuracy:.1f}%")
        print(f"   一致性: {quality_metrics.consistency:.1f}%")
        print(f"   时效性: {quality_metrics.timeliness:.1f}%")
        print(f"   有效性: {quality_metrics.validity:.1f}%")
        
        # 获取质量报告
        quality_report = enhancer.get_quality_report()
        print(f"✅ 验证成功率: {quality_report['validation_success_rate']:.1f}%")
        print(f"✅ 改进率: {quality_report['improvement_rate']:.1f}%")
        
        return quality_metrics.overall_score >= 80
        
    except Exception as e:
        print(f"❌ 数据质量增强测试失败: {e}")
        return False


async def test_analysis_enhancement():
    """测试分析深度增强"""
    print("🔍 测试分析深度增强...")
    
    try:
        # 初始化分析增强器
        enhancer = AnalysisEnhancer()
        
        # 模拟公司数据
        company_data = {
            'company_info': {
                'name': '测试银行',
                'industry': '银行业',
                'employees': 50000
            },
            'financial_statements': {
                'revenue': 150000000000,
                'net_income': 35000000000,
                'total_assets': 4500000000000,
                'total_equity': 350000000000,
                'current_assets': 1000000000000,
                'current_liabilities': 800000000000,
                'retained_earnings': 200000000000,
                'ebit': 45000000000,
                'market_cap': 700000000000
            },
            'historical_data': [
                {'year': 2021, 'revenue': 130000000000, 'net_income': 30000000000, 'total_assets': 4200000000000},
                {'year': 2022, 'revenue': 140000000000, 'net_income': 32000000000, 'total_assets': 4350000000000},
                {'year': 2023, 'revenue': 150000000000, 'net_income': 35000000000, 'total_assets': 4500000000000}
            ]
        }
        
        # 执行增强分析
        enhanced_analysis = await enhancer.enhance_financial_analysis(
            company_data, AnalysisDepth.EXPERT
        )
        
        print(f"✅ 分析深度增强完成")
        
        # 检查分析结果
        if 'advanced_analysis' in enhanced_analysis:
            advanced = enhanced_analysis['advanced_analysis']
            
            if 'industry_comparison' in advanced:
                industry_result = advanced['industry_comparison']
                print(f"   行业对比分析: 评分{industry_result.score:.1f}")
            
            if 'zscore_analysis' in advanced:
                zscore_result = advanced['zscore_analysis']
                print(f"   Z-Score分析: {zscore_result.score:.2f}")
            
            if 'esg_analysis' in advanced:
                esg_result = advanced['esg_analysis']
                print(f"   ESG分析: 评分{esg_result.score:.1f}")
            
            if 'trend_analysis' in advanced:
                trend_result = advanced['trend_analysis']
                print(f"   趋势分析: 评分{trend_result.score:.1f}")
        
        # 检查分析摘要
        if 'analysis_summary' in enhanced_analysis:
            summary = enhanced_analysis['analysis_summary']
            print(f"✅ 总体评分: {summary.get('overall_score', 0):.1f}")
            print(f"✅ 质量等级: {summary.get('quality_level', 'N/A')}")
            print(f"✅ 平均置信度: {summary.get('average_confidence', 0):.2f}")
            print(f"✅ 成功分析数: {summary.get('successful_analyses', 0)}/{summary.get('total_analyses', 0)}")
        
        # 获取增强报告
        enhancement_report = enhancer.get_enhancement_report()
        print(f"✅ 分析成功率: {enhancement_report['success_rate']:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析深度增强测试失败: {e}")
        return False


async def test_user_experience_optimization():
    """测试用户体验优化"""
    print("👤 测试用户体验优化...")
    
    try:
        # 初始化用户体验优化器
        ux_optimizer = UserExperienceOptimizer()
        
        # 测试带进度跟踪的任务执行
        async def sample_task():
            await asyncio.sleep(1)
            return "任务完成"
        
        result = await ux_optimizer.execute_with_progress("示例任务", sample_task)
        print(f"✅ 进度跟踪任务: {result}")
        
        # 测试用户输入验证
        valid_input = {
            'symbol': '000001',
            'amount': 1000.0,
            'enabled': True
        }
        
        is_valid, errors = ux_optimizer.validate_user_input(valid_input, ['symbol', 'amount'])
        print(f"✅ 输入验证: {'通过' if is_valid else '失败'}")
        if errors:
            print(f"   错误: {errors}")
        
        # 测试错误处理
        try:
            raise ValueError("测试错误")
        except Exception as e:
            response = ux_optimizer.create_user_friendly_response(e, success=False)
            print(f"✅ 错误处理: {response['error']['user_message']}")
        
        # 测试用户反馈收集
        feedback_id = ux_optimizer.feedback_collector.collect_feedback(
            'user123', 'suggestion', '建议增加更多图表', 4
        )
        print(f"✅ 反馈收集: {feedback_id}")
        
        # 获取用户体验指标
        ux_metrics = ux_optimizer.get_user_experience_metrics()
        print(f"✅ 操作成功率: {ux_metrics['success_rate']:.1f}%")
        print(f"✅ 平均响应时间: {ux_metrics['average_response_time']:.3f}s")
        print(f"✅ 用户满意度: {ux_metrics['user_satisfaction_score']:.1f}/100")
        
        return True
        
    except Exception as e:
        print(f"❌ 用户体验优化测试失败: {e}")
        return False


async def test_system_stability():
    """测试系统稳定性"""
    print("🛡️ 测试系统稳定性...")
    
    try:
        # 初始化系统稳定性增强器
        stability_enhancer = SystemStabilityEnhancer({
            'circuit_breaker_threshold': 3,
            'max_retries': 2,
            'monitor_interval': 5
        })
        
        # 启动稳定性监控
        await stability_enhancer.start_stability_monitoring()
        print("✅ 稳定性监控已启动")
        
        # 等待一次健康检查
        await asyncio.sleep(6)
        
        # 测试稳定性保护执行
        def reliable_function():
            return "执行成功"
        
        result = await stability_enhancer.execute_with_stability(reliable_function)
        print(f"✅ 稳定性保护执行: {result}")
        
        # 测试失败场景
        failure_count = 0
        def unreliable_function():
            nonlocal failure_count
            failure_count += 1
            if failure_count <= 2:
                raise Exception("模拟失败")
            return "重试成功"
        
        try:
            result = await stability_enhancer.execute_with_stability(unreliable_function)
            print(f"✅ 重试机制: {result}")
        except Exception as e:
            print(f"⚠️ 重试失败: {e}")
        
        # 获取系统健康状态
        health = stability_enhancer.system_monitor.get_system_health()
        print(f"✅ 系统健康状态: {health['overall_status']}")
        
        # 检查各组件状态
        for component, status in health['components'].items():
            print(f"   {component}: {status['status']} - {status['message']}")
        
        # 执行自愈操作
        healing_result = await stability_enhancer.perform_self_healing()
        print(f"✅ 自愈操作: {'成功' if healing_result['success'] else '失败'}")
        if healing_result.get('actions_taken'):
            print(f"   执行的操作: {', '.join(healing_result['actions_taken'])}")
        
        # 获取稳定性报告
        stability_report = stability_enhancer.get_stability_report()
        print(f"✅ 系统运行时间: {stability_report['uptime_hours']:.1f}小时")
        print(f"✅ 请求成功率: {stability_report['success_rate']:.1f}%")
        print(f"✅ 熔断器状态: {stability_report['circuit_breaker_state']}")
        
        # 停止监控
        await stability_enhancer.stop_stability_monitoring()
        print("✅ 稳定性监控已停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 系统稳定性测试失败: {e}")
        return False


async def test_integrated_optimization():
    """测试集成优化效果"""
    print("🔄 测试集成优化效果...")
    
    try:
        # 模拟完整的分析流程
        from src.agents.data_collector_agent import DataCollectorAgent
        from src.agents.financial_analyst_agent import FinancialAnalystAgent
        from src.agents.report_generator_agent import ReportGeneratorAgent
        
        # 初始化优化组件
        performance_optimizer = PerformanceOptimizer()
        data_quality_enhancer = DataQualityEnhancer()
        ux_optimizer = UserExperienceOptimizer()
        
        # 配置Agent
        config = {
            'analysis_depth': 'comprehensive',
            'include_charts': True,
            'data_sources': ['mock']
        }
        
        # 创建Agent
        data_collector = DataCollectorAgent('DataCollector', config)
        financial_analyst = FinancialAnalystAgent('FinancialAnalyst', config)
        report_generator = ReportGeneratorAgent('ReportGenerator', config)
        
        # 执行优化后的完整流程
        company_symbol = '000001'
        
        # 1. 数据收集（带性能优化）
        print("  📊 优化的数据收集...")
        data_result = await ux_optimizer.execute_with_progress(
            "数据收集",
            data_collector.execute_task,
            'collect_company_data',
            company_symbol=company_symbol
        )
        
        if data_result.get('success'):
            company_data = data_result['result']
            
            # 2. 数据质量增强
            print("  🔍 数据质量增强...")
            enhanced_data, quality_metrics = await data_quality_enhancer.enhance_data_quality(company_data)
            print(f"     数据质量评分: {quality_metrics.overall_score:.1f}/100")
            
            # 3. 增强财务分析
            print("  📈 增强财务分析...")
            analysis_result = await ux_optimizer.execute_with_progress(
                "财务分析",
                financial_analyst.execute_task,
                'analyze_company',
                symbol=company_symbol,
                company_data=enhanced_data
            )
            
            if analysis_result.get('success'):
                analysis_data = analysis_result['result']
                
                # 4. 优化报告生成
                print("  📄 优化报告生成...")
                report_result = await ux_optimizer.execute_with_progress(
                    "报告生成",
                    report_generator.execute_task,
                    'generate_company_report',
                    symbol=company_symbol,
                    company_data=enhanced_data,
                    analysis_result=analysis_data
                )
                
                if report_result.get('success'):
                    report_data = report_result['result']
                    print(f"     报告生成完成: {report_data.get('title', 'N/A')}")
                    
                    # 检查优化效果
                    metadata = report_data.get('metadata', {})
                    if metadata.get('llm_enhanced'):
                        print("     ✨ LLM增强: 已启用")
                    
                    quality_score = report_data.get('quality_metrics', {}).get('overall_professional_score', 0)
                    print(f"     质量评分: {quality_score}/100")
        
        # 获取综合优化指标
        ux_metrics = ux_optimizer.get_user_experience_metrics()
        performance_report = performance_optimizer.get_performance_report()
        
        print(f"✅ 综合优化效果:")
        print(f"   用户体验评分: {ux_metrics['user_satisfaction_score']:.1f}/100")
        print(f"   系统成功率: {ux_metrics['success_rate']:.1f}%")
        print(f"   平均响应时间: {ux_metrics['average_response_time']:.2f}s")
        print(f"   缓存命中率: {performance_report['cache_stats']['hit_rate']:.1f}%")
        
        await performance_optimizer.cleanup()
        
        return True
        
    except Exception as e:
        print(f"❌ 集成优化测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始测试优化后的AFAC系统")
    print("=" * 60)
    
    # 设置日志
    logging.basicConfig(level=logging.WARNING)
    
    test_results = {}
    
    try:
        # 执行各项优化测试
        test_results['performance_optimization'] = await test_performance_optimization()
        test_results['data_quality_enhancement'] = await test_data_quality_enhancement()
        test_results['analysis_enhancement'] = await test_analysis_enhancement()
        test_results['user_experience_optimization'] = await test_user_experience_optimization()
        test_results['system_stability'] = await test_system_stability()
        test_results['integrated_optimization'] = await test_integrated_optimization()
        
        # 汇总测试结果
        print("\n" + "=" * 60)
        print("📊 系统优化测试结果:")
        
        passed_tests = sum(1 for result in test_results.values() if result)
        total_tests = len(test_results)
        
        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"  {test_name}: {status}")
        
        print(f"\n🎯 总体结果: {passed_tests}/{total_tests} 测试通过")
        
        if passed_tests == total_tests:
            print("🎉 所有优化测试通过！AFAC系统优化成功！")
            print("\n🏆 系统优化成果:")
            print("  ⚡ 性能优化: 模型量化、智能缓存、并发优化")
            print("  📊 数据质量: 验证清洗、质量评分、一致性检查")
            print("  🔍 分析深度: Z-Score、行业对比、ESG、趋势分析")
            print("  👤 用户体验: 进度跟踪、错误处理、友好反馈")
            print("  🛡️ 系统稳定: 熔断器、重试机制、健康监控")
            print("  🔗 集成优化: 端到端优化流程")
        else:
            print("⚠️ 部分优化测试失败，但核心功能正常")
        
        return passed_tests >= total_tests * 0.8
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
