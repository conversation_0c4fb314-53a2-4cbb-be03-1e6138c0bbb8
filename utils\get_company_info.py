#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取公司基本信息
支持A股、港股、美股
"""

import akshare as ak
import pandas as pd
import yfinance as yf
from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


def get_company_info(stock_code: str, market: str = "A") -> Dict[str, Any]:
    """
    获取公司基本信息
    
    Args:
        stock_code: 股票代码
        market: 市场类型 ("A", "HK", "US")
    
    Returns:
        公司基本信息字典
    """
    try:
        if market.upper() == "A":
            return _get_a_stock_info(stock_code)
        elif market.upper() == "HK":
            return _get_hk_stock_info(stock_code)
        elif market.upper() == "US":
            return _get_us_stock_info(stock_code)
        else:
            raise ValueError(f"不支持的市场类型: {market}")
            
    except Exception as e:
        logger.error(f"获取公司信息失败 {stock_code}: {e}")
        return _get_default_company_info(stock_code, market)


def _get_a_stock_info(stock_code: str) -> Dict[str, Any]:
    """获取A股公司信息"""
    try:
        # 获取股票基本信息
        stock_info = ak.stock_individual_info_em(symbol=stock_code)
        
        # 获取公司概况
        company_profile = ak.stock_profile_em(symbol=stock_code)
        
        # 整理信息
        info = {
            "stock_code": stock_code,
            "market": "A股",
            "company_name": _safe_get(stock_info, "公司名称", f"公司{stock_code}"),
            "industry": _safe_get(stock_info, "所属行业", "未知行业"),
            "main_business": _safe_get(company_profile, "主营业务", "主营业务信息暂无"),
            "listing_date": _safe_get(stock_info, "上市时间", ""),
            "total_share_capital": _safe_get(stock_info, "总股本", 0),
            "circulating_shares": _safe_get(stock_info, "流通股", 0),
            "market_cap": _safe_get(stock_info, "总市值", 0),
            "pe_ratio": _safe_get(stock_info, "市盈率", 0),
            "pb_ratio": _safe_get(stock_info, "市净率", 0),
            "dividend_yield": _safe_get(stock_info, "股息率", 0),
            "company_profile": _safe_get(company_profile, "公司简介", ""),
            "data_source": "akshare"
        }
        
        logger.info(f"成功获取A股公司信息: {stock_code}")
        return info
        
    except Exception as e:
        logger.warning(f"获取A股公司信息失败 {stock_code}: {e}")
        return _get_mock_a_stock_info(stock_code)


def _get_hk_stock_info(stock_code: str) -> Dict[str, Any]:
    """获取港股公司信息"""
    try:
        # 港股代码格式处理
        if not stock_code.endswith(".HK"):
            hk_code = f"{stock_code}.HK"
        else:
            hk_code = stock_code
        
        # 使用yfinance获取港股信息
        ticker = yf.Ticker(hk_code)
        info_data = ticker.info
        
        info = {
            "stock_code": stock_code,
            "market": "港股",
            "company_name": info_data.get("longName", f"公司{stock_code}"),
            "industry": info_data.get("industry", "未知行业"),
            "sector": info_data.get("sector", "未知板块"),
            "main_business": info_data.get("longBusinessSummary", "主营业务信息暂无"),
            "market_cap": info_data.get("marketCap", 0),
            "pe_ratio": info_data.get("trailingPE", 0),
            "pb_ratio": info_data.get("priceToBook", 0),
            "dividend_yield": info_data.get("dividendYield", 0),
            "employees": info_data.get("fullTimeEmployees", 0),
            "website": info_data.get("website", ""),
            "data_source": "yfinance"
        }
        
        logger.info(f"成功获取港股公司信息: {stock_code}")
        return info
        
    except Exception as e:
        logger.warning(f"获取港股公司信息失败 {stock_code}: {e}")
        return _get_mock_hk_stock_info(stock_code)


def _get_us_stock_info(stock_code: str) -> Dict[str, Any]:
    """获取美股公司信息"""
    try:
        # 使用yfinance获取美股信息
        ticker = yf.Ticker(stock_code)
        info_data = ticker.info
        
        info = {
            "stock_code": stock_code,
            "market": "美股",
            "company_name": info_data.get("longName", f"公司{stock_code}"),
            "industry": info_data.get("industry", "未知行业"),
            "sector": info_data.get("sector", "未知板块"),
            "main_business": info_data.get("longBusinessSummary", "主营业务信息暂无"),
            "market_cap": info_data.get("marketCap", 0),
            "pe_ratio": info_data.get("trailingPE", 0),
            "pb_ratio": info_data.get("priceToBook", 0),
            "dividend_yield": info_data.get("dividendYield", 0),
            "employees": info_data.get("fullTimeEmployees", 0),
            "website": info_data.get("website", ""),
            "country": info_data.get("country", ""),
            "exchange": info_data.get("exchange", ""),
            "data_source": "yfinance"
        }
        
        logger.info(f"成功获取美股公司信息: {stock_code}")
        return info
        
    except Exception as e:
        logger.warning(f"获取美股公司信息失败 {stock_code}: {e}")
        return _get_mock_us_stock_info(stock_code)


def _safe_get(data, key, default=None):
    """安全获取数据"""
    if isinstance(data, pd.DataFrame):
        if key in data.columns and len(data) > 0:
            return data[key].iloc[0]
    elif isinstance(data, dict):
        return data.get(key, default)
    return default


def _get_default_company_info(stock_code: str, market: str) -> Dict[str, Any]:
    """获取默认公司信息"""
    company_names = {
        "000001": "平安银行",
        "000002": "万科A", 
        "600036": "招商银行",
        "000858": "五粮液",
        "600519": "贵州茅台"
    }
    
    return {
        "stock_code": stock_code,
        "market": market,
        "company_name": company_names.get(stock_code, f"公司{stock_code}"),
        "industry": "相关行业",
        "main_business": "主营业务信息获取中...",
        "data_source": "default"
    }


def _get_mock_a_stock_info(stock_code: str) -> Dict[str, Any]:
    """获取模拟A股信息"""
    mock_data = {
        "000001": {
            "company_name": "平安银行",
            "industry": "银行业",
            "main_business": "商业银行业务，包括公司银行、零售银行、资金业务等",
            "market_cap": 3500.0,
            "pe_ratio": 5.2,
            "pb_ratio": 0.8
        },
        "000002": {
            "company_name": "万科A",
            "industry": "房地产",
            "main_business": "房地产开发与销售，物业服务，商业地产运营",
            "market_cap": 2800.0,
            "pe_ratio": 8.5,
            "pb_ratio": 1.2
        },
        "600036": {
            "company_name": "招商银行",
            "industry": "银行业", 
            "main_business": "商业银行业务，零售银行，公司银行，投资银行",
            "market_cap": 12000.0,
            "pe_ratio": 6.8,
            "pb_ratio": 1.1
        }
    }
    
    base_info = {
        "stock_code": stock_code,
        "market": "A股",
        "listing_date": "2000-01-01",
        "total_share_capital": 1000000000,
        "circulating_shares": *********,
        "dividend_yield": 0.03,
        "company_profile": "公司简介信息获取中...",
        "data_source": "mock"
    }
    
    if stock_code in mock_data:
        base_info.update(mock_data[stock_code])
    else:
        base_info.update({
            "company_name": f"公司{stock_code}",
            "industry": "相关行业",
            "main_business": "主营业务信息获取中...",
            "market_cap": 1000.0,
            "pe_ratio": 15.0,
            "pb_ratio": 2.0
        })
    
    return base_info


def _get_mock_hk_stock_info(stock_code: str) -> Dict[str, Any]:
    """获取模拟港股信息"""
    return {
        "stock_code": stock_code,
        "market": "港股",
        "company_name": f"港股公司{stock_code}",
        "industry": "相关行业",
        "main_business": "主营业务信息获取中...",
        "market_cap": 5000000000,
        "pe_ratio": 12.0,
        "pb_ratio": 1.5,
        "dividend_yield": 0.04,
        "data_source": "mock"
    }


def _get_mock_us_stock_info(stock_code: str) -> Dict[str, Any]:
    """获取模拟美股信息"""
    return {
        "stock_code": stock_code,
        "market": "美股",
        "company_name": f"美股公司{stock_code}",
        "industry": "相关行业",
        "main_business": "主营业务信息获取中...",
        "market_cap": ***********,
        "pe_ratio": 20.0,
        "pb_ratio": 3.0,
        "dividend_yield": 0.02,
        "data_source": "mock"
    }
