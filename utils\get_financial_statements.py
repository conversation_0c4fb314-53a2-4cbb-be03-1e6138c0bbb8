#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取财务报表数据
包括资产负债表、利润表、现金流量表
"""

import akshare as ak
import pandas as pd
import yfinance as yf
from typing import Dict, Any, Optional
import logging
from datetime import datetime, timedelta
import numpy as np

logger = logging.getLogger(__name__)


def get_financial_statements(stock_code: str, market: str = "A", periods: int = 8) -> Dict[str, Any]:
    """
    获取财务报表数据
    
    Args:
        stock_code: 股票代码
        market: 市场类型 ("A", "HK", "US")
        periods: 获取期数
    
    Returns:
        财务报表数据字典
    """
    try:
        if market.upper() == "A":
            return _get_a_stock_financials(stock_code, periods)
        elif market.upper() == "HK":
            return _get_hk_stock_financials(stock_code, periods)
        elif market.upper() == "US":
            return _get_us_stock_financials(stock_code, periods)
        else:
            raise ValueError(f"不支持的市场类型: {market}")
            
    except Exception as e:
        logger.error(f"获取财务报表失败 {stock_code}: {e}")
        return _get_mock_financial_statements(stock_code, market, periods)


def _get_a_stock_financials(stock_code: str, periods: int) -> Dict[str, Any]:
    """获取A股财务报表"""
    try:
        financials = {}
        
        # 获取资产负债表
        try:
            balance_sheet = ak.stock_balance_sheet_by_report_em(symbol=stock_code)
            if not balance_sheet.empty:
                financials['balance_sheet'] = balance_sheet.head(periods)
                logger.info(f"获取资产负债表成功: {len(balance_sheet)} 期")
        except Exception as e:
            logger.warning(f"获取资产负债表失败: {e}")
            financials['balance_sheet'] = _get_mock_balance_sheet(periods)
        
        # 获取利润表
        try:
            income_statement = ak.stock_profit_sheet_by_report_em(symbol=stock_code)
            if not income_statement.empty:
                financials['income_statement'] = income_statement.head(periods)
                logger.info(f"获取利润表成功: {len(income_statement)} 期")
        except Exception as e:
            logger.warning(f"获取利润表失败: {e}")
            financials['income_statement'] = _get_mock_income_statement(periods)
        
        # 获取现金流量表
        try:
            cash_flow = ak.stock_cash_flow_sheet_by_report_em(symbol=stock_code)
            if not cash_flow.empty:
                financials['cash_flow'] = cash_flow.head(periods)
                logger.info(f"获取现金流量表成功: {len(cash_flow)} 期")
        except Exception as e:
            logger.warning(f"获取现金流量表失败: {e}")
            financials['cash_flow'] = _get_mock_cash_flow(periods)
        
        # 计算财务比率
        financials['financial_ratios'] = _calculate_financial_ratios(financials)
        
        financials['data_source'] = 'akshare'
        financials['last_updated'] = datetime.now().isoformat()
        
        logger.info(f"成功获取A股财务报表: {stock_code}")
        return financials
        
    except Exception as e:
        logger.error(f"获取A股财务报表失败 {stock_code}: {e}")
        return _get_mock_financial_statements(stock_code, "A", periods)


def _get_hk_stock_financials(stock_code: str, periods: int) -> Dict[str, Any]:
    """获取港股财务报表"""
    try:
        # 港股代码格式处理
        if not stock_code.endswith(".HK"):
            hk_code = f"{stock_code}.HK"
        else:
            hk_code = stock_code
        
        ticker = yf.Ticker(hk_code)
        
        financials = {}
        
        # 获取财务报表
        try:
            balance_sheet = ticker.balance_sheet
            income_statement = ticker.financials
            cash_flow = ticker.cashflow
            
            if not balance_sheet.empty:
                financials['balance_sheet'] = balance_sheet.T.head(periods)
            if not income_statement.empty:
                financials['income_statement'] = income_statement.T.head(periods)
            if not cash_flow.empty:
                financials['cash_flow'] = cash_flow.T.head(periods)
                
        except Exception as e:
            logger.warning(f"获取港股财务报表失败: {e}")
        
        # 如果没有获取到数据，使用模拟数据
        if not financials:
            return _get_mock_financial_statements(stock_code, "HK", periods)
        
        # 计算财务比率
        financials['financial_ratios'] = _calculate_financial_ratios(financials)
        
        financials['data_source'] = 'yfinance'
        financials['last_updated'] = datetime.now().isoformat()
        
        logger.info(f"成功获取港股财务报表: {stock_code}")
        return financials
        
    except Exception as e:
        logger.error(f"获取港股财务报表失败 {stock_code}: {e}")
        return _get_mock_financial_statements(stock_code, "HK", periods)


def _get_us_stock_financials(stock_code: str, periods: int) -> Dict[str, Any]:
    """获取美股财务报表"""
    try:
        ticker = yf.Ticker(stock_code)
        
        financials = {}
        
        # 获取财务报表
        try:
            balance_sheet = ticker.balance_sheet
            income_statement = ticker.financials
            cash_flow = ticker.cashflow
            
            if not balance_sheet.empty:
                financials['balance_sheet'] = balance_sheet.T.head(periods)
            if not income_statement.empty:
                financials['income_statement'] = income_statement.T.head(periods)
            if not cash_flow.empty:
                financials['cash_flow'] = cash_flow.T.head(periods)
                
        except Exception as e:
            logger.warning(f"获取美股财务报表失败: {e}")
        
        # 如果没有获取到数据，使用模拟数据
        if not financials:
            return _get_mock_financial_statements(stock_code, "US", periods)
        
        # 计算财务比率
        financials['financial_ratios'] = _calculate_financial_ratios(financials)
        
        financials['data_source'] = 'yfinance'
        financials['last_updated'] = datetime.now().isoformat()
        
        logger.info(f"成功获取美股财务报表: {stock_code}")
        return financials
        
    except Exception as e:
        logger.error(f"获取美股财务报表失败 {stock_code}: {e}")
        return _get_mock_financial_statements(stock_code, "US", periods)


def _calculate_financial_ratios(financials: Dict[str, Any]) -> Dict[str, float]:
    """计算财务比率"""
    try:
        ratios = {}
        
        balance_sheet = financials.get('balance_sheet', pd.DataFrame())
        income_statement = financials.get('income_statement', pd.DataFrame())
        
        if not balance_sheet.empty and not income_statement.empty:
            # 获取最新数据
            latest_bs = balance_sheet.iloc[0] if len(balance_sheet) > 0 else {}
            latest_is = income_statement.iloc[0] if len(income_statement) > 0 else {}
            
            # 基础数据提取（处理不同的列名）
            revenue = _get_value(latest_is, ['营业收入', 'Total Revenue', '总收入'])
            net_income = _get_value(latest_is, ['净利润', 'Net Income', '净收入'])
            total_assets = _get_value(latest_bs, ['资产总计', 'Total Assets', '总资产'])
            shareholders_equity = _get_value(latest_bs, ['股东权益合计', 'Total Stockholder Equity', '股东权益'])
            current_assets = _get_value(latest_bs, ['流动资产合计', 'Total Current Assets', '流动资产'])
            current_liabilities = _get_value(latest_bs, ['流动负债合计', 'Total Current Liabilities', '流动负债'])
            total_liabilities = _get_value(latest_bs, ['负债合计', 'Total Liab', '总负债'])
            
            # 计算财务比率
            if revenue and revenue != 0:
                ratios['net_margin'] = (net_income / revenue) * 100 if net_income else 0
            
            if total_assets and total_assets != 0:
                ratios['roa'] = (net_income / total_assets) * 100 if net_income else 0
                ratios['asset_turnover'] = revenue / total_assets if revenue else 0
            
            if shareholders_equity and shareholders_equity != 0:
                ratios['roe'] = (net_income / shareholders_equity) * 100 if net_income else 0
            
            if current_liabilities and current_liabilities != 0:
                ratios['current_ratio'] = current_assets / current_liabilities if current_assets else 0
            
            if total_assets and total_assets != 0:
                ratios['debt_ratio'] = (total_liabilities / total_assets) * 100 if total_liabilities else 0
            
            if shareholders_equity and shareholders_equity != 0:
                ratios['debt_to_equity'] = (total_liabilities / shareholders_equity) * 100 if total_liabilities else 0
        
        return ratios
        
    except Exception as e:
        logger.error(f"计算财务比率失败: {e}")
        return {}


def _get_value(data, keys):
    """从数据中获取值，支持多个可能的键名"""
    for key in keys:
        if key in data and pd.notna(data[key]):
            return float(data[key])
    return 0


def _get_mock_financial_statements(stock_code: str, market: str, periods: int) -> Dict[str, Any]:
    """获取模拟财务报表数据"""
    return {
        'balance_sheet': _get_mock_balance_sheet(periods),
        'income_statement': _get_mock_income_statement(periods),
        'cash_flow': _get_mock_cash_flow(periods),
        'financial_ratios': _get_mock_financial_ratios(),
        'data_source': 'mock',
        'last_updated': datetime.now().isoformat()
    }


def _get_mock_balance_sheet(periods: int) -> pd.DataFrame:
    """生成模拟资产负债表"""
    dates = pd.date_range(end=datetime.now(), periods=periods, freq='Q')
    
    data = []
    for i, date in enumerate(dates):
        # 模拟增长趋势
        growth_factor = 1 + (i * 0.05)
        
        row = {
            '报告期': date.strftime('%Y-%m-%d'),
            '资产总计': 1000000 * growth_factor,
            '流动资产合计': 400000 * growth_factor,
            '货币资金': 150000 * growth_factor,
            '应收账款': 100000 * growth_factor,
            '存货': 80000 * growth_factor,
            '固定资产': 300000 * growth_factor,
            '无形资产': 50000 * growth_factor,
            '负债合计': 600000 * growth_factor,
            '流动负债合计': 300000 * growth_factor,
            '应付账款': 120000 * growth_factor,
            '短期借款': 80000 * growth_factor,
            '长期借款': 200000 * growth_factor,
            '股东权益合计': 400000 * growth_factor,
            '股本': 100000,
            '资本公积': 150000 * growth_factor,
            '留存收益': 150000 * growth_factor
        }
        data.append(row)
    
    return pd.DataFrame(data)


def _get_mock_income_statement(periods: int) -> pd.DataFrame:
    """生成模拟利润表"""
    dates = pd.date_range(end=datetime.now(), periods=periods, freq='Q')
    
    data = []
    for i, date in enumerate(dates):
        # 模拟季节性和增长趋势
        seasonal_factor = 1 + 0.1 * np.sin(i * np.pi / 2)
        growth_factor = 1 + (i * 0.03)
        
        revenue = 200000 * seasonal_factor * growth_factor
        
        row = {
            '报告期': date.strftime('%Y-%m-%d'),
            '营业收入': revenue,
            '营业成本': revenue * 0.7,
            '营业利润': revenue * 0.15,
            '利润总额': revenue * 0.14,
            '净利润': revenue * 0.12,
            '基本每股收益': (revenue * 0.12) / 100000,
            '稀释每股收益': (revenue * 0.12) / 100000,
            '销售费用': revenue * 0.08,
            '管理费用': revenue * 0.05,
            '财务费用': revenue * 0.02,
            '研发费用': revenue * 0.03
        }
        data.append(row)
    
    return pd.DataFrame(data)


def _get_mock_cash_flow(periods: int) -> pd.DataFrame:
    """生成模拟现金流量表"""
    dates = pd.date_range(end=datetime.now(), periods=periods, freq='Q')
    
    data = []
    for i, date in enumerate(dates):
        growth_factor = 1 + (i * 0.04)
        
        operating_cf = 150000 * growth_factor
        
        row = {
            '报告期': date.strftime('%Y-%m-%d'),
            '经营活动现金流量净额': operating_cf,
            '投资活动现金流量净额': -50000 * growth_factor,
            '筹资活动现金流量净额': -30000 * growth_factor,
            '现金及现金等价物净增加额': operating_cf - 80000 * growth_factor,
            '期末现金及现金等价物余额': 150000 * growth_factor
        }
        data.append(row)
    
    return pd.DataFrame(data)


def _get_mock_financial_ratios() -> Dict[str, float]:
    """生成模拟财务比率"""
    return {
        'roe': 12.5,          # 净资产收益率
        'roa': 8.2,           # 总资产收益率
        'net_margin': 12.0,   # 净利润率
        'current_ratio': 1.33, # 流动比率
        'debt_ratio': 60.0,   # 资产负债率
        'debt_to_equity': 150.0, # 产权比率
        'asset_turnover': 0.68   # 资产周转率
    }
