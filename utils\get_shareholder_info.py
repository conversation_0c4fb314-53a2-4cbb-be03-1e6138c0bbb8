#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取股东信息
包括十大股东、机构持股等
"""

import akshare as ak
import pandas as pd
import yfinance as yf
from typing import Dict, Any, Optional, List
import logging
from datetime import datetime
import numpy as np

logger = logging.getLogger(__name__)


def get_shareholder_info(stock_code: str, market: str = "A") -> Dict[str, Any]:
    """
    获取股东信息
    
    Args:
        stock_code: 股票代码
        market: 市场类型 ("A", "HK", "US")
    
    Returns:
        股东信息字典
    """
    try:
        if market.upper() == "A":
            return _get_a_stock_shareholders(stock_code)
        elif market.upper() == "HK":
            return _get_hk_stock_shareholders(stock_code)
        elif market.upper() == "US":
            return _get_us_stock_shareholders(stock_code)
        else:
            raise ValueError(f"不支持的市场类型: {market}")
            
    except Exception as e:
        logger.error(f"获取股东信息失败 {stock_code}: {e}")
        return _get_mock_shareholder_info(stock_code, market)


def _get_a_stock_shareholders(stock_code: str) -> Dict[str, Any]:
    """获取A股股东信息"""
    try:
        shareholder_info = {}
        
        # 获取十大股东
        try:
            top_shareholders = ak.stock_zh_a_gdhs(symbol=stock_code)
            if not top_shareholders.empty:
                shareholder_info['top_shareholders'] = top_shareholders
                logger.info(f"获取十大股东成功: {len(top_shareholders)} 个")
        except Exception as e:
            logger.warning(f"获取十大股东失败: {e}")
            shareholder_info['top_shareholders'] = _get_mock_top_shareholders()
        
        # 获取机构持股
        try:
            institutional_holdings = ak.stock_zh_a_jgcg(symbol=stock_code)
            if not institutional_holdings.empty:
                shareholder_info['institutional_holdings'] = institutional_holdings
                logger.info(f"获取机构持股成功: {len(institutional_holdings)} 个")
        except Exception as e:
            logger.warning(f"获取机构持股失败: {e}")
            shareholder_info['institutional_holdings'] = _get_mock_institutional_holdings()
        
        # 计算股权集中度
        shareholder_info['ownership_concentration'] = _calculate_ownership_concentration(
            shareholder_info.get('top_shareholders', pd.DataFrame())
        )
        
        # 分析股东结构
        shareholder_info['shareholder_analysis'] = _analyze_shareholder_structure(shareholder_info)
        
        shareholder_info['data_source'] = 'akshare'
        shareholder_info['last_updated'] = datetime.now().isoformat()
        
        logger.info(f"成功获取A股股东信息: {stock_code}")
        return shareholder_info
        
    except Exception as e:
        logger.error(f"获取A股股东信息失败 {stock_code}: {e}")
        return _get_mock_shareholder_info(stock_code, "A")


def _get_hk_stock_shareholders(stock_code: str) -> Dict[str, Any]:
    """获取港股股东信息"""
    try:
        # 港股股东信息获取相对困难，使用模拟数据
        logger.warning(f"港股股东信息获取功能开发中，使用模拟数据: {stock_code}")
        return _get_mock_shareholder_info(stock_code, "HK")
        
    except Exception as e:
        logger.error(f"获取港股股东信息失败 {stock_code}: {e}")
        return _get_mock_shareholder_info(stock_code, "HK")


def _get_us_stock_shareholders(stock_code: str) -> Dict[str, Any]:
    """获取美股股东信息"""
    try:
        # 美股股东信息获取相对困难，使用模拟数据
        logger.warning(f"美股股东信息获取功能开发中，使用模拟数据: {stock_code}")
        return _get_mock_shareholder_info(stock_code, "US")
        
    except Exception as e:
        logger.error(f"获取美股股东信息失败 {stock_code}: {e}")
        return _get_mock_shareholder_info(stock_code, "US")


def _calculate_ownership_concentration(shareholders_df: pd.DataFrame) -> Dict[str, Any]:
    """计算股权集中度"""
    try:
        concentration = {}
        
        if shareholders_df.empty:
            return concentration
        
        # 获取持股比例列
        holding_ratio_col = None
        for col in ['持股比例', '持股比例(%)', 'Holding Ratio', '持股数量']:
            if col in shareholders_df.columns:
                holding_ratio_col = col
                break
        
        if holding_ratio_col is None:
            logger.warning("未找到持股比例列")
            return concentration
        
        # 处理持股比例数据
        holdings = shareholders_df[holding_ratio_col].copy()
        
        # 如果是百分比字符串，转换为数值
        if holdings.dtype == 'object':
            holdings = holdings.str.replace('%', '').astype(float)
        
        # 计算各项集中度指标
        if len(holdings) > 0:
            concentration['largest_shareholder'] = float(holdings.iloc[0])
            concentration['top_3_ratio'] = float(holdings.head(3).sum())
            concentration['top_5_ratio'] = float(holdings.head(5).sum())
            concentration['top_10_ratio'] = float(holdings.head(10).sum())
            
            # 计算HHI指数（赫芬达尔-赫希曼指数）
            concentration['hhi_index'] = float((holdings ** 2).sum())
            
            # 股权集中度评级
            largest = concentration['largest_shareholder']
            if largest > 50:
                concentration['concentration_level'] = '高度集中'
            elif largest > 30:
                concentration['concentration_level'] = '中度集中'
            elif largest > 20:
                concentration['concentration_level'] = '相对集中'
            else:
                concentration['concentration_level'] = '相对分散'
        
        return concentration
        
    except Exception as e:
        logger.error(f"计算股权集中度失败: {e}")
        return {}


def _analyze_shareholder_structure(shareholder_info: Dict[str, Any]) -> Dict[str, Any]:
    """分析股东结构"""
    try:
        analysis = {}
        
        top_shareholders = shareholder_info.get('top_shareholders', pd.DataFrame())
        institutional_holdings = shareholder_info.get('institutional_holdings', pd.DataFrame())
        
        # 分析股东类型
        if not top_shareholders.empty:
            shareholder_types = _classify_shareholder_types(top_shareholders)
            analysis['shareholder_types'] = shareholder_types
        
        # 分析机构投资者
        if not institutional_holdings.empty:
            institutional_analysis = _analyze_institutional_investors(institutional_holdings)
            analysis['institutional_analysis'] = institutional_analysis
        
        # 股权稳定性分析
        concentration = shareholder_info.get('ownership_concentration', {})
        if concentration:
            stability_analysis = _analyze_ownership_stability(concentration)
            analysis['stability_analysis'] = stability_analysis
        
        return analysis
        
    except Exception as e:
        logger.error(f"分析股东结构失败: {e}")
        return {}


def _classify_shareholder_types(shareholders_df: pd.DataFrame) -> Dict[str, Any]:
    """分类股东类型"""
    try:
        types = {
            'state_owned': 0,      # 国有股东
            'institutional': 0,    # 机构投资者
            'individual': 0,       # 个人投资者
            'foreign': 0,          # 外资
            'other': 0            # 其他
        }
        
        # 获取股东名称列
        name_col = None
        for col in ['股东名称', 'Shareholder Name', '股东']:
            if col in shareholders_df.columns:
                name_col = col
                break
        
        if name_col is None:
            return types
        
        # 分类关键词
        state_keywords = ['国资', '财政', '社保', '中央', '省政府', '市政府']
        institutional_keywords = ['基金', '保险', '证券', '银行', '信托', '资管']
        foreign_keywords = ['QFII', '外资', 'Foreign', 'International']
        
        for _, row in shareholders_df.iterrows():
            name = str(row[name_col])
            
            if any(keyword in name for keyword in state_keywords):
                types['state_owned'] += 1
            elif any(keyword in name for keyword in institutional_keywords):
                types['institutional'] += 1
            elif any(keyword in name for keyword in foreign_keywords):
                types['foreign'] += 1
            elif '有限公司' in name or '股份' in name:
                types['institutional'] += 1
            else:
                types['individual'] += 1
        
        return types
        
    except Exception as e:
        logger.error(f"分类股东类型失败: {e}")
        return {}


def _analyze_institutional_investors(institutional_df: pd.DataFrame) -> Dict[str, Any]:
    """分析机构投资者"""
    try:
        analysis = {
            'total_institutions': len(institutional_df),
            'institution_types': {},
            'holding_changes': {}
        }
        
        # 分析机构类型
        if '机构类型' in institutional_df.columns:
            type_counts = institutional_df['机构类型'].value_counts().to_dict()
            analysis['institution_types'] = type_counts
        
        # 分析持股变化
        if '持股变化' in institutional_df.columns:
            changes = institutional_df['持股变化'].value_counts().to_dict()
            analysis['holding_changes'] = changes
        
        return analysis
        
    except Exception as e:
        logger.error(f"分析机构投资者失败: {e}")
        return {}


def _analyze_ownership_stability(concentration: Dict[str, Any]) -> Dict[str, str]:
    """分析股权稳定性"""
    try:
        stability = {}
        
        largest = concentration.get('largest_shareholder', 0)
        top_5 = concentration.get('top_5_ratio', 0)
        
        # 控制权稳定性
        if largest > 50:
            stability['control_stability'] = '控制权高度稳定'
        elif largest > 30:
            stability['control_stability'] = '控制权相对稳定'
        else:
            stability['control_stability'] = '控制权分散'
        
        # 股权流动性
        if top_5 > 70:
            stability['liquidity'] = '股权流动性较低'
        elif top_5 > 50:
            stability['liquidity'] = '股权流动性中等'
        else:
            stability['liquidity'] = '股权流动性较高'
        
        return stability
        
    except Exception as e:
        logger.error(f"分析股权稳定性失败: {e}")
        return {}


def _get_mock_shareholder_info(stock_code: str, market: str) -> Dict[str, Any]:
    """获取模拟股东信息"""
    return {
        'top_shareholders': _get_mock_top_shareholders(),
        'institutional_holdings': _get_mock_institutional_holdings(),
        'ownership_concentration': _get_mock_ownership_concentration(),
        'shareholder_analysis': _get_mock_shareholder_analysis(),
        'data_source': 'mock',
        'last_updated': datetime.now().isoformat()
    }


def _get_mock_top_shareholders() -> pd.DataFrame:
    """生成模拟十大股东数据"""
    shareholders = [
        {'股东名称': '第一大股东有限公司', '持股比例': 25.5, '持股数量': 255000000, '股东性质': '国有法人'},
        {'股东名称': '第二大股东投资公司', '持股比例': 15.2, '持股数量': 152000000, '股东性质': '境内法人'},
        {'股东名称': '社保基金组合', '持股比例': 8.7, '持股数量': 87000000, '股东性质': '机构投资者'},
        {'股东名称': '某保险公司', '持股比例': 6.3, '持股数量': 63000000, '股东性质': '机构投资者'},
        {'股东名称': '某基金管理公司', '持股比例': 4.8, '持股数量': 48000000, '股东性质': '机构投资者'},
        {'股东名称': '某证券公司', '持股比例': 3.9, '持股数量': 39000000, '股东性质': '机构投资者'},
        {'股东名称': '某信托公司', '持股比例': 3.2, '持股数量': 32000000, '股东性质': '机构投资者'},
        {'股东名称': '某私募基金', '持股比例': 2.8, '持股数量': 28000000, '股东性质': '机构投资者'},
        {'股东名称': '某QFII', '持股比例': 2.1, '持股数量': 21000000, '股东性质': '外资'},
        {'股东名称': '某个人投资者', '持股比例': 1.5, '持股数量': 15000000, '股东性质': '个人'}
    ]
    
    return pd.DataFrame(shareholders)


def _get_mock_institutional_holdings() -> pd.DataFrame:
    """生成模拟机构持股数据"""
    institutions = [
        {'机构名称': '某公募基金', '机构类型': '基金', '持股数量': 50000000, '持股比例': 5.0, '持股变化': '增持'},
        {'机构名称': '某保险公司', '机构类型': '保险', '持股数量': 40000000, '持股比例': 4.0, '持股变化': '新进'},
        {'机构名称': '某券商资管', '机构类型': '券商', '持股数量': 30000000, '持股比例': 3.0, '持股变化': '减持'},
        {'机构名称': '某银行理财', '机构类型': '银行', '持股数量': 25000000, '持股比例': 2.5, '持股变化': '不变'},
        {'机构名称': '某私募基金', '机构类型': '私募', '持股数量': 20000000, '持股比例': 2.0, '持股变化': '增持'}
    ]
    
    return pd.DataFrame(institutions)


def _get_mock_ownership_concentration() -> Dict[str, Any]:
    """生成模拟股权集中度数据"""
    return {
        'largest_shareholder': 25.5,
        'top_3_ratio': 49.4,
        'top_5_ratio': 60.5,
        'top_10_ratio': 74.0,
        'hhi_index': 1250.5,
        'concentration_level': '中度集中'
    }


def _get_mock_shareholder_analysis() -> Dict[str, Any]:
    """生成模拟股东分析数据"""
    return {
        'shareholder_types': {
            'state_owned': 1,
            'institutional': 7,
            'individual': 1,
            'foreign': 1,
            'other': 0
        },
        'institutional_analysis': {
            'total_institutions': 5,
            'institution_types': {
                '基金': 1,
                '保险': 1,
                '券商': 1,
                '银行': 1,
                '私募': 1
            },
            'holding_changes': {
                '增持': 2,
                '新进': 1,
                '减持': 1,
                '不变': 1
            }
        },
        'stability_analysis': {
            'control_stability': '控制权相对稳定',
            'liquidity': '股权流动性中等'
        }
    }
