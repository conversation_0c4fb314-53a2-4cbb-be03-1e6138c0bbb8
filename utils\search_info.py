#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
搜索行业信息和宏观经济数据
"""

import akshare as ak
import pandas as pd
from typing import Dict, Any, Optional, List
import logging
from datetime import datetime, timedelta
import requests
import json

logger = logging.getLogger(__name__)


def search_industry_info(industry_name: str) -> Dict[str, Any]:
    """
    搜索行业信息
    
    Args:
        industry_name: 行业名称
    
    Returns:
        行业信息字典
    """
    try:
        industry_info = {}
        
        # 获取行业基本信息
        industry_info['basic_info'] = _get_industry_basic_info(industry_name)
        
        # 获取行业公司列表
        industry_info['companies'] = _get_industry_companies(industry_name)
        
        # 获取行业指数数据
        industry_info['index_data'] = _get_industry_index_data(industry_name)
        
        # 获取行业新闻
        industry_info['news'] = _get_industry_news(industry_name)
        
        # 行业分析
        industry_info['analysis'] = _analyze_industry_data(industry_info)
        
        industry_info['data_source'] = 'multiple'
        industry_info['last_updated'] = datetime.now().isoformat()
        
        logger.info(f"成功获取行业信息: {industry_name}")
        return industry_info
        
    except Exception as e:
        logger.error(f"搜索行业信息失败 {industry_name}: {e}")
        return _get_mock_industry_info(industry_name)


def search_macro_data(indicators: List[str]) -> Dict[str, Any]:
    """
    搜索宏观经济数据
    
    Args:
        indicators: 宏观指标列表
    
    Returns:
        宏观数据字典
    """
    try:
        macro_data = {}
        
        for indicator in indicators:
            if indicator.upper() == 'GDP':
                macro_data['gdp'] = _get_gdp_data()
            elif indicator.upper() == 'CPI':
                macro_data['cpi'] = _get_cpi_data()
            elif indicator in ['利率', 'INTEREST_RATE']:
                macro_data['interest_rate'] = _get_interest_rate_data()
            elif indicator in ['汇率', 'EXCHANGE_RATE']:
                macro_data['exchange_rate'] = _get_exchange_rate_data()
            elif indicator.upper() == 'PMI':
                macro_data['pmi'] = _get_pmi_data()
            elif indicator in ['货币供应量', 'MONEY_SUPPLY']:
                macro_data['money_supply'] = _get_money_supply_data()
        
        # 宏观数据分析
        macro_data['analysis'] = _analyze_macro_data(macro_data)
        
        macro_data['data_source'] = 'akshare'
        macro_data['last_updated'] = datetime.now().isoformat()
        
        logger.info(f"成功获取宏观数据: {indicators}")
        return macro_data
        
    except Exception as e:
        logger.error(f"搜索宏观数据失败 {indicators}: {e}")
        return _get_mock_macro_data(indicators)


def _get_industry_basic_info(industry_name: str) -> Dict[str, Any]:
    """获取行业基本信息"""
    try:
        # 行业映射
        industry_mapping = {
            '银行业': '银行',
            '房地产': '房地产开发',
            '新能源汽车': '汽车制造',
            '医药': '医药制造',
            '科技': '软件开发'
        }
        
        mapped_industry = industry_mapping.get(industry_name, industry_name)
        
        # 尝试获取行业数据
        try:
            # 这里可以调用具体的行业数据接口
            # 由于akshare的行业接口可能变化，使用模拟数据
            basic_info = _get_mock_industry_basic_info(industry_name)
            return basic_info
        except Exception as e:
            logger.warning(f"获取行业基本信息失败: {e}")
            return _get_mock_industry_basic_info(industry_name)
            
    except Exception as e:
        logger.error(f"获取行业基本信息失败: {e}")
        return {}


def _get_industry_companies(industry_name: str) -> List[Dict[str, Any]]:
    """获取行业公司列表"""
    try:
        # 行业公司映射
        industry_companies = {
            '银行业': [
                {'code': '000001', 'name': '平安银行', 'market_cap': 3500},
                {'code': '600036', 'name': '招商银行', 'market_cap': 12000},
                {'code': '600000', 'name': '浦发银行', 'market_cap': 4500},
                {'code': '002142', 'name': '宁波银行', 'market_cap': 2800},
                {'code': '600015', 'name': '华夏银行', 'market_cap': 1800}
            ],
            '房地产': [
                {'code': '000002', 'name': '万科A', 'market_cap': 2800},
                {'code': '000858', 'name': '五粮液', 'market_cap': 8500},
                {'code': '600048', 'name': '保利发展', 'market_cap': 1500},
                {'code': '001979', 'name': '招商蛇口', 'market_cap': 1200},
                {'code': '600383', 'name': '金地集团', 'market_cap': 800}
            ],
            '新能源汽车': [
                {'code': '002594', 'name': '比亚迪', 'market_cap': 7500},
                {'code': '300750', 'name': '宁德时代', 'market_cap': 12000},
                {'code': '002460', 'name': '赣锋锂业', 'market_cap': 2200},
                {'code': '300014', 'name': '亿纬锂能', 'market_cap': 1800},
                {'code': '002812', 'name': '恩捷股份', 'market_cap': 1500}
            ]
        }
        
        return industry_companies.get(industry_name, [])
        
    except Exception as e:
        logger.error(f"获取行业公司列表失败: {e}")
        return []


def _get_industry_index_data(industry_name: str) -> Dict[str, Any]:
    """获取行业指数数据"""
    try:
        # 行业指数映射
        index_mapping = {
            '银行业': '399986',  # 中证银行指数
            '房地产': '399393', # 国证地产指数
            '新能源汽车': '399976' # 中证新能车指数
        }
        
        index_code = index_mapping.get(industry_name)
        
        if index_code:
            try:
                # 获取指数历史数据
                index_data = ak.index_zh_a_hist(symbol=index_code, period="daily", start_date="20230101")
                
                if not index_data.empty:
                    return {
                        'index_code': index_code,
                        'index_name': f'{industry_name}指数',
                        'historical_data': index_data.tail(252),  # 最近一年数据
                        'latest_price': float(index_data['收盘'].iloc[-1]),
                        'price_change': float(index_data['涨跌幅'].iloc[-1])
                    }
            except Exception as e:
                logger.warning(f"获取行业指数数据失败: {e}")
        
        # 返回模拟数据
        return _get_mock_industry_index_data(industry_name)
        
    except Exception as e:
        logger.error(f"获取行业指数数据失败: {e}")
        return {}


def _get_industry_news(industry_name: str) -> List[Dict[str, str]]:
    """获取行业新闻"""
    try:
        # 这里可以调用新闻API获取行业相关新闻
        # 由于API限制，使用模拟数据
        news = [
            {
                'title': f'{industry_name}行业发展前景看好',
                'content': f'{industry_name}行业在政策支持下迎来新的发展机遇...',
                'source': '财经新闻',
                'publish_date': datetime.now().strftime('%Y-%m-%d')
            },
            {
                'title': f'{industry_name}龙头企业业绩亮眼',
                'content': f'{industry_name}行业龙头企业发布最新财报，业绩超预期...',
                'source': '证券日报',
                'publish_date': (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            }
        ]
        
        return news
        
    except Exception as e:
        logger.error(f"获取行业新闻失败: {e}")
        return []


def _get_gdp_data() -> Dict[str, Any]:
    """获取GDP数据"""
    try:
        # 获取GDP数据
        gdp_data = ak.macro_china_gdp()
        
        if not gdp_data.empty:
            return {
                'historical_data': gdp_data.tail(20),
                'latest_value': float(gdp_data['国内生产总值-绝对值'].iloc[-1]),
                'growth_rate': float(gdp_data['国内生产总值-同比增长'].iloc[-1]),
                'data_source': 'akshare'
            }
        else:
            return _get_mock_gdp_data()
            
    except Exception as e:
        logger.warning(f"获取GDP数据失败: {e}")
        return _get_mock_gdp_data()


def _get_cpi_data() -> Dict[str, Any]:
    """获取CPI数据"""
    try:
        # 获取CPI数据
        cpi_data = ak.macro_china_cpi()
        
        if not cpi_data.empty:
            return {
                'historical_data': cpi_data.tail(24),
                'latest_value': float(cpi_data['全国-同比'].iloc[-1]),
                'month_on_month': float(cpi_data['全国-环比'].iloc[-1]),
                'data_source': 'akshare'
            }
        else:
            return _get_mock_cpi_data()
            
    except Exception as e:
        logger.warning(f"获取CPI数据失败: {e}")
        return _get_mock_cpi_data()


def _get_interest_rate_data() -> Dict[str, Any]:
    """获取利率数据"""
    try:
        # 获取利率数据
        rate_data = ak.macro_china_money_supply()
        
        # 由于利率数据接口可能变化，使用模拟数据
        return _get_mock_interest_rate_data()
        
    except Exception as e:
        logger.warning(f"获取利率数据失败: {e}")
        return _get_mock_interest_rate_data()


def _get_exchange_rate_data() -> Dict[str, Any]:
    """获取汇率数据"""
    try:
        # 获取人民币汇率数据
        usd_cny = ak.currency_hist(symbol="USDCNY")
        
        if not usd_cny.empty:
            return {
                'usd_cny': {
                    'latest_rate': float(usd_cny['收盘'].iloc[-1]),
                    'change': float(usd_cny['涨跌幅'].iloc[-1]),
                    'historical_data': usd_cny.tail(252)
                },
                'data_source': 'akshare'
            }
        else:
            return _get_mock_exchange_rate_data()
            
    except Exception as e:
        logger.warning(f"获取汇率数据失败: {e}")
        return _get_mock_exchange_rate_data()


def _get_pmi_data() -> Dict[str, Any]:
    """获取PMI数据"""
    try:
        # 获取PMI数据
        pmi_data = ak.macro_china_pmi()
        
        if not pmi_data.empty:
            return {
                'historical_data': pmi_data.tail(24),
                'latest_value': float(pmi_data['制造业PMI'].iloc[-1]),
                'data_source': 'akshare'
            }
        else:
            return _get_mock_pmi_data()
            
    except Exception as e:
        logger.warning(f"获取PMI数据失败: {e}")
        return _get_mock_pmi_data()


def _get_money_supply_data() -> Dict[str, Any]:
    """获取货币供应量数据"""
    try:
        # 获取货币供应量数据
        money_data = ak.macro_china_money_supply()
        
        if not money_data.empty:
            return {
                'historical_data': money_data.tail(24),
                'latest_m2': float(money_data['货币和准货币(M2)-同比增长'].iloc[-1]),
                'latest_m1': float(money_data['货币(M1)-同比增长'].iloc[-1]),
                'data_source': 'akshare'
            }
        else:
            return _get_mock_money_supply_data()
            
    except Exception as e:
        logger.warning(f"获取货币供应量数据失败: {e}")
        return _get_mock_money_supply_data()


def _analyze_industry_data(industry_info: Dict[str, Any]) -> Dict[str, str]:
    """分析行业数据"""
    analysis = {}
    
    # 分析行业发展阶段
    companies = industry_info.get('companies', [])
    if companies:
        avg_market_cap = sum(c.get('market_cap', 0) for c in companies) / len(companies)
        if avg_market_cap > 5000:
            analysis['development_stage'] = '成熟期'
        elif avg_market_cap > 2000:
            analysis['development_stage'] = '成长期'
        else:
            analysis['development_stage'] = '发展期'
    
    # 分析市场集中度
    if companies and len(companies) >= 3:
        top3_cap = sum(c.get('market_cap', 0) for c in companies[:3])
        total_cap = sum(c.get('market_cap', 0) for c in companies)
        if total_cap > 0:
            concentration = (top3_cap / total_cap) * 100
            if concentration > 70:
                analysis['market_concentration'] = '高度集中'
            elif concentration > 50:
                analysis['market_concentration'] = '中度集中'
            else:
                analysis['market_concentration'] = '相对分散'
    
    return analysis


def _analyze_macro_data(macro_data: Dict[str, Any]) -> Dict[str, str]:
    """分析宏观数据"""
    analysis = {}
    
    # 分析经济增长
    gdp = macro_data.get('gdp', {})
    if gdp:
        growth_rate = gdp.get('growth_rate', 0)
        if growth_rate > 6:
            analysis['economic_growth'] = '高速增长'
        elif growth_rate > 4:
            analysis['economic_growth'] = '中速增长'
        else:
            analysis['economic_growth'] = '低速增长'
    
    # 分析通胀水平
    cpi = macro_data.get('cpi', {})
    if cpi:
        cpi_value = cpi.get('latest_value', 0)
        if cpi_value > 3:
            analysis['inflation_level'] = '通胀压力较大'
        elif cpi_value > 1:
            analysis['inflation_level'] = '温和通胀'
        else:
            analysis['inflation_level'] = '通胀压力较小'
    
    return analysis


# 模拟数据生成函数
def _get_mock_industry_info(industry_name: str) -> Dict[str, Any]:
    """生成模拟行业信息"""
    return {
        'basic_info': _get_mock_industry_basic_info(industry_name),
        'companies': _get_industry_companies(industry_name),
        'index_data': _get_mock_industry_index_data(industry_name),
        'news': _get_industry_news(industry_name),
        'analysis': {'development_stage': '成长期', 'market_concentration': '中度集中'},
        'data_source': 'mock',
        'last_updated': datetime.now().isoformat()
    }


def _get_mock_industry_basic_info(industry_name: str) -> Dict[str, Any]:
    """生成模拟行业基本信息"""
    return {
        'industry_name': industry_name,
        'market_size': 1000000,  # 百万元
        'growth_rate': 8.5,      # 增长率%
        'companies_count': 150,   # 公司数量
        'employment': 500000,     # 就业人数
        'description': f'{industry_name}是国民经济的重要组成部分...'
    }


def _get_mock_industry_index_data(industry_name: str) -> Dict[str, Any]:
    """生成模拟行业指数数据"""
    return {
        'index_code': '999999',
        'index_name': f'{industry_name}指数',
        'latest_price': 3500.0,
        'price_change': 1.25,
        'historical_data': pd.DataFrame()
    }


def _get_mock_macro_data(indicators: List[str]) -> Dict[str, Any]:
    """生成模拟宏观数据"""
    mock_data = {
        'analysis': {'economic_growth': '中速增长', 'inflation_level': '温和通胀'},
        'data_source': 'mock',
        'last_updated': datetime.now().isoformat()
    }
    
    for indicator in indicators:
        if indicator.upper() == 'GDP':
            mock_data['gdp'] = _get_mock_gdp_data()
        elif indicator.upper() == 'CPI':
            mock_data['cpi'] = _get_mock_cpi_data()
        # 添加其他指标...
    
    return mock_data


def _get_mock_gdp_data() -> Dict[str, Any]:
    """生成模拟GDP数据"""
    return {
        'latest_value': 1143670,  # 亿元
        'growth_rate': 5.2,       # 同比增长%
        'data_source': 'mock'
    }


def _get_mock_cpi_data() -> Dict[str, Any]:
    """生成模拟CPI数据"""
    return {
        'latest_value': 2.1,      # 同比%
        'month_on_month': 0.3,    # 环比%
        'data_source': 'mock'
    }


def _get_mock_interest_rate_data() -> Dict[str, Any]:
    """生成模拟利率数据"""
    return {
        'benchmark_rate': 3.85,   # 基准利率%
        'deposit_rate': 1.5,      # 存款利率%
        'loan_rate': 4.35,        # 贷款利率%
        'data_source': 'mock'
    }


def _get_mock_exchange_rate_data() -> Dict[str, Any]:
    """生成模拟汇率数据"""
    return {
        'usd_cny': {
            'latest_rate': 7.25,
            'change': -0.15
        },
        'data_source': 'mock'
    }


def _get_mock_pmi_data() -> Dict[str, Any]:
    """生成模拟PMI数据"""
    return {
        'latest_value': 50.8,     # PMI指数
        'data_source': 'mock'
    }


def _get_mock_money_supply_data() -> Dict[str, Any]:
    """生成模拟货币供应量数据"""
    return {
        'latest_m2': 10.1,        # M2同比增长%
        'latest_m1': 5.8,         # M1同比增长%
        'data_source': 'mock'
    }
